# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Sensitive files
config/credentials.py
config/local_config.json
config/.encryption_key
*.bak
test_mt5_connections.py

# Data files
data/
*.csv
*.json
*.pkl
*.h5
*.model

# Model files
models/saved_models/
*.pt
*.pth
*.hdf5

# System files
.DS_Store
Thumbs.db

# Monitoring logs
monitoring/logs/
*.json.bak
