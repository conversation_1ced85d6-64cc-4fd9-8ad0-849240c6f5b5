@echo off
REM Monitor Training Progress Script
REM This script monitors model training progress in real-time

echo ========================================
echo MT5 Trading Bot Training Monitor
echo ========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists and activate it
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

REM Check if log file exists
if not exist "model_training.log" (
    echo WARNING: model_training.log not found
    echo Starting training monitor anyway...
)

REM Start monitoring
echo.
echo Starting real-time training progress monitor...
echo Press Ctrl+C to stop monitoring
echo.
echo Monitoring will update every 30 seconds
echo Visualizations will be saved to 'visualizations' directory
echo.

python visualize_training.py --log_file model_training.log --monitor --interval 30

REM Handle exit
echo.
echo Training monitor stopped.
pause
