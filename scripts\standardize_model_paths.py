#!/usr/bin/env python
"""
Script to standardize model storage paths according to the requirements:

Terminal 1: ARIMA Models
models/saved_models/arima/{symbol}_{timeframe}_arima.pkl
models/saved_models/arima/{symbol}_{timeframe}_arima_metrics.json

Terminal 2: LSTM Models
models/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
models/saved_models/lstm/{symbol}_{timeframe}_lstm_metrics.json

Terminal 3: TFT Models
models/saved_models/tft/{symbol}_{timeframe}_tft.pth
models/saved_models/tft/{symbol}_{timeframe}_tft_metrics.json

Terminal 4: LSTM + ARIMA Ensemble
models/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima.pkl
models/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima_metrics.json

Terminal 5: TFT + ARIMA Ensemble
models/saved_models/ensemble/{symbol}_{timeframe}_tft_arima.pkl
models/saved_models/ensemble/{symbol}_{timeframe}_tft_arima_metrics.json
"""

import sys
import logging
from pathlib import Path
import shutil
import json

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('model_path_standardizer')

# Model path mappings according to requirements
TERMINAL_MODEL_MAPPING = {
    "1": "arima",
    "2": "lstm",
    "3": "tft",
    "4": "lstm_arima",
    "5": "tft_arima"
}

MODEL_EXTENSIONS = {
    "arima": ".pkl",
    "lstm": ".pt",
    "tft": ".pt",
    "gru": ".pt",
    "transformer": ".pt",
    "xgboost": ".json",
    "lightgbm": ".txt",
    "lstm_arima": ".pkl",
    "tft_arima": ".pkl"
}

TIMEFRAMES = ["M5", "M15", "M30", "H1", "H4"]
SYMBOL = "BTCUSD.a"

def create_directory_structure():
    """Create the standardized directory structure."""
    base_path = Path("models/saved_models")

    directories = [
        base_path / "arima",
        base_path / "lstm",
        base_path / "gru",
        base_path / "transformer",
        base_path / "tft",
        base_path / "xgboost",
        base_path / "lightgbm",
        base_path / "ensemble"
    ]

    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def get_standardized_model_path(terminal_id: str, timeframe: str) -> tuple:
    """
    Get standardized model path for a terminal and timeframe.

    Args:
        terminal_id: Terminal ID (1-5)
        timeframe: Timeframe (M5, M15, M30, H1, H4)

    Returns:
        Tuple of (model_path, metrics_path)
    """
    model_type = TERMINAL_MODEL_MAPPING[terminal_id]
    extension = MODEL_EXTENSIONS[model_type]

    # Determine base directory
    if model_type in ["lstm_arima", "tft_arima"]:
        base_dir = Path("models/saved_models/ensemble")
    else:
        base_dir = Path("models/saved_models") / model_type

    # Create file names
    model_filename = f"{SYMBOL}_{timeframe}_{model_type}{extension}"
    metrics_filename = f"{SYMBOL}_{timeframe}_{model_type}_metrics.json"

    model_path = base_dir / model_filename
    metrics_path = base_dir / metrics_filename

    return model_path, metrics_path

def migrate_existing_models():
    """Migrate existing models to standardized paths."""
    logger.info("Starting model migration...")

    # Look for existing model files in various locations
    search_paths = [
        Path("models"),
        Path("models/saved"),
        Path("models/terminal_1"),
        Path("models/terminal_2"),
        Path("models/terminal_3"),
        Path("models/terminal_4"),
        Path("models/terminal_5")
    ]

    migrated_count = 0

    for search_path in search_paths:
        if not search_path.exists():
            continue

        logger.info(f"Searching for models in: {search_path}")

        # Find model files
        for model_file in search_path.rglob("*"):
            if model_file.is_file() and any(ext in model_file.suffix for ext in ['.h5', '.pkl', '.pth', '.json']):
                # Try to determine terminal and timeframe from filename or path
                terminal_id, timeframe = extract_terminal_timeframe(model_file)

                if terminal_id and timeframe:
                    # Get standardized path
                    new_model_path, new_metrics_path = get_standardized_model_path(terminal_id, timeframe)

                    # Determine if this is a model or metrics file
                    if 'metrics' in model_file.name.lower():
                        target_path = new_metrics_path
                    else:
                        target_path = new_model_path

                    # Migrate file
                    if model_file != target_path:
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(model_file, target_path)
                        logger.info(f"Migrated: {model_file} -> {target_path}")
                        migrated_count += 1

    logger.info(f"Migration complete. Migrated {migrated_count} files.")

def extract_terminal_timeframe(file_path: Path) -> tuple:
    """
    Extract terminal ID and timeframe from file path or name.

    Args:
        file_path: Path to the model file

    Returns:
        Tuple of (terminal_id, timeframe) or (None, None) if not found
    """
    file_str = str(file_path).lower()

    # Extract terminal ID
    terminal_id = None
    for tid in ["1", "2", "3", "4", "5"]:
        if f"terminal_{tid}" in file_str or f"terminal{tid}" in file_str:
            terminal_id = tid
            break

    # Extract timeframe
    timeframe = None
    for tf in TIMEFRAMES:
        if tf.lower() in file_str:
            timeframe = tf
            break

    return terminal_id, timeframe

def create_model_config_files():
    """Create model configuration files for each terminal/timeframe combination."""
    logger.info("Creating model configuration files...")

    for terminal_id in TERMINAL_MODEL_MAPPING.keys():
        model_type = TERMINAL_MODEL_MAPPING[terminal_id]

        for timeframe in TIMEFRAMES:
            model_path, metrics_path = get_standardized_model_path(terminal_id, timeframe)

            # Create model configuration
            config = {
                "terminal_id": terminal_id,
                "model_type": model_type,
                "timeframe": timeframe,
                "symbol": SYMBOL,
                "model_path": str(model_path),
                "metrics_path": str(metrics_path),
                "created_at": "2024-01-01T00:00:00",
                "last_updated": "2024-01-01T00:00:00"
            }

            # Save configuration
            config_path = model_path.parent / f"{SYMBOL}_{timeframe}_{model_type}_config.json"
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)

            logger.info(f"Created config: {config_path}")

def validate_directory_structure():
    """Validate that the directory structure is correct."""
    logger.info("Validating directory structure...")

    required_dirs = [
        "models/saved_models/arima",
        "models/saved_models/lstm",
        "models/saved_models/gru",
        "models/saved_models/transformer",
        "models/saved_models/tft",
        "models/saved_models/xgboost",
        "models/saved_models/lightgbm",
        "models/saved_models/ensemble"
    ]

    all_valid = True
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            logger.error(f"Missing directory: {dir_path}")
            all_valid = False
        else:
            logger.info(f"✓ Directory exists: {dir_path}")

    if all_valid:
        logger.info("✓ Directory structure validation passed")
    else:
        logger.error("✗ Directory structure validation failed")

    return all_valid

def main():
    """Main function to standardize model paths."""
    logger.info("Starting model path standardization...")

    try:
        # Step 1: Create directory structure
        create_directory_structure()

        # Step 2: Migrate existing models
        migrate_existing_models()

        # Step 3: Create model configuration files
        create_model_config_files()

        # Step 4: Validate structure
        if validate_directory_structure():
            logger.info("✓ Model path standardization completed successfully")
        else:
            logger.error("✗ Model path standardization completed with errors")
            return 1

        return 0

    except Exception as e:
        logger.error(f"Error during model path standardization: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

