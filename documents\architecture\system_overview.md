# Multi-Terminal Trading System Architecture

## System Overview

The multi-terminal algorithmic trading bot is a sophisticated system that operates **5 MetaTrader 5 terminals simultaneously**, each utilizing different machine learning models to generate trading signals for BTCUSD across multiple timeframes (M5, M15, M30, H1, H4).

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    Multi-Terminal Algorithmic Trading System                     │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Terminal 1    │  │   Terminal 2    │  │   Terminal 3    │                  │
│  │   LSTM Primary  │  │   GRU Primary   │  │   TFT Primary   │                  │
│  │   + 8 Models    │  │   + 8 Models    │  │   + 8 Models    │                  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                  │
│  ┌─────────────────┐  ┌─────────────────┐                                       │
│  │   Terminal 4    │  │   Terminal 5    │                                       │
│  │  XGBoost Primary│  │ LightGBM Primary│                                       │
│  │   + 8 Models    │  │   + 8 Models    │                                       │
│  └─────────────────┘  └─────────────────┘                                       │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              Core Infrastructure                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Data Management │  │ Model Training  │  │ Trading Engine  │                  │
│  │ • MT5 API       │  │ • PyTorch       │  │ • Signal Gen    │                  │
│  │ • Real-time     │  │ • GPU RTX 4070  │  │ • Risk Mgmt     │                  │
│  │ • Multi-TF      │  │ • 9 Model Types │  │ • Execution     │                  │
│  │ • 5 Terminals   │  │ • Ensemble      │  │ • Monitoring    │                  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            Support Systems                                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Memory Manager  │  │ Error Handling  │  │ Monitoring      │                  │
│  │ • Adaptive      │  │ • Circuit Break │  │ • Performance   │                  │
│  │ • Multi-level   │  │ • Recovery      │  │ • Visualization │                  │
│  │ • GPU Aware     │  │ • Graceful Deg  │  │ • Real-time     │                  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Terminal Configuration

Each terminal operates independently with its own primary model while having access to all 9 models:

| Terminal | Primary Model | Login | Path | Status |
|----------|---------------|-------|------|--------|
| Terminal 1 | LSTM | 61336224 | `C:/Users/<USER>/Desktop/MT5 IC 01/` | ✅ Active |
| Terminal 2 | GRU | 61336225 | `C:/Users/<USER>/Desktop/MT5 IC 02/` | ✅ Active |
| Terminal 3 | TFT | 61336226 | `C:/Users/<USER>/Desktop/MT5 IC 03/` | ✅ Active |
| Terminal 4 | XGBoost | 61336227 | `C:/Users/<USER>/Desktop/MT5 Pepper 02/` | ✅ Active |
| Terminal 5 | LightGBM | 61336228 | `C:/Users/<USER>/Desktop/MT5 Pepper 03/` | ✅ Active |

## Core Components

### 1. Configuration Management (`config/`)
- **ConfigurationManager**: Centralized multi-terminal configuration handler
- **Consolidated Config**: Typed configuration objects with dataclass validation
- **MT5 Terminals**: 5 terminal configurations with credentials and paths
- **Model Configs**: Individual configuration for all 9 model types
- **Validation**: Comprehensive configuration validation and error checking

### 2. Data Management (`data/` & `trading/data_collector.py`)
- **DataCollector**: Real-time and historical data collection from all 5 MT5 terminals
- **DataManager**: Organized data storage and retrieval by terminal/timeframe
- **DataPreprocessor**: Advanced data cleaning, feature engineering, and technical indicators
- **Storage Structure**: `data/terminal_X/timeframe/` organized hierarchy
- **Multi-timeframe Support**: M5, M15, M30, H1, H4 data collection and processing

### 3. Model Layer (`models/`)
- **BaseModel**: Abstract base class with standardized model interface
- **LSTM/GRU Models**: PyTorch-based recurrent neural networks with GPU acceleration
- **Transformer Model**: PyTorch-based attention mechanism model
- **TFT Model**: Temporal Fusion Transformer using PyTorch Forecasting
- **XGBoost/LightGBM**: Tree-based gradient boosting models with hyperparameter optimization
- **ARIMA Model**: Statistical time series model using statsmodels with auto-parameter selection
- **Ensemble Models**: LSTM-ARIMA and TFT-ARIMA hybrid combinations
- **Model Manager**: Centralized model loading, validation, and lifecycle management
- **Storage Structure**: `models/terminal_X/timeframe/model_name/` organized hierarchy

### 4. Trading Engine (`trading/`)
- **TradingBot**: Main trading bot orchestrator with multi-terminal coordination
- **MT5ConnectionManager**: Multi-terminal connection management with failover support
- **SignalGenerator**: ML model prediction aggregation and ensemble logic
- **TradeExecutor**: Order placement and management across all 5 terminals
- **TradingStrategy**: Advanced risk management and position sizing algorithms
- **DataCollector**: Real-time market data collection with enhanced error handling

### 5. Utilities (`utils/`)
- **EnhancedMemoryManager**: Advanced memory management with adaptive thresholds and GPU awareness
- **EnhancedErrorHandler**: Comprehensive error handling, tracking, and recovery mechanisms
- **EnhancedCircuitBreaker**: Advanced circuit breaker with multiple failure strategies
- **ModelManager**: Model loading, validation, and health checking across all terminals
- **ThreadManager**: Multi-threading coordination and safety for multi-terminal operations
- **IntelligentCache**: Performance-optimized caching system with TTL and memory awareness
- **GracefulDegradation**: System degradation and feature toggle management
- **DataPreprocessor**: Advanced data cleaning, feature engineering, and technical indicators
- **Common**: Shared utility functions and terminal ID normalization helpers

### 6. Monitoring (`monitoring/`)
- **PerformanceMonitor**: Multi-terminal trading performance tracking and analysis
- **ProgressVisualizer**: Real-time training and trading progress visualization with matplotlib
- **Metrics Collection**: Comprehensive system, trading, and model performance metrics
- **Report Generation**: Automated performance reports with charts and statistics

## Multi-Terminal Data Flow

### 1. Data Collection Flow (5 Terminals)
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│ Terminal 1  │  │ Terminal 2  │  │ Terminal 3  │  │ Terminal 4  │  │ Terminal 5  │
│ (IC 01)     │  │ (IC 02)     │  │ (IC 03)     │  │ (Pepper 02) │  │ (Pepper 03) │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
       │                │                │                │                │
       └────────────────┼────────────────┼────────────────┼────────────────┘
                        │                │                │
                   ┌─────────────────────────────────────────────┐
                   │         DataCollector                       │
                   │    • Real-time tick data                    │
                   │    • OHLCV bars (M5, M15, M30, H1, H4)     │
                   │    • Account information                    │
                   └─────────────────────────────────────────────┘
                                        │
                   ┌─────────────────────────────────────────────┐
                   │         DataPreprocessor                    │
                   │    • Technical indicators                   │
                   │    • Feature engineering                    │
                   │    • Data validation                        │
                   └─────────────────────────────────────────────┘
                                        │
                   ┌─────────────────────────────────────────────┐
                   │    Organized Storage Structure              │
                   │    data/terminal_X/timeframe/               │
                   └─────────────────────────────────────────────┘
```

### 2. Model Training Flow (Multi-Timeframe)
```
Processed Data → ModelTrainer → GPU Acceleration → Trained Models → Validation → Storage
     │              │              (RTX 4070)           │             │           │
     │              │                                   │             │           │
   M5, M15,      9 Model Types                    Performance      Model      models/
   M30, H1,      (LSTM, GRU,                      Metrics        Validation   terminal_X/
   H4 Data       TFT, etc.)                                                   timeframe/
```

### 3. Real-Time Trading Flow (5 Terminals)
```
Live Market Data → Model Predictions → Signal Generation → Risk Management → Trade Execution
       │                   │                  │                   │                │
   5 Terminals         9 Models/Terminal    Ensemble Logic    Position Sizing   5 Terminals
   Real-time           Parallel Inference   Confidence Check   Stop Loss/TP     Simultaneous
   Collection                                                                   Execution
```

## Key Design Patterns

### 1. Factory Pattern
- **ModelManager**: Dynamic model creation based on configuration
- **ConfigurationManager**: Typed configuration object instantiation
- **DataCollector**: Terminal-specific data collector creation

### 2. Observer Pattern
- **PerformanceMonitor**: Real-time performance tracking across all terminals
- **ProgressVisualizer**: Event-driven visualization updates
- **EnhancedErrorHandler**: Error event tracking and notification

### 3. Circuit Breaker Pattern
- **EnhancedCircuitBreaker**: Advanced failure detection and recovery
- **GracefulDegradation**: System-wide degradation management
- **MT5ConnectionManager**: Connection failure handling and failover

### 4. Adapter Pattern
- **BaseModel**: Standardized model interface across all 9 model types
- **DataPreprocessor**: Unified data format standardization
- **ModelAdapter**: Model input/output adaptation layer

### 5. Singleton Pattern
- **EnhancedMemoryManager**: Global memory management instance
- **ConfigurationManager**: Centralized configuration access
- **IntelligentCache**: System-wide caching coordination

## Advanced Features

### 1. Multi-Terminal Architecture
- **5 Independent Terminals**: Parallel operation with individual model assignments
- **Algorithmic Trading Enabled**: All terminals configured for automated trading
- **Failover Support**: Automatic terminal recovery and reconnection

### 2. Advanced ML Pipeline
- **9 Model Types**: LSTM, GRU, Transformer, TFT, XGBoost, LightGBM, ARIMA, Ensembles
- **GPU Acceleration**: NVIDIA RTX 4070 with 12.88GB VRAM utilization
- **Multi-Timeframe Training**: M5, M15, M30, H1, H4 simultaneous support

### 3. Intelligent Resource Management
- **Adaptive Memory Management**: Dynamic threshold adjustment based on system load
- **Circuit Breaker Protection**: Multi-level failure detection and recovery
- **Intelligent Caching**: TTL-based caching with memory-aware cleanup

### 4. Real-Time Monitoring
- **Performance Visualization**: Real-time charts and progress tracking
- **Comprehensive Metrics**: System, trading, and model performance monitoring
- **Automated Reporting**: Performance reports with statistical analysis
