"""
Metrics utility module for model evaluation.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Union, Tuple, Any, Optional
import logging

logger = logging.getLogger(__name__)

def calculate_metrics(y_true: np.n<PERSON><PERSON>, y_pred: np.ndar<PERSON>, metrics: List[str] = None) -> Dict[str, float]:
    """
    Calculate evaluation metrics for model predictions.
    
    Args:
        y_true: Ground truth values
        y_pred: Predicted values
        metrics: List of metrics to calculate. If None, calculates all available metrics.
                Available metrics: 'mse', 'rmse', 'mae', 'mape', 'r2'
    
    Returns:
        Dictionary of metric names and values
    """
    if metrics is None:
        metrics = ['mse', 'rmse', 'mae', 'mape', 'r2']
    
    # Ensure inputs are numpy arrays
    y_true = np.asarray(y_true).flatten()
    y_pred = np.asarray(y_pred).flatten()
    
    # Check for NaN or infinite values
    if np.isnan(y_true).any() or np.isnan(y_pred).any():
        logger.warning("NaN values found in inputs to calculate_metrics")
        # Remove NaN values
        mask = ~(np.isnan(y_true) | np.isnan(y_pred))
        y_true = y_true[mask]
        y_pred = y_pred[mask]
    
    if np.isinf(y_true).any() or np.isinf(y_pred).any():
        logger.warning("Infinite values found in inputs to calculate_metrics")
        # Remove infinite values
        mask = ~(np.isinf(y_true) | np.isinf(y_pred))
        y_true = y_true[mask]
        y_pred = y_pred[mask]
    
    # Check if we have any valid data left
    if len(y_true) == 0 or len(y_pred) == 0:
        logger.error("No valid data points after removing NaN/Inf values")
        return {metric: float('nan') for metric in metrics}
    
    results = {}
    
    # Calculate requested metrics
    for metric in metrics:
        if metric.lower() == 'mse':
            results['mse'] = np.mean((y_true - y_pred) ** 2)
        elif metric.lower() == 'rmse':
            results['rmse'] = np.sqrt(np.mean((y_true - y_pred) ** 2))
        elif metric.lower() == 'mae':
            results['mae'] = np.mean(np.abs(y_true - y_pred))
        elif metric.lower() == 'mape':
            # Avoid division by zero
            mask = y_true != 0
            if np.sum(mask) > 0:
                results['mape'] = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
            else:
                results['mape'] = float('nan')
                logger.warning("Cannot calculate MAPE: division by zero")
        elif metric.lower() == 'r2':
            # R-squared calculation
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            ss_res = np.sum((y_true - y_pred) ** 2)
            if ss_tot > 0:
                results['r2'] = 1 - (ss_res / ss_tot)
            else:
                results['r2'] = float('nan')
                logger.warning("Cannot calculate R2: variance in true values is zero")
        else:
            logger.warning(f"Unknown metric: {metric}")
    
    return results

def calculate_classification_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                                    y_prob: Optional[np.ndarray] = None,
                                    metrics: List[str] = None) -> Dict[str, float]:
    """
    Calculate evaluation metrics for classification models.
    
    Args:
        y_true: Ground truth labels
        y_pred: Predicted labels
        y_prob: Predicted probabilities (for metrics like AUC)
        metrics: List of metrics to calculate. If None, calculates all available metrics.
                Available metrics: 'accuracy', 'precision', 'recall', 'f1', 'auc'
    
    Returns:
        Dictionary of metric names and values
    """
    try:
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
    except ImportError:
        logger.error("scikit-learn is required for classification metrics")
        return {}
    
    if metrics is None:
        metrics = ['accuracy', 'precision', 'recall', 'f1']
        if y_prob is not None:
            metrics.append('auc')
    
    # Ensure inputs are numpy arrays
    y_true = np.asarray(y_true).flatten()
    y_pred = np.asarray(y_pred).flatten()
    
    results = {}
    
    # Calculate requested metrics
    for metric in metrics:
        try:
            if metric.lower() == 'accuracy':
                results['accuracy'] = accuracy_score(y_true, y_pred)
            elif metric.lower() == 'precision':
                results['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            elif metric.lower() == 'recall':
                results['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            elif metric.lower() == 'f1':
                results['f1'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
            elif metric.lower() == 'auc' and y_prob is not None:
                # For binary classification
                if len(np.unique(y_true)) == 2:
                    results['auc'] = roc_auc_score(y_true, y_prob)
                else:
                    # For multi-class, we need one-hot encoded y_true and y_prob for each class
                    logger.warning("AUC for multi-class not implemented")
                    results['auc'] = float('nan')
            else:
                logger.warning(f"Unknown or unavailable metric: {metric}")
        except Exception as e:
            logger.error(f"Error calculating {metric}: {str(e)}")
            results[metric] = float('nan')
    
    return results
