"""
XGBoost model implementation for time series prediction.
"""
# Standard library imports
import logging
from typing import Dict, Optional, Any
import os
from pathlib import Path

# Third-party imports
import numpy as np
import xgboost as xgb

# Local imports
from .base_model import BaseModel

logger = logging.getLogger(__name__)

class XGBoostModel(BaseModel):
    def __init__(self, config: Dict[str, Any]):
        """Initialize XGBoost model with a configuration dictionary."""
        super().__init__(config)

        # Initialize model parameters from config using dictionary access
        # Use .get() with defaults for robustness
        self.max_depth = self.config.get('max_depth', 6) # Default XGBoost value
        self.learning_rate = self.config.get('learning_rate', 0.1) # Common default
        self.n_estimators = self.config.get('n_estimators', 100)
        self.subsample = self.config.get('subsample', 1.0)
        self.colsample_bytree = self.config.get('colsample_bytree', 1.0)
        self.min_child_weight = self.config.get('min_child_weight', 1)
        self.gamma = self.config.get('gamma', 0)

        # Model is initialized in build() method
        self.model = None

    def build(self) -> None:
        """Build the XGBoost model architecture using parameters from config."""
        try:
            # Check for GPU support
            use_gpu = self.config.get('use_gpu', False)
            tree_method = 'hist'  # Default CPU method
            device = 'cpu'

            if use_gpu:
                try:
                    # Test if GPU is available by checking CUDA
                    import torch
                    if torch.cuda.is_available():
                        tree_method = 'hist'  # Updated from deprecated 'gpu_hist'
                        device = 'cuda'
                        logger.info(f"XGBoost GPU support enabled for '{self.model_name}' using tree_method='hist' with device='cuda'")
                    else:
                        logger.warning(f"CUDA not available for XGBoost '{self.model_name}', using CPU")
                        tree_method = 'hist'
                        device = 'cpu'
                except Exception as e:
                    logger.warning(f"GPU not available for XGBoost '{self.model_name}', falling back to CPU: {e}")
                    tree_method = 'hist'
                    device = 'cpu'

            self.model = xgb.XGBRegressor(
                max_depth=self.max_depth,
                learning_rate=self.learning_rate,
                n_estimators=self.n_estimators,
                subsample=self.subsample,
                colsample_bytree=self.colsample_bytree,
                min_child_weight=self.min_child_weight,
                gamma=self.gamma,
                objective=self.config.get('objective', 'reg:squarederror'),
                random_state=self.config.get('random_state', 42),
                tree_method=tree_method,
                device=device,
                n_jobs=self.config.get('n_jobs', -1)  # Use all available cores
            )
            logger.info(f"XGBoost model ('{self.model_name}') built successfully with params: {self.model.get_params()}")

        except Exception as e:
            logger.error(f"Error building XGBoost model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def train(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[tuple] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Train the XGBoost model."""
        try:
            # Handle epochs parameter override
            epochs = kwargs.get('epochs')
            if epochs is not None:
                logger.info(f"Overriding n_estimators with epochs parameter: {epochs}")
                self.n_estimators = epochs
                # Rebuild model with new n_estimators
                self.build()

            # Input validation using config dictionary access
            expected_input_dim = self.config.get('input_dim')
            if expected_input_dim is not None and X.shape[1] != expected_input_dim:
                # Check if model uses flattened sequences
                uses_flattened_sequence = self.config.get('uses_flattened_sequence', False)
                if uses_flattened_sequence:
                    # For flattened sequences, the input dimension is sequence_length * feature_dim
                    sequence_length = self.config.get('sequence_length', 60)
                    # Calculate feature dimension for validation
                    _ = expected_input_dim // sequence_length
                    if X.shape[1] == expected_input_dim:
                        # Input is already correctly shaped
                        pass
                    else:
                        raise ValueError(
                            f"Input shape mismatch for model '{self.model_name}'. Expected (batch_size, {expected_input_dim}), "
                            f"got {X.shape}"
                        )
                else:
                    raise ValueError(
                        f"Input shape mismatch for model '{self.model_name}'. Expected (batch_size, {expected_input_dim}), "
                        f"got {X.shape}"
                    )

            expected_output_dim = self.config.get('output_dim', 1) # Default to 1 if not specified
            # XGBoost typically handles 1D target, check if y needs reshaping or if output_dim > 1 requires MultiOutputRegressor
            if y.ndim == 1:
                y_processed = y
            elif y.ndim == 2 and y.shape[1] == 1:
                 y_processed = y.ravel()
            elif y.ndim == 2 and y.shape[1] == expected_output_dim and expected_output_dim > 1:
                 # Need MultiOutputRegressor wrapper or specific XGBoost setup
                 logger.warning(f"Target y has shape {y.shape} with output_dim={expected_output_dim}. Standard XGBRegressor expects 1D target. Consider MultiOutput wrapper.")
                 # For now, attempt training on the first column if allowed, or raise error
                 # Option 1: Train on first column (if appropriate)
                 # y_processed = y[:, 0]
                 # Option 2: Raise error
                 raise ValueError(f"XGBoostModel currently handles 1D target, but got shape {y.shape} and output_dim {expected_output_dim}")
            else:
                 raise ValueError(f"Unsupported target shape {y.shape} for XGBoostModel.")

            # Build model if not already built
            if self.model is None:
                logger.info(f"Model '{self.model_name}' not built. Building now...")
                self.build()
            if self.model is None: # Check again if build failed somehow
                 raise RuntimeError(f"Failed to build model '{self.model_name}' before training.")

            # Setup evaluation data
            eval_set = None
            if validation_data:
                X_val, y_val = validation_data
                # Ensure validation target is also processed correctly (e.g., ravel if needed)
                if y_val.ndim == 2 and y_val.shape[1] == 1:
                    y_val_processed = y_val.ravel()
                elif y_val.ndim == 1:
                     y_val_processed = y_val
                else:
                     # Handle multi-output validation target based on model capability
                     raise ValueError(f"Unsupported validation target shape {y_val.shape}")
                eval_set = [(X_val, y_val_processed)]

            # Extract training-specific args from config or kwargs
            fit_params = {
                'eval_set': eval_set,
                # 'eval_metric': eval_metric,  # This should be set during model initialization
                # 'early_stopping_rounds': self.config.get('early_stopping_rounds', 10), # Add early stopping - not supported in this version
                'verbose': self.config.get('verbose', False) # Control verbosity
            }

            # Filter out parameters that XGBoost doesn't accept
            xgb_invalid_params = ['epochs', 'batch_size', 'patience']
            filtered_kwargs = {k: v for k, v in kwargs.items() if k not in xgb_invalid_params}
            fit_params.update(filtered_kwargs) # Allow overriding via filtered kwargs

            logger.info(f"Starting training for XGBoost model '{self.model_name}'...")
            # Train model
            self.model.fit(X, y_processed, **fit_params)

            # Store training history
            evals_result = self.model.evals_result()
            self.history = evals_result # Store the whole dictionary
            logger.info(f"XGBoost model '{self.model_name}' trained successfully. Final validation metrics: {evals_result.get('validation_0', 'N/A')}")
            return self.history

        except Exception as e:
            logger.error(f"Error training XGBoost model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def predict(self, X: np.ndarray) -> np.ndarray:
        try:
            # Input validation
            expected_input_dim = self.config.get('input_dim')
            if expected_input_dim is not None and X.shape[1] != expected_input_dim:
                # For XGBoost trained with flattened sequences, we need to check if the input matches
                # the expected flattened dimension (sequence_length * input_dim)
                sequence_length = self.config.get('sequence_length', 60)
                flattened_dim = sequence_length * expected_input_dim

                # Try to adapt the input using ModelInputAdapter
                try:
                    # If X is 3D, flatten it to 2D
                    if len(X.shape) == 3:
                        batch_size, seq_len, features = X.shape
                        X_flat = X.reshape(batch_size, seq_len * features)

                        # If the flattened shape is still not correct, pad or truncate
                        if X_flat.shape[1] < flattened_dim:
                            # Pad with zeros
                            padding = np.zeros((batch_size, flattened_dim - X_flat.shape[1]))
                            X = np.concatenate([X_flat, padding], axis=1)
                        elif X_flat.shape[1] > flattened_dim:
                            # Truncate
                            X = X_flat[:, :flattened_dim]
                        else:
                            X = X_flat
                    elif len(X.shape) == 2 and X.shape[1] != flattened_dim:
                        # If X is already 2D but wrong size, pad or truncate
                        batch_size = X.shape[0]
                        if X.shape[1] < flattened_dim:
                            # Pad with zeros
                            padding = np.zeros((batch_size, flattened_dim - X.shape[1]))
                            X = np.concatenate([X, padding], axis=1)
                        elif X.shape[1] > flattened_dim:
                            # Truncate
                            X = X[:, :flattened_dim]
                except Exception as adapt_error:
                    logger.error(f"Error adapting input for XGBoost model '{self.model_name}': {str(adapt_error)}")
                    raise ValueError(
                        f"Input shape mismatch for prediction. Expected (batch_size, {expected_input_dim}) or "
                        f"(batch_size, {flattened_dim}), got {X.shape}"
                    )

            # Load model if not already loaded (or maybe rely on caller to load?)
            if self.model is None:
                logger.warning(f"Model '{self.model_name}' is not loaded. Attempting to load from {self.model_path}...")
                # Assuming self.model_path is set correctly by BaseModel __init__
                # The load method signature changed in BaseModel, need to adapt
                # We need the path_prefix used to save, which isn't available here.
                # Best practice: Ensure model is loaded before calling predict.
                # For now, raise error if not loaded.
                raise RuntimeError(f"Model '{self.model_name}' must be loaded before calling predict.")
                # Alternative: Try loading based on self.model_path (may not be consistent if save used path_prefix)
                # self.load_model(self.model_path)

            return self.model.predict(X)

        except Exception as e:
            logger.error(f"Error making predictions with XGBoost model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def save_model(self, path: str) -> None:
        """Save the XGBoost model to the specified full path."""
        try:
            if self.model is None:
                raise ValueError("Model has not been built or trained yet. Cannot save.")

            # Ensure directory exists
            Path(path).parent.mkdir(parents=True, exist_ok=True)

            self.model.save_model(path)
            logger.info(f"XGBoost model '{self.model_name}' saved to {path}")

        except Exception as e:
            logger.error(f"Error saving XGBoost model '{self.model_name}' to {path}: {str(e)}", exc_info=True)
            raise

    def load_model(self, path: str) -> None:
        """Load the XGBoost model from the specified full path. Sets self.model."""
        try:
            if not os.path.exists(path):
                raise FileNotFoundError(f"XGBoost model file not found: {path}")

            # Build the model structure first (optional but safer if params change)
            self.build() # Re-build with current config params before loading weights

            self.model.load_model(path)
            logger.info(f"XGBoost model '{self.model_name}' loaded from {path}")

        except Exception as e:
            logger.error(f"Error loading XGBoost model '{self.model_name}' from {path}: {str(e)}", exc_info=True)
            raise

    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """
        Get feature importance scores.

        Returns:
            Optional[Dict[str, float]]: Dictionary mapping feature names to importance scores, or None.
        """
        try:
            if self.model is None or not hasattr(self.model, 'feature_importances_'):
                logger.warning(f"Model '{self.model_name}' not trained yet or doesn't have feature importances.")
                return None

            importance_scores = self.model.feature_importances_
            # Get feature names from config using dictionary access
            feature_names = self.config.get('FEATURE_COLUMNS')

            if feature_names is None:
                logger.warning("FEATURE_COLUMNS not found in config. Cannot map importance scores.")
                # Return raw scores if names aren't available
                return {f"feature_{i}": score for i, score in enumerate(importance_scores)}
            elif len(feature_names) != len(importance_scores):
                 logger.warning(f"Mismatch between number of features in config ({len(feature_names)}) and importance scores ({len(importance_scores)}). Returning raw scores.")
                 return {f"feature_{i}": score for i, score in enumerate(importance_scores)}
            else:
                 return dict(zip(feature_names, importance_scores))

        except Exception as e:
            logger.error(f"Error getting feature importance for '{self.model_name}': {str(e)}", exc_info=True)
            raise # Or return None

    def get_model_params(self) -> Dict:
        """Get current model parameters."""
        try:
            if self.model is None:
                # Optionally build the model if not built yet to get default params
                logger.warning(f"Model '{self.model_name}' not built yet. Building to get default parameters.")
                self.build()
                if self.model is None: # Check if build failed
                     raise ValueError("Model cannot be built to get parameters.")

            return self.model.get_params()

        except Exception as e:
            logger.error(f"Error getting model parameters for '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def set_model_params(self, params: Dict) -> None:
        """Set model parameters."""
        try:
            if self.model is None:
                # Build the model first if setting parameters before training
                logger.warning(f"Model '{self.model_name}' not built yet. Building before setting parameters.")
                self.build()
                if self.model is None: # Check if build failed
                     raise ValueError("Model cannot be built to set parameters.")

            # Update internal parameter attributes as well for consistency if needed
            # e.g., self.max_depth = params.get('max_depth', self.max_depth)
            # ... (update all relevant attributes)

            self.model.set_params(**params)
            logger.info(f"Successfully updated model parameters for '{self.model_name}'. Current params: {self.model.get_params()}")

        except Exception as e:
            logger.error(f"Error setting model parameters for '{self.model_name}': {str(e)}", exc_info=True)
            raise