# 🚀 HOW TO RUN AND <PERSON><PERSON><PERSON>OR THE MT5 TRADING BOT

## **📋 COMPREHENSIVE GUIDE: RUNNING THE BOT WITH TRAINED MODELS**

This guide provides complete instructions for running the trading bot using trained models and monitoring its performance in real-time.

---

## **🔧 PREREQUISITES**

### **1. Trained Models Required**
Ensure you have successfully trained models using:
```bash
python train_models.py --timeframe H4 --symbol BTCUSD.a
```

**Model Storage Locations:**
- `models/terminal_1/H4/` - H4 timeframe models
- `models/terminal_1/M30/` - M30 timeframe models
- `models/terminal_1/H1/` - H1 timeframe models

### **2. MT5 Terminal Setup**
- ✅ MetaTrader 5 installed and running
- ✅ Expert Advisors enabled
- ✅ Auto trading enabled
- ✅ Proper symbol configuration (BTCUSD.a)

### **3. Configuration Verification**
Check `config/config.json` for proper setup:
- Terminal IDs configured
- Model paths correct
- Trading parameters set

---

## **🚀 RUNNING THE BOT - MULTIPLE METHODS**

### **Method 1: Main Entry Point (RECOMMENDED)**
```bash
# Start the complete trading bot system
python main.py
```

**What this does:**
- Loads all trained models
- Initializes MT5 connections
- Starts trading strategies
- Activates monitoring systems
- Begins real-time trading

### **Method 2: Using Startup Scripts**
```bash
# Windows
run_trading_bot.bat

# Linux/Mac
./run_trading_bot.sh
```

### **Method 3: Specific Terminal**
```bash
# Run bot for specific terminal
python main.py --terminal 1
```

---

## **📊 MONITORING AND VISUALIZATION SYSTEMS**

### **🎯 COMPREHENSIVE MONITORING STARTUP**
```bash
# Start all monitoring systems (RECOMMENDED)
start_monitoring.bat
```

**This provides:**
1. **Real-time Performance Monitor** (updates every 30s)
2. **Training Progress Visualizer** (if training active)
3. **Static Dashboard Generation**
4. **Interactive Web Interface**

### **🔍 INDIVIDUAL MONITORING OPTIONS**

#### **A. Real-time Bot Performance Monitor**
```bash
# Monitor all terminals in real-time
python monitor_bot_performance.py --realtime --interval 30

# Monitor specific terminal
python monitor_bot_performance.py --realtime --terminal 1 --interval 30
```

#### **B. Training Progress Visualization**
```bash
# Monitor training progress (if training is active)
python visualize_training.py --log_file model_training.log --monitor --interval 30

# Generate static training reports
python visualize_training.py --log_file model_training.log
```

#### **C. Static Performance Dashboard**
```bash
# Generate comprehensive dashboard
python monitor_bot_performance.py

# Dashboard will be saved to: monitoring/dashboard/performance_dashboard.html
```

---

## **📈 MONITORING OUTPUTS AND LOCATIONS**

### **🎯 Key Monitoring Files**

#### **Performance Dashboards**
- **Main Dashboard**: `monitoring/dashboard/performance_dashboard.html`
- **Terminal-Specific**: `monitoring/dashboard/terminal_X_detailed.html`
- **Training Progress**: `visualizations/training_progress.html`

#### **Performance Data**
- **Metrics History**: `monitoring/terminal_X/metrics_history.json`
- **Trade History**: `monitoring/terminal_X/trade_history.json`
- **Model Performance**: `monitoring/terminal_X/model_performance.json`
- **Performance Reports**: `monitoring/terminal_X/performance_report.json`

#### **Visualization Files**
- **Training Charts**: `visualizations/training_progress.png`
- **Model Comparison**: `visualizations/model_comparison.png`
- **Progress Plots**: `monitoring/progress/progress_YYYYMMDD_HHMMSS.png`

---

## **🎛️ REAL-TIME MONITORING FEATURES**

### **📊 Performance Dashboard Components**

#### **1. Overview Dashboard**
- **Total P&L by Terminal**: Profit/Loss tracking
- **Win Rate Comparison**: Success rate across terminals
- **Active Trades**: Current trading activity
- **Daily Performance**: Time-series performance

#### **2. Detailed Terminal Analysis**
- **Cumulative P&L**: Running profit/loss
- **Trade Distribution**: Histogram of trade outcomes
- **Drawdown Analysis**: Risk assessment
- **Rolling Win Rate**: Performance trends

#### **3. Model Performance Tracking**
- **Individual Model Metrics**: Per-model statistics
- **Training Progress**: Loss curves and validation
- **Model Comparison**: Relative performance
- **Ensemble Performance**: Combined model results

#### **4. System Resource Monitoring**
- **Memory Usage**: RAM consumption
- **CPU Usage**: Processing load
- **GPU Usage**: Graphics card utilization
- **Trading Activity**: Real-time trade execution

---

## **🔧 TROUBLESHOOTING AND MONITORING**

### **🚨 Common Issues and Solutions**

#### **Issue 1: Bot Not Starting**
```bash
# Check logs
tail -f trading_bot.log

# Verify MT5 connection
python -c "import MetaTrader5 as mt5; print(mt5.initialize())"
```

#### **Issue 2: No Performance Data**
```bash
# Check monitoring directory
ls -la monitoring/terminal_*/

# Verify configuration
python -c "from config import config_manager; print(config_manager.get_monitoring_config())"
```

#### **Issue 3: Models Not Loading**
```bash
# Check model files
ls -la models/terminal_*/H4/

# Verify model integrity
python -c "from utils.model_manager import ModelManager; mm = ModelManager(); print(mm.list_available_models())"
```

---

## **📱 MONITORING WORKFLOW**

### **🎯 RECOMMENDED MONITORING SEQUENCE**

#### **Step 1: Start the Bot**
```bash
python main.py
```

#### **Step 2: Launch Monitoring**
```bash
start_monitoring.bat
```

#### **Step 3: Open Dashboards**
- **Main Dashboard**: Open `monitoring/dashboard/performance_dashboard.html`
- **Training Monitor**: Monitor console output for training progress
- **System Monitor**: Watch resource usage in real-time

#### **Step 4: Monitor Key Metrics**
- **P&L Tracking**: Watch cumulative profit/loss
- **Win Rate**: Monitor success percentage
- **Drawdown**: Track maximum loss periods
- **Trade Frequency**: Observe trading activity

#### **Step 5: Performance Analysis**
- **Daily Reviews**: Check end-of-day reports
- **Model Comparison**: Analyze individual model performance
- **Risk Assessment**: Monitor drawdown and volatility
- **System Health**: Check resource usage and errors

---

## **🎯 ADVANCED MONITORING FEATURES**

### **📊 Interactive Web Dashboard**
```bash
# Future feature - Web-based monitoring
python monitor_bot_performance.py --web --port 8080
```

### **📱 Real-time Alerts**
- **Performance Thresholds**: Automated alerts for significant changes
- **Error Notifications**: Immediate notification of system issues
- **Trade Confirmations**: Real-time trade execution updates

### **📈 Performance Analytics**
- **Sharpe Ratio Calculation**: Risk-adjusted returns
- **Maximum Drawdown**: Worst-case loss scenarios
- **Volatility Analysis**: Price movement patterns
- **Correlation Analysis**: Model performance relationships

---

## **🎉 SUMMARY**

**The MT5 Trading Bot provides comprehensive monitoring and visualization capabilities:**

✅ **Real-time Performance Tracking**: Live P&L, win rates, and trade activity
✅ **Interactive Dashboards**: Web-based visualization with drill-down capabilities
✅ **Training Progress Monitoring**: Live model training visualization
✅ **System Resource Monitoring**: CPU, memory, and GPU usage tracking
✅ **Automated Report Generation**: Daily, weekly, and monthly performance reports
✅ **Multi-Terminal Support**: Monitor multiple trading terminals simultaneously
✅ **Model Performance Analysis**: Individual and ensemble model tracking
✅ **Risk Management Monitoring**: Drawdown and volatility tracking

**Start monitoring your trading bot performance today with these comprehensive tools!**
