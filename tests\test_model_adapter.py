"""
Tests for the ModelInputAdapter class.
"""
import sys
from pathlib import Path
import numpy as np
import pytest

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

from model_adapter import ModelInputAdapter

def test_adapt_input_none():
    """Test that None input returns None."""
    result = ModelInputAdapter.adapt_input(None, (10, 5))
    assert result is None

def test_adapt_input_2d():
    """Test adapting 2D input to 3D."""
    X = np.random.random((3, 4))  # (batch_size, features)
    target_shape = (10, 4)  # (seq_len, features)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is not None
    assert result.shape == (3, 10, 4)  # (batch_size, target_seq_len, target_features)
    # Check that the original data is preserved at the end of the sequence
    np.testing.assert_array_equal(result[:, -1, :], X)

def test_adapt_input_truncate_sequence():
    """Test truncating sequence when input sequence is longer than target."""
    X = np.random.random((2, 15, 3))  # (batch_size, seq_len, features)
    target_shape = (10, 3)  # (target_seq_len, target_features)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is not None
    assert result.shape == (2, 10, 3)
    # Check that the last 10 elements of the sequence are preserved
    np.testing.assert_array_equal(result, X[:, -10:, :])

def test_adapt_input_pad_sequence():
    """Test padding sequence when input sequence is shorter than target."""
    X = np.random.random((2, 5, 3))  # (batch_size, seq_len, features)
    target_shape = (10, 3)  # (target_seq_len, target_features)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is not None
    assert result.shape == (2, 10, 3)
    # Check that the original data is preserved at the end of the sequence
    np.testing.assert_array_equal(result[:, -5:, :], X)
    # Check that the padding is zeros
    assert np.all(result[:, :5, :] == 0)

def test_adapt_input_truncate_features():
    """Test truncating features when input features are more than target."""
    X = np.random.random((2, 10, 5))  # (batch_size, seq_len, features)
    target_shape = (10, 3)  # (target_seq_len, target_features)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is not None
    assert result.shape == (2, 10, 3)
    # Check that the first 3 features are preserved
    np.testing.assert_array_equal(result, X[:, :, :3])

def test_adapt_input_pad_features():
    """Test padding features when input features are less than target."""
    X = np.random.random((2, 10, 2))  # (batch_size, seq_len, features)
    target_shape = (10, 5)  # (target_seq_len, target_features)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is not None
    assert result.shape == (2, 10, 5)
    # Check that the original features are preserved
    np.testing.assert_array_equal(result[:, :, :2], X)
    # Check that the padding is zeros
    assert np.all(result[:, :, 2:] == 0)

def test_adapt_input_both_dimensions():
    """Test adapting both sequence length and features."""
    X = np.random.random((2, 5, 2))  # (batch_size, seq_len, features)
    target_shape = (10, 5)  # (target_seq_len, target_features)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is not None
    assert result.shape == (2, 10, 5)
    # Check that the original data is preserved
    np.testing.assert_array_equal(result[:, -5:, :2], X)
    # Check that the padding is zeros
    assert np.all(result[:, :5, :] == 0)
    assert np.all(result[:, :, 2:] == 0)

def test_adapt_input_non_numpy():
    """Test adapting input that is not a numpy array."""
    X = [[1, 2, 3], [4, 5, 6]]  # List of lists
    target_shape = (5, 3)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is not None
    assert result.shape == (2, 5, 3)
    # Check that the original data is preserved at the end of the sequence
    np.testing.assert_array_equal(result[:, -1, :], np.array([[1, 2, 3], [4, 5, 6]]))

def test_adapt_input_invalid_shape():
    """Test that invalid input shape returns None."""
    X = np.random.random((2, 3, 4, 5))  # 4D array
    target_shape = (10, 5)
    
    result = ModelInputAdapter.adapt_input(X, target_shape)
    
    assert result is None
