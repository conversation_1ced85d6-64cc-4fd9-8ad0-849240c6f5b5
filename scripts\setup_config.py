"""
Setup script for the trading bot configuration.
This script helps users set up their configuration files.
"""
import sys
import shutil
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

def setup_config():
    """Set up configuration files."""
    print("Setting up configuration files...")

    # Check if config.json exists
    config_path = project_root / "config" / "config.json"
    config_example_path = project_root / "config" / "config.json.example"

    if not config_path.exists() and config_example_path.exists():
        print(f"Creating config.json from example...")
        shutil.copy(config_example_path, config_path)
        print(f"Created {config_path}")
    elif not config_path.exists():
        print(f"Error: {config_example_path} not found. Cannot create config.json.")
        return False
    else:
        print(f"Config file {config_path} already exists.")

    # Check if local_config.json exists
    local_config_path = project_root / "config" / "local_config.json"
    local_config_template_path = project_root / "config" / "local_config_template.json"

    if not local_config_path.exists() and local_config_template_path.exists():
        print(f"Creating local_config.json from template...")
        shutil.copy(local_config_template_path, local_config_path)
        print(f"Created {local_config_path}")
        print("Please edit local_config.json with your MT5 credentials.")
    elif not local_config_path.exists():
        print(f"Error: {local_config_template_path} not found. Cannot create local_config.json.")
        return False
    else:
        print(f"Local config file {local_config_path} already exists.")

    # Create directories
    directories = [
        project_root / "data",
        project_root / "models",
        project_root / "logs",
        project_root / "data" / "cache",
        project_root / "monitoring",
        project_root / "monitoring" / "plots",
        project_root / "monitoring" / "reports"
    ]

    for directory in directories:
        if not directory.exists():
            print(f"Creating directory {directory}...")
            directory.mkdir(parents=True, exist_ok=True)

    print("Configuration setup complete.")
    return True

def update_gitignore():
    """Update .gitignore file to exclude sensitive files."""
    gitignore_path = project_root / ".gitignore"

    # Entries to add to .gitignore
    entries = [
        "# Sensitive configuration files",
        "config/local_config.json",
        "config/credentials.py",
        "",
        "# Data and model files",
        "data/",
        "models/",
        "logs/",
        "monitoring/",
        "*.log",
        "*.h5",
        "*.pkl",
        "*.json.bak",
        ""
    ]

    # Read existing .gitignore
    existing_entries = []
    if gitignore_path.exists():
        with open(gitignore_path, "r") as f:
            existing_entries = f.read().splitlines()

    # Add new entries
    with open(gitignore_path, "a+") as f:
        for entry in entries:
            if entry not in existing_entries:
                f.write(f"{entry}\n")

    print("Updated .gitignore file.")
    return True

def check_gpu_setup():
    """Check GPU setup and provide installation instructions."""
    print("\n🔍 Checking GPU setup...")

    try:
        import torch

        # Check PyTorch GPU support
        cuda_available = torch.cuda.is_available()
        device_count = torch.cuda.device_count() if cuda_available else 0

        print(f"  - PyTorch version: {torch.__version__}")
        print(f"  - CUDA available: {cuda_available}")
        print(f"  - GPU devices: {device_count}")

        if not cuda_available:
            print("\n⚠️  WARNING: PyTorch was not built with CUDA support!")
            print("   GPU training is not available with this installation.")
            print("\n🔧 To enable GPU training:")
            print("   1. Uninstall current PyTorch:")
            print("      pip uninstall torch torchvision torchaudio")
            print("   2. Install PyTorch with GPU support:")
            print("      pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
            print("   3. Verify CUDA and GPU drivers are installed")
            print("   4. Restart your terminal/IDE after installation")
            return False
        elif device_count == 0:
            print("\n⚠️  WARNING: No GPUs detected by PyTorch!")
            print("   Check your GPU drivers and CUDA installation.")
            return False
        else:
            print(f"\n✅ GPU setup is correct! {device_count} GPU(s) available.")
            for i in range(device_count):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"   - GPU {i}: {gpu_name}")
            return True

    except ImportError:
        print("\n❌ PyTorch not installed!")
        print("   Install with: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        return False
    except Exception as e:
        print(f"\n❌ Error checking GPU setup: {str(e)}")
        return False

def main():
    """Main function."""
    print("Trading Bot Configuration Setup")
    print("==============================")

    if not setup_config():
        print("Error setting up configuration files.")
        return 1

    if not update_gitignore():
        print("Error updating .gitignore file.")
        return 1

    # Check GPU setup
    gpu_available = check_gpu_setup()

    print("\nSetup complete!")
    print("Please edit config/local_config.json with your MT5 credentials.")

    if gpu_available:
        print("\n✅ GPU training is available!")
        print("Test with: python train_models.py --models lstm --epochs 10 --batch-size 16")
    else:
        print("\n⚠️  GPU training not available (will use CPU)")
        print("Test with: python train_models.py --models lstm --epochs 10 --batch-size 16")

    print("You can now run the trading bot with: python main.py")

    return 0

if __name__ == "__main__":
    sys.exit(main())
