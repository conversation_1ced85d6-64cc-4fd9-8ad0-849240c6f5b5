"""
MT5 Terminal Launcher utility - ENABLED FOR FULL MT5 MODE.
This module provides terminal management for multi-terminal support.
"""

# FULL MT5 MODE: This module is enabled for multi-terminal support
import logging
import os
import subprocess
import time
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)

def _get_config_manager():
    """Get the configuration manager instance."""
    try:
        from config import config_manager
        return config_manager
    except ImportError:
        logger.error("Could not import configuration manager")
        return None

def is_terminal_running(terminal_id: Union[str, int]) -> bool:
    """
    Check if an MT5 terminal is running.

    Args:
        terminal_id: ID of the MT5 terminal to check

    Returns:
        bool: True if terminal is running, False otherwise
    """
    try:
        terminal_path = get_terminal_path(str(terminal_id))
        if not terminal_path:
            return False

        # Check if any process is running the terminal executable
        terminal_name = Path(terminal_path).name
        terminal_dir = str(Path(terminal_path).parent)

        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cwd']):
            try:
                proc_info = proc.info
                if not proc_info['name']:
                    continue

                # Check if process name matches
                if terminal_name.lower() in proc_info['name'].lower():
                    # Additional checks for better matching
                    if proc_info['exe']:
                        # Check if executable path matches
                        if terminal_path.lower() in proc_info['exe'].lower():
                            return True
                        # Check if directory matches (for cases where full path doesn't match)
                        if terminal_dir.lower() in proc_info['exe'].lower():
                            return True

                    # If we can't get exe info, check working directory
                    if proc_info.get('cwd') and terminal_dir.lower() in proc_info['cwd'].lower():
                        return True

            except (psutil.AccessDenied, psutil.NoSuchProcess, psutil.ZombieProcess):
                continue
        return False
    except Exception as e:
        logger.error(f"Error checking if terminal {terminal_id} is running: {str(e)}")
        return False

def start_terminal(terminal_id: Union[str, int]) -> bool:
    """
    Start an MT5 terminal.

    Args:
        terminal_id: ID of the MT5 terminal to start

    Returns:
        bool: True if terminal started successfully, False otherwise
    """
    try:
        terminal_id_str = str(terminal_id)

        # Check if terminal is already running
        if is_terminal_running(terminal_id_str):
            logger.info(f"Terminal {terminal_id_str} is already running")
            return True

        # Get terminal path
        terminal_path = get_terminal_path(terminal_id_str)
        if not terminal_path:
            logger.error(f"Terminal path not found for terminal {terminal_id_str}")
            return False

        # Check if executable exists
        if not os.path.exists(terminal_path):
            logger.error(f"Terminal executable not found: {terminal_path}")
            return False

        # Start the terminal
        logger.info(f"Starting terminal {terminal_id_str}: {terminal_path}")
        try:
            # Use shell=True on Windows for better compatibility
            if os.name == 'nt':  # Windows
                subprocess.Popen(f'"{terminal_path}"', shell=True)
            else:
                subprocess.Popen([terminal_path], shell=False)
            logger.info(f"Terminal {terminal_id_str} process launched")
        except Exception as start_err:
            logger.error(f"Error launching terminal {terminal_id_str}: {start_err}")
            return False

        # Wait a bit for the terminal to start
        time.sleep(3)

        # Verify it started - give it more time and check multiple times
        max_attempts = 5
        for attempt in range(max_attempts):
            time.sleep(1)  # Wait a bit more between checks
            if is_terminal_running(terminal_id_str):
                logger.info(f"Terminal {terminal_id_str} started successfully (attempt {attempt + 1})")
                return True

        # Final check - maybe the process started but we can't detect it properly
        # Check if any terminal64.exe process is running
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and 'terminal64.exe' in proc.info['name'].lower():
                        logger.info(f"Found terminal64.exe process (PID: {proc.info['pid']}) - assuming terminal {terminal_id_str} started")
                        return True
                except (psutil.AccessDenied, psutil.NoSuchProcess):
                    continue
        except Exception as e:
            logger.debug(f"Error checking for terminal processes: {str(e)}")

        logger.warning(f"Terminal {terminal_id_str} may not have started properly")
        return False

    except Exception as e:
        logger.error(f"Error starting terminal {terminal_id}: {str(e)}")
        return False

def stop_terminal(terminal_id: Union[str, int]) -> bool:
    """
    Stop an MT5 terminal.

    Args:
        terminal_id: ID of the MT5 terminal to stop

    Returns:
        bool: True if terminal stopped successfully, False otherwise
    """
    try:
        terminal_id_str = str(terminal_id)

        # Check if terminal is running
        if not is_terminal_running(terminal_id_str):
            logger.info(f"Terminal {terminal_id_str} is not running")
            return True

        # Get terminal path
        terminal_path = get_terminal_path(terminal_id_str)
        if not terminal_path:
            logger.error(f"Terminal path not found for terminal {terminal_id_str}")
            return False

        # Find and terminate the process
        terminal_name = Path(terminal_path).name
        terminated = False

        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and terminal_name.lower() in proc.info['name'].lower():
                    if proc.info['exe'] and terminal_path in proc.info['exe']:
                        logger.info(f"Terminating terminal {terminal_id_str} (PID: {proc.info['pid']})")
                        proc.terminate()
                        terminated = True
                        break
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                continue

        if terminated:
            # Wait for process to terminate
            time.sleep(2)

            # Verify it stopped
            if not is_terminal_running(terminal_id_str):
                logger.info(f"Terminal {terminal_id_str} stopped successfully")
                return True
            else:
                logger.warning(f"Terminal {terminal_id_str} may not have stopped properly")
                return False
        else:
            logger.warning(f"Could not find running process for terminal {terminal_id_str}")
            return False

    except Exception as e:
        logger.error(f"Error stopping terminal {terminal_id}: {str(e)}")
        return False

def get_terminal_path(terminal_id: str) -> Optional[str]:
    """
    Get the path to an MT5 terminal executable.

    Args:
        terminal_id: ID of the MT5 terminal

    Returns:
        Optional[str]: Path to terminal executable if found, None otherwise
    """
    try:
        config_manager = _get_config_manager()
        if not config_manager:
            return None

        mt5_config = config_manager.get_mt5_config()
        if not mt5_config or not hasattr(mt5_config, 'terminals'):
            return None

        terminal_config = mt5_config.terminals.get(terminal_id)
        if not terminal_config:
            return None

        if isinstance(terminal_config, dict):
            return terminal_config.get('path')
        else:
            return getattr(terminal_config, 'path', None)

    except Exception as e:
        logger.error(f"Error getting terminal path for {terminal_id}: {str(e)}")
        return None

def get_configured_terminals() -> List[str]:
    """
    Get a list of all configured MT5 terminals.

    Returns:
        List[str]: List of terminal IDs that are configured
    """
    try:
        config_manager = _get_config_manager()
        if not config_manager:
            return []

        mt5_config = config_manager.get_mt5_config()
        if not mt5_config or not hasattr(mt5_config, 'terminals'):
            return []

        return list(mt5_config.terminals.keys())

    except Exception as e:
        logger.error(f"Error getting configured terminals: {str(e)}")
        return []

def get_running_terminals() -> List[str]:
    """
    Get a list of running MT5 terminals.

    Returns:
        List[str]: List of terminal IDs that are currently running
    """
    try:
        configured_terminals = get_configured_terminals()
        running_terminals = []

        for terminal_id in configured_terminals:
            if is_terminal_running(terminal_id):
                running_terminals.append(terminal_id)

        return running_terminals

    except Exception as e:
        logger.error(f"Error getting running terminals: {str(e)}")
        return []

def start_all_terminals() -> Dict[str, bool]:
    """
    Start all configured MT5 terminals.

    Returns:
        Dict[str, bool]: Dictionary mapping terminal IDs to success status
    """
    try:
        configured_terminals = get_configured_terminals()
        results = {}

        for terminal_id in configured_terminals:
            logger.info(f"Starting terminal {terminal_id}...")
            success = start_terminal(terminal_id)
            results[terminal_id] = success

            if success:
                logger.info(f"Terminal {terminal_id} started successfully")
            else:
                logger.error(f"Failed to start terminal {terminal_id}")

            # Wait between terminal starts
            time.sleep(2)

        return results

    except Exception as e:
        logger.error(f"Error starting all terminals: {str(e)}")
        return {}

def stop_all_terminals() -> Dict[str, bool]:
    """
    Stop all running MT5 terminals.

    Returns:
        Dict[str, bool]: Dictionary mapping terminal IDs to success status
    """
    try:
        running_terminals = get_running_terminals()
        results = {}

        for terminal_id in running_terminals:
            logger.info(f"Stopping terminal {terminal_id}...")
            success = stop_terminal(terminal_id)
            results[terminal_id] = success

            if success:
                logger.info(f"Terminal {terminal_id} stopped successfully")
            else:
                logger.error(f"Failed to stop terminal {terminal_id}")

            # Wait between terminal stops
            time.sleep(1)

        return results

    except Exception as e:
        logger.error(f"Error stopping all terminals: {str(e)}")
        return {}
