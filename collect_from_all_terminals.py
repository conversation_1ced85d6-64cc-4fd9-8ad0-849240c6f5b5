#!/usr/bin/env python
"""
<PERSON>ript to collect 6-year historical BTCUSD.a data from all 5 MT5 terminals
across multiple timeframes (M5, M15, M30, H1, H4) in parquet format.

This script:
1. Connects to all 5 MT5 terminals
2. Collects historical OHLCV data for BTCUSD.a from each terminal
3. Processes and validates the data
4. Saves the data in parquet format
5. Verifies the collected data for completeness and quality
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from pathlib import Path
import argparse
import time
import sys
import subprocess
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('multi_terminal_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('multi_terminal_collection')

# Constants
SYMBOL = "BTCUSD.a"
TIMEFRAMES = ["M5", "M15", "M30", "H1", "H4"]
YEARS = 6  # 6 years of historical data
TERMINAL_IDS = [1, 2, 3, 4, 5]  # All 5 terminals
MT5_PATHS = {
    1: "C:/Program Files/MT5_Terminal1/terminal64.exe",
    2: "C:/Program Files/MT5_Terminal2/terminal64.exe",
    3: "C:/Program Files/MT5_Terminal3/terminal64.exe",
    4: "C:/Program Files/MT5_Terminal4/terminal64.exe",
    5: "C:/Program Files/MT5_Terminal5/terminal64.exe"
}

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect historical BTCUSD.a data from all 5 MT5 terminals')
    parser.add_argument('--output_dir', type=str, default='data/multi_terminal',
                        help='Output directory for collected data')
    parser.add_argument('--years', type=int, default=6,
                        help='Number of years of historical data to collect')
    parser.add_argument('--force', action='store_true',
                        help='Force data collection even if files already exist')
    return parser.parse_args()

def start_mt5_terminal(terminal_id):
    """
    Start an MT5 terminal.

    MINIMAL MT5 MODE: This function is disabled to preserve algorithmic trading.
    The function will attempt to call terminal management but will show warnings.

    Args:
        terminal_id: Terminal ID to start

    Returns:
        True if terminal started successfully, False otherwise
    """
    try:
        logger.warning("=" * 60)
        logger.warning(f"MINIMAL MT5 MODE: Terminal {terminal_id} startup is DISABLED")
        logger.warning("This preserves algorithmic trading settings")
        logger.warning("Please ensure MT5 terminals are started manually")
        logger.warning("=" * 60)

        # Try to start the terminal using the manage_mt5_terminals.py script
        cmd = f"python scripts/manage_mt5_terminals.py --start --terminal {terminal_id} --wait 5"
        logger.info(f"Running command (will show warnings): {cmd}")

        result = subprocess.run(cmd, shell=True, check=False, capture_output=True, text=True)

        # In minimal mode, we assume the terminal is manually started
        logger.warning(f"MINIMAL MT5 MODE: Assuming terminal {terminal_id} is manually started")
        logger.warning("Please verify that your MT5 terminal is running with algo trading enabled")

        # Return True to continue with data collection
        return True

    except Exception as e:
        logger.warning(f"Terminal management error (expected in minimal mode): {str(e)}")
        logger.warning("Please ensure MT5 terminals are started manually")
        return True  # Continue anyway in minimal mode

def collect_data_from_terminal(terminal_id, symbol, timeframe, start_date, end_date, output_dir, force=False):
    """
    Collect data from a specific terminal for a symbol and timeframe.
    
    Args:
        terminal_id: MT5 terminal ID
        symbol: Symbol to collect data for
        timeframe: Timeframe string (e.g., "M5")
        start_date: Start date for data collection
        end_date: End date for data collection
        output_dir: Output directory for collected data
        force: Force data collection even if file already exists
        
    Returns:
        Tuple of (success, message, data_path)
    """
    try:
        logger.info(f"Collecting {symbol} {timeframe} data from terminal {terminal_id}")
        
        # Prepare output path
        output_path = Path(output_dir) / f"{symbol}_{timeframe}_terminal_{terminal_id}.parquet"
        
        # Check if file already exists and is not empty
        if output_path.exists() and output_path.stat().st_size > 0 and not force:
            logger.info(f"Data file already exists: {output_path}")
            return True, f"Data file already exists: {output_path}", output_path
        
        # Create output directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Start the terminal
        if not start_mt5_terminal(terminal_id):
            return False, f"Failed to start MT5 terminal {terminal_id}", None
        
        # Use a Python script to collect data from this terminal
        script = f"""
import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import time

# Constants
SYMBOL = "{symbol}"
TIMEFRAME = "{timeframe}"
START_DATE = datetime.fromisoformat("{start_date.isoformat()}")
END_DATE = datetime.fromisoformat("{end_date.isoformat()}")
OUTPUT_PATH = "{output_path}"

# MT5 timeframe mapping
TIMEFRAME_MAP = {{
    'M1': mt5.TIMEFRAME_M1,
    'M5': mt5.TIMEFRAME_M5,
    'M15': mt5.TIMEFRAME_M15,
    'M30': mt5.TIMEFRAME_M30,
    'H1': mt5.TIMEFRAME_H1,
    'H4': mt5.TIMEFRAME_H4,
    'D1': mt5.TIMEFRAME_D1,
    'W1': mt5.TIMEFRAME_W1,
    'MN1': mt5.TIMEFRAME_MN1
}}

# Initialize MT5
if not mt5.initialize():
    print(f"Failed to initialize MT5: {{mt5.last_error()}}")
    exit(1)

# Get MT5 timeframe constant
mt5_timeframe = TIMEFRAME_MAP.get(TIMEFRAME)
if mt5_timeframe is None:
    print(f"Invalid timeframe: {{TIMEFRAME}}")
    mt5.shutdown()
    exit(1)

# Collect data in chunks to manage memory
all_data = []
current_start = START_DATE
chunk_size = timedelta(days=30)  # Process 30 days at a time

while current_start < END_DATE:
    chunk_end = min(current_start + chunk_size, END_DATE)
    
    print(f"Collecting chunk from {{current_start}} to {{chunk_end}} for {{SYMBOL}} {{TIMEFRAME}}")
    
    # Get rates from MT5
    rates = mt5.copy_rates_range(
        SYMBOL,
        mt5_timeframe,
        current_start,
        chunk_end
    )
    
    if rates is None or len(rates) == 0:
        print(f"No data returned for {{SYMBOL}} {{TIMEFRAME}} from {{current_start}} to {{chunk_end}}")
        # Move to next chunk
        current_start = chunk_end
        continue
    
    # Convert to DataFrame
    chunk_df = pd.DataFrame(rates)
    chunk_df['time'] = pd.to_datetime(chunk_df['time'], unit='s')
    chunk_df.set_index('time', inplace=True)
    
    # Add to list
    all_data.append(chunk_df)
    print(f"Collected {{len(chunk_df)}} rows for {{SYMBOL}} {{TIMEFRAME}} ({{current_start}} to {{chunk_end}})")
    
    # Move to next chunk
    current_start = chunk_end
    
    # Sleep to prevent overwhelming MT5
    time.sleep(1)

# Combine all chunks
if not all_data:
    print(f"No data collected for {{SYMBOL}} {{TIMEFRAME}}")
    mt5.shutdown()
    exit(1)

combined_df = pd.concat(all_data)

# Remove duplicates and sort
combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
combined_df = combined_df.sort_index()

# Save to parquet
combined_df.to_parquet(OUTPUT_PATH)

print(f"Successfully saved {{len(combined_df)}} rows of {{SYMBOL}} {{TIMEFRAME}} data to {{OUTPUT_PATH}}")

# Shutdown MT5
mt5.shutdown()
"""
        
        # Save the script to a temporary file
        script_path = f"temp_collect_terminal_{terminal_id}.py"
        with open(script_path, "w") as f:
            f.write(script)
        
        # Run the script
        logger.info(f"Running data collection script for terminal {terminal_id}")
        result = subprocess.run(f"python {script_path}", shell=True, check=False, capture_output=True, text=True)
        
        # Clean up the temporary script
        try:
            os.remove(script_path)
        except:
            pass
        
        if result.returncode == 0 and "Successfully saved" in result.stdout:
            logger.info(f"Successfully collected data from terminal {terminal_id}")
            return True, f"Successfully collected data from terminal {terminal_id}", output_path
        else:
            logger.error(f"Failed to collect data from terminal {terminal_id}: {result.stderr}")
            return False, f"Failed to collect data from terminal {terminal_id}: {result.stderr}", None
        
    except Exception as e:
        logger.error(f"Error collecting data from terminal {terminal_id}: {str(e)}")
        return False, f"Error: {str(e)}", None

def merge_terminal_data(symbol, timeframe, terminal_data_paths, output_dir):
    """
    Merge data from multiple terminals for a specific symbol and timeframe.
    
    Args:
        symbol: Trading symbol
        timeframe: Timeframe
        terminal_data_paths: List of paths to terminal data files
        output_dir: Output directory for merged data
        
    Returns:
        Tuple of (success, message, data_path)
    """
    try:
        logger.info(f"Merging {symbol} {timeframe} data from {len(terminal_data_paths)} terminals")
        
        # Prepare output path
        output_path = Path(output_dir) / f"{symbol}_{timeframe}_merged.parquet"
        
        # Create output directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Load data from each terminal
        dfs = []
        for path in terminal_data_paths:
            try:
                if not Path(path).exists():
                    logger.warning(f"File does not exist: {path}")
                    continue
                    
                df = pd.read_parquet(path)
                if df.empty:
                    logger.warning(f"Empty data file: {path}")
                    continue
                    
                dfs.append(df)
                logger.info(f"Loaded {len(df)} rows from {path}")
            except Exception as e:
                logger.error(f"Error loading {path}: {str(e)}")
        
        if not dfs:
            logger.error(f"No valid data loaded for {symbol} {timeframe}")
            return False, f"No valid data loaded for {symbol} {timeframe}", None
        
        # Concatenate all dataframes
        merged_df = pd.concat(dfs)
        
        # Remove duplicates and sort
        merged_df = merged_df[~merged_df.index.duplicated(keep='first')]
        merged_df = merged_df.sort_index()
        
        # Calculate technical indicators
        merged_df = calculate_technical_indicators(merged_df)
        
        # Save to parquet
        merged_df.to_parquet(output_path)
        
        logger.info(f"Successfully saved {len(merged_df)} rows of merged {symbol} {timeframe} data to {output_path}")
        
        return True, f"Successfully merged data for {symbol} {timeframe}", output_path
        
    except Exception as e:
        logger.error(f"Error merging data for {symbol} {timeframe}: {str(e)}")
        return False, f"Error: {str(e)}", None

def calculate_technical_indicators(df):
    """
    Calculate technical indicators for a DataFrame.
    
    Args:
        df: DataFrame with OHLCV data
        
    Returns:
        DataFrame with technical indicators added
    """
    try:
        if len(df) < 30:
            logger.warning("Not enough data to calculate all indicators")
            return df
        
        # Make a copy to avoid modifying original
        df = df.copy()
        
        # Calculate RSI (14)
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Calculate MACD
        ema12 = df['close'].ewm(span=12, adjust=False).mean()
        ema26 = df['close'].ewm(span=26, adjust=False).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # Calculate Bollinger Bands (20, 2)
        df['bb_middle'] = df['close'].rolling(20).mean()
        std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (std * 2)
        df['bb_lower'] = df['bb_middle'] - (std * 2)
        
        # Calculate ATR (14)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        df['atr'] = true_range.rolling(14).mean()
        
        # Calculate returns
        df['returns'] = df['close'].pct_change()
        
        # Calculate volatility (14)
        df['volatility'] = df['returns'].rolling(14).std()
        
        # Calculate momentum (14)
        df['momentum'] = df['close'] / df['close'].shift(14) - 1
        
        return df
        
    except Exception as e:
        logger.error(f"Error calculating technical indicators: {str(e)}")
        return df

def prepare_data_for_training(input_path, output_dir):
    """
    Prepare data for model training by splitting into train/validation/test sets.
    
    Args:
        input_path: Path to the input parquet file
        output_dir: Output directory for processed data
        
    Returns:
        Tuple of (success, message)
    """
    try:
        logger.info(f"Preparing data from {input_path} for model training")
        
        # Load the data
        df = pd.read_parquet(input_path)
        
        # Get symbol and timeframe from filename
        filename = Path(input_path).stem
        parts = filename.split('_')
        symbol = parts[0]
        timeframe = parts[1]
        
        # Create output directories
        train_dir = Path(output_dir) / 'train'
        val_dir = Path(output_dir) / 'validation'
        test_dir = Path(output_dir) / 'test'
        
        for directory in [train_dir, val_dir, test_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Sort by time
        df = df.sort_index()
        
        # Calculate split points (70% train, 15% validation, 15% test)
        total_rows = len(df)
        train_end = int(total_rows * 0.7)
        val_end = train_end + int(total_rows * 0.15)
        
        # Split the data
        train_df = df.iloc[:train_end]
        val_df = df.iloc[train_end:val_end]
        test_df = df.iloc[val_end:]
        
        # Save to parquet files
        train_path = train_dir / f"{symbol}_{timeframe}.parquet"
        val_path = val_dir / f"{symbol}_{timeframe}.parquet"
        test_path = test_dir / f"{symbol}_{timeframe}.parquet"
        
        train_df.to_parquet(train_path)
        val_df.to_parquet(val_path)
        test_df.to_parquet(test_path)
        
        logger.info(f"Saved {len(train_df)} training rows to {train_path}")
        logger.info(f"Saved {len(val_df)} validation rows to {val_path}")
        logger.info(f"Saved {len(test_df)} test rows to {test_path}")
        
        # Save metadata
        metadata = {
            'symbol': symbol,
            'timeframe': timeframe,
            'total_rows': total_rows,
            'train_rows': len(train_df),
            'val_rows': len(val_df),
            'test_rows': len(test_df),
            'train_start': train_df.index.min().strftime('%Y-%m-%d %H:%M:%S'),
            'train_end': train_df.index.max().strftime('%Y-%m-%d %H:%M:%S'),
            'val_start': val_df.index.min().strftime('%Y-%m-%d %H:%M:%S'),
            'val_end': val_df.index.max().strftime('%Y-%m-%d %H:%M:%S'),
            'test_start': test_df.index.min().strftime('%Y-%m-%d %H:%M:%S'),
            'test_end': test_df.index.max().strftime('%Y-%m-%d %H:%M:%S'),
            'columns': list(df.columns),
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        metadata_path = Path(output_dir) / 'metadata' / f"{symbol}_{timeframe}_metadata.json"
        metadata_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=4)
        
        logger.info(f"Saved metadata to {metadata_path}")
        
        return True, f"Successfully prepared data for {symbol} {timeframe}"
        
    except Exception as e:
        logger.error(f"Error preparing data: {str(e)}")
        return False, f"Error: {str(e)}"

def main():
    """Main function to collect and prepare BTCUSD.a data from all terminals."""
    args = parse_arguments()
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * args.years)
    
    logger.info(f"Starting data collection for {SYMBOL} from {start_date} to {end_date}")
    logger.info(f"Collecting data for timeframes: {TIMEFRAMES}")
    logger.info(f"Using terminals: {TERMINAL_IDS}")
    
    # Create output directories
    raw_dir = Path(args.output_dir) / 'raw'
    merged_dir = Path(args.output_dir) / 'merged'
    processed_dir = Path(args.output_dir) / 'processed'
    
    for directory in [raw_dir, merged_dir, processed_dir]:
        directory.mkdir(parents=True, exist_ok=True)
    
    # Collect data from each terminal for each timeframe
    collection_results = {}
    for timeframe in TIMEFRAMES:
        collection_results[timeframe] = {}
        for terminal_id in TERMINAL_IDS:
            success, message, data_path = collect_data_from_terminal(
                terminal_id,
                SYMBOL,
                timeframe,
                start_date,
                end_date,
                raw_dir,
                args.force
            )
            
            collection_results[timeframe][terminal_id] = {
                'success': success,
                'message': message,
                'data_path': data_path
            }
            
            logger.info(f"Result for {SYMBOL} {timeframe} from terminal {terminal_id}: {message}")
    
    # Merge data from all terminals for each timeframe
    merge_results = {}
    for timeframe in TIMEFRAMES:
        # Get paths to successful collections
        terminal_data_paths = [
            result['data_path'] 
            for terminal_id, result in collection_results[timeframe].items() 
            if result['success'] and result['data_path'] is not None
        ]
        
        success, message, data_path = merge_terminal_data(
            SYMBOL,
            timeframe,
            terminal_data_paths,
            merged_dir
        )
        
        merge_results[timeframe] = {
            'success': success,
            'message': message,
            'data_path': data_path
        }
        
        logger.info(f"Merge result for {SYMBOL} {timeframe}: {message}")
    
    # Prepare data for model training
    preparation_results = {}
    for timeframe, result in merge_results.items():
        if result['success'] and result['data_path'] is not None:
            success, message = prepare_data_for_training(
                result['data_path'],
                processed_dir
            )
            
            preparation_results[timeframe] = {
                'success': success,
                'message': message
            }
            
            logger.info(f"Preparation result for {SYMBOL} {timeframe}: {message}")
    
    # Print summary
    logger.info("\n=== Data Collection Summary ===")
    for timeframe in TIMEFRAMES:
        successful_terminals = sum(1 for result in collection_results[timeframe].values() if result['success'])
        logger.info(f"{SYMBOL} {timeframe}: Collected from {successful_terminals}/{len(TERMINAL_IDS)} terminals")
    
    logger.info("\n=== Data Merge Summary ===")
    for timeframe in TIMEFRAMES:
        status = "SUCCESS" if merge_results[timeframe]['success'] else "FAILED"
        logger.info(f"{SYMBOL} {timeframe}: {status} - {merge_results[timeframe]['message']}")
    
    logger.info("\n=== Data Preparation Summary ===")
    for timeframe in TIMEFRAMES:
        if timeframe in preparation_results:
            status = "SUCCESS" if preparation_results[timeframe]['success'] else "FAILED"
            logger.info(f"{SYMBOL} {timeframe}: {status} - {preparation_results[timeframe]['message']}")
        else:
            logger.info(f"{SYMBOL} {timeframe}: SKIPPED - No merged data available")
    
    logger.info("Data collection, merging, and preparation complete.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
