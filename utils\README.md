# Enhanced Error Handling System

This directory contains the enhanced error handling system for the trading bot. The system provides comprehensive error logging, tracking, recovery mechanisms, and circuit breaker integration.

## Overview

The enhanced error handling system consists of the following components:

1. **EnhancedErrorHandler**: The main error handler that provides centralized error handling, tracking, and recovery mechanisms.
2. **CircuitBreaker**: A circuit breaker implementation that prevents cascading failures by temporarily disabling operations when a threshold of failures is reached.
3. **Error Recovery Strategies**: Specific recovery strategies for different error categories.
4. **Error Context**: Detailed context information for better debugging.

## Usage

### Basic Error Handling

```python
from utils.enhanced_error_handler import enhanced_error_handler, ErrorCategory, ErrorSeverity

try:
    # Some code that might raise an exception
    result = some_function()
except Exception as e:
    # Handle the error
    error_info = enhanced_error_handler.handle_error(
        exception=e,
        context={"some_context": "value"},
        source="my_module",
        severity=ErrorSeverity.ERROR,
        category=ErrorCategory.DATA
    )
    
    # Check if recovery was attempted and succeeded
    if error_info.recovery_succeeded:
        # Recovery succeeded, continue
        pass
    else:
        # Recovery failed, handle the error
        pass
```

### Using the Decorator

```python
from utils.enhanced_error_handler import with_error_handling

@with_error_handling
def my_function():
    # Some code that might raise an exception
    result = some_function()
    return result
```

### Using the Circuit Breaker

```python
from utils.enhanced_error_handler import with_circuit_breaker

# Use the circuit breaker
result = with_circuit_breaker("my_circuit", my_function, arg1, arg2, kwarg1=value1)
```

### Using the Retry Decorator

```python
from utils.enhanced_error_handler import with_retry

@with_retry(max_retries=3, retry_delay=1.0, backoff_factor=2.0)
def my_function():
    # Some code that might raise an exception
    result = some_function()
    return result
```

### Registering Recovery Handlers

```python
from utils.enhanced_error_handler import enhanced_error_handler, ErrorCategory

# Register a recovery handler for a specific exception type
def my_recovery_handler(exception, context):
    # Attempt to recover from the error
    # Return True if recovery succeeded, False otherwise
    return True

enhanced_error_handler.register_recovery_handler(ValueError, my_recovery_handler)

# Register a recovery handler for a specific error category
enhanced_error_handler.register_category_recovery_handler(ErrorCategory.NETWORK, my_recovery_handler)

# Register a global recovery handler
enhanced_error_handler.register_global_recovery_handler(my_recovery_handler)
```

## Error Categories

The enhanced error handler categorizes errors into the following categories:

- `NETWORK`: Network-related errors
- `AUTHENTICATION`: Authentication failures
- `DATA`: Data integrity/parsing issues
- `EXECUTION`: Trade execution problems
- `RESOURCE`: Resource exhaustion (memory, CPU)
- `MT5_API`: MT5-specific API errors
- `MODEL`: ML model errors
- `CONFIG`: Configuration errors
- `SYSTEM`: System-level errors
- `DATABASE`: Database-related errors
- `TIMEOUT`: Timeout errors
- `VALIDATION`: Validation errors
- `UNKNOWN`: Uncategorized errors

## Error Severity Levels

The enhanced error handler supports the following severity levels:

- `INFO`: Informational, not critical
- `WARNING`: Warning, might need attention
- `ERROR`: Error, needs attention
- `CRITICAL`: Critical, needs immediate attention
- `FATAL`: Fatal, system cannot continue

## Circuit Breaker States

The circuit breaker can be in one of the following states:

- `CLOSED`: Normal operation, requests allowed
- `OPEN`: Circuit is open, requests are blocked
- `HALF_OPEN`: Testing if the circuit can be closed again

## Best Practices

1. **Use the decorator**: Use the `with_error_handling` decorator for functions that might raise exceptions.
2. **Provide context**: Always provide context information when handling errors.
3. **Categorize errors**: Always categorize errors for better tracking and recovery.
4. **Use circuit breakers**: Use circuit breakers for external dependencies.
5. **Register recovery handlers**: Register recovery handlers for specific error types and categories.
6. **Monitor error rates**: Monitor error rates and trends for early detection of issues.
