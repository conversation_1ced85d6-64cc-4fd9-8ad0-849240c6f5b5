@echo off
REM Trading Bot Startup Script
REM This script starts the MT5 trading bot with proper error handling

echo ========================================
echo MT5 Trading Bot Startup Script
echo ========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo WARNING: Virtual environment not found
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install/update requirements
echo Installing/updating requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install requirements
    pause
    exit /b 1
)

REM Check if configuration exists
if not exist "config\config.json" (
    echo ERROR: Configuration file not found
    echo Please run setup_config.py first
    pause
    exit /b 1
)

REM Create necessary directories
echo Creating necessary directories...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "models" mkdir models
if not exist "reports" mkdir reports

REM MINIMAL MT5 MODE: Terminal management disabled
echo.
echo MINIMAL MT5 MODE: Automatic terminal startup is DISABLED
echo This preserves algorithmic trading settings in your terminals
echo.
echo Please ensure:
echo 1. MT5 terminals are started manually
echo 2. Algorithmic trading is enabled in each terminal
echo 3. Terminals are logged in with correct credentials
echo.
echo Press any key to continue with the trading bot...
pause >nul

REM Start the trading bot
echo.
echo Starting trading bot...
echo Press Ctrl+C to stop the bot
echo.
python main.py

REM Handle exit
echo.
echo Trading bot stopped.
pause
