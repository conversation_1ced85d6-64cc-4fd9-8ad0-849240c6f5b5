import logging
import numpy as np
import pandas as pd
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import MetaTrader5 as mt5

from utils.enhanced_error_handler import EnhancedError<PERSON>and<PERSON>
from utils.enhanced_memory_manager import <PERSON>hancedMemoryManager
from trading.mt5_connection_manager import MT5ConnectionManager

logger = logging.getLogger('data_collector')

class DataCollector:
    """
    Handles data collection from MetaTrader 5 with efficient memory management and error handling.
    Provides methods to collect price data, account info, and market data with built-in retry
    logic and chunk-based processing to prevent memory issues.
    """

    def __init__(
        self,
        mt5_manager: MT5ConnectionManager,
        error_handler: <PERSON>hancedError<PERSON><PERSON><PERSON>,
        memory_manager: EnhancedMemoryManager,
        config: Dict[str, Any]
    ):
        """
        Initialize the data collector.

        Args:
            mt5_manager: Manager for MT5 connections
            error_handler: Error handler for safe execution
            memory_manager: Memory manager to monitor memory usage
            config: Configuration dictionary
        """
        self.mt5_manager = mt5_manager
        self.error_handler = error_handler
        self.memory_manager = memory_manager
        self.config = config

        # Extract configuration values
        self.symbol = config.get('strategy', {}).get('symbol', 'BTCUSD.a')
        self.timeframes = config.get('strategy', {}).get('timeframes', ['H1'])
        self.max_candles = config.get('data_collection', {}).get('max_candles', 5000)
        self.chunk_size = config.get('data_collection', {}).get('chunk_size', 1000)
        # Ensure terminal_id is a string for consistency
        self.terminal_id = str(config.get('mt5', {}).get('terminal_id', '1'))
        self.max_retries = config.get('data_collection', {}).get('max_retries', 3)
        self.retry_delay = config.get('data_collection', {}).get('retry_delay', 5)

        # Register recovery handlers
        self._register_recovery_handlers()

        # Cache for collected data
        self.price_data_cache = {}
        self.last_data_time = {}
        self.update_interval = config.get('strategy', {}).get('update_interval', 60)  # seconds

    def _register_recovery_handlers(self):
        """Register recovery handlers for data collection errors."""
        self.error_handler.register_recovery_handler(
            ConnectionError,
            lambda ctx: self._reconnect_mt5(ctx)
        )
        self.error_handler.register_recovery_handler(
            TimeoutError,
            self._handle_timeout_error
        )
        self.error_handler.register_recovery_handler(
            MemoryError,
            self._handle_memory_error
        )

    def _reconnect_mt5(self, context: Dict[str, Any]) -> bool:
        """Attempt to reconnect to MT5"""
        logger.warning(f"Attempting to reconnect to MT5 terminal {self.terminal_id}")
        # Force get a new connection
        self.mt5_manager.shutdown(self.terminal_id)
        time.sleep(2)  # Wait before reconnection attempt
        connection = self.mt5_manager.get_connection(self.terminal_id, force_new=True)
        return connection.is_connected

    def _handle_timeout_error(self, _: Exception, __: Dict[str, Any]) -> bool:
        """Handle timeout errors by waiting and retrying"""
        logger.warning("Timeout error encountered, waiting before retry")
        time.sleep(self.retry_delay)
        return True

    def _handle_memory_error(self, _: Exception, __: Dict[str, Any]) -> bool:
        """Handle memory errors by cleaning up and reducing chunk size"""
        logger.warning("Memory error encountered, performing cleanup")
        self.memory_manager.cleanup("moderate")

        # Reduce chunk size for future operations
        self.chunk_size = max(1000, int(self.chunk_size * 0.5))
        logger.info(f"Reduced chunk size to {self.chunk_size}")
        return True

    def collect_price_data(
        self,
        symbol: Optional[str] = None,
        timeframes: Optional[List[str]] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        force_update: bool = False
    ) -> Dict[str, pd.DataFrame]:
        """
        Collect price data for the specified symbol and timeframes.

        Args:
            symbol: Trading symbol (e.g., 'EURUSD')
            timeframes: List of timeframes (e.g., ['H1', 'H4'])
            start_time: Start time for data collection
            end_time: End time for data collection
            force_update: Force update even if cache is valid

        Returns:
            Dict[str, pd.DataFrame]: Dictionary mapping timeframe to price data DataFrame
        """
        # Use default values if not provided
        symbol = symbol or self.symbol
        timeframes = timeframes or self.timeframes
        end_time = end_time or datetime.now()

        # If start_time is not provided, calculate based on max candles
        if start_time is None:
            # Different lookback for different timeframes
            max_lookback = {
                'M1': timedelta(days=7),
                'M5': timedelta(days=30),
                'M15': timedelta(days=60),
                'M30': timedelta(days=90),
                'H1': timedelta(days=180),
                'H4': timedelta(days=365),
                'D1': timedelta(days=1000)
            }

            # Find the longest timeframe to determine start_time
            longest_tf = timeframes[0]
            for tf in timeframes:
                if max_lookback.get(tf, timedelta(days=30)) > max_lookback.get(longest_tf, timedelta(days=30)):
                    longest_tf = tf

            start_time = end_time - max_lookback.get(longest_tf, timedelta(days=30))

        return self.error_handler.safe_execute(
            self._collect_price_data_impl,
            args=(symbol, timeframes, start_time, end_time, force_update),
            retry_count=self.max_retries,
            retry_delay=self.retry_delay,
            context={
                'component': 'data_collection',
                'symbol': symbol,
                'timeframes': timeframes
            }
        )

    def _collect_price_data_impl(
        self,
        symbol: str,
        timeframes: List[str],
        start_time: datetime,
        end_time: datetime,
        force_update: bool
    ) -> Dict[str, pd.DataFrame]:
        """
        Implementation of price data collection with chunking to manage memory.

        Args:
            symbol: Trading symbol
            timeframes: List of timeframes
            start_time: Start time for data collection
            end_time: End time for data collection
            force_update: Force update even if cache is valid

        Returns:
            Dict[str, pd.DataFrame]: Dictionary mapping timeframe to price data DataFrame
        """
        logger.info(f"Collecting price data for {symbol} on timeframes {timeframes}")

        # Check memory before processing
        self.memory_manager.check_memory_usage()

        # Get MT5 connection
        connection = self.mt5_manager.get_connection(self.terminal_id)
        if not connection.is_connected:
            raise ConnectionError(f"Failed to connect to MT5 terminal {self.terminal_id}")

        # Dictionary to store results for each timeframe
        result = {}

        # Convert timeframe strings to MT5 timeframe constants
        mt5_timeframes = {
            'M1': mt5.TIMEFRAME_M1,
            'M5': mt5.TIMEFRAME_M5,
            'M15': mt5.TIMEFRAME_M15,
            'M30': mt5.TIMEFRAME_M30,
            'H1': mt5.TIMEFRAME_H1,
            'H4': mt5.TIMEFRAME_H4,
            'D1': mt5.TIMEFRAME_D1,
            'W1': mt5.TIMEFRAME_W1,
            'MN1': mt5.TIMEFRAME_MN1
        }

        for timeframe in timeframes:
            # Check if we need to update the cache
            cache_key = f"{symbol}_{timeframe}"
            current_time = datetime.now()

            if (not force_update and
                cache_key in self.price_data_cache and
                cache_key in self.last_data_time and
                (current_time - self.last_data_time[cache_key]).total_seconds() < self.update_interval):
                logger.debug(f"Using cached data for {symbol} {timeframe}")
                result[timeframe] = self.price_data_cache[cache_key]
                continue

            # Convert timeframe string to MT5 timeframe constant
            mt5_timeframe = mt5_timeframes.get(timeframe)
            if mt5_timeframe is None:
                logger.warning(f"Invalid timeframe: {timeframe}, skipping")
                continue

            # Process data in chunks to manage memory
            all_candles = []
            current_start = start_time

            while current_start < end_time:
                # Calculate chunk end time
                chunk_end = min(current_start + timedelta(days=30), end_time)

                # Get rates from MT5
                rates = mt5.copy_rates_range(
                    symbol,
                    mt5_timeframe,
                    current_start,
                    chunk_end
                )

                if rates is None or len(rates) == 0:
                    logger.warning(f"No data received for {symbol} {timeframe} from {current_start} to {chunk_end}")
                    # Move to next chunk
                    current_start = chunk_end
                    continue

                # Convert to pandas DataFrame
                chunk_df = pd.DataFrame(rates)
                chunk_df['time'] = pd.to_datetime(chunk_df['time'], unit='s')

                # Add to list
                all_candles.append(chunk_df)

                # Check memory after processing chunk
                self.memory_manager.check_memory_usage()

                # Update start time for next chunk
                current_start = chunk_end

                # Small sleep to prevent overwhelming MT5
                time.sleep(0.1)

            # Combine all chunks
            if not all_candles:
                logger.warning(f"No data collected for {symbol} {timeframe}")
                continue

            df = pd.concat(all_candles, ignore_index=True)

            # Remove duplicates
            df = df.drop_duplicates(subset=['time'])

            # Sort by time
            df = df.sort_values('time')

            # Set time as index for easier analysis
            df.set_index('time', inplace=True)

            # Store in cache
            self.price_data_cache[cache_key] = df
            self.last_data_time[cache_key] = current_time

            # Add to result
            result[timeframe] = df

            logger.info(f"Collected {len(df)} candles for {symbol} {timeframe}")

        return result

    def get_account_info(self) -> Dict[str, any]:
        """
        Get account information from MT5.

        Returns:
            Dict[str, any]: Account information
        """
        def _get_account_info():
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection.is_connected:
                raise ConnectionError(f"Failed to connect to MT5 terminal {self.terminal_id}")

            account_info = mt5.account_info()
            if account_info is None:
                raise RuntimeError("Failed to get account info from MT5")

            # Convert to dictionary
            account_info_dict = {
                'balance': account_info.balance,
                'equity': account_info.equity,
                'profit': account_info.profit,
                'margin': account_info.margin,
                'margin_free': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'leverage': account_info.leverage,
                'currency': account_info.currency
            }

            return account_info_dict

        # Use error handler to safely execute
        account_info = self.error_handler.safe_execute(
            _get_account_info,
            on_error=lambda e, _: logger.error(f"Error getting account info: {str(e)}"),
            retry_count=self.max_retries,
            retry_delay=self.retry_delay,
            context={'component': 'account_info'}
        )

        if account_info is None:
            logger.error("Failed to get account information")
            # Return empty dict with default values
            return {
                'balance': 0.0,
                'equity': 0.0,
                'profit': 0.0,
                'margin': 0.0,
                'margin_free': 0.0,
                'margin_level': 0.0,
                'leverage': 0.0,
                'currency': 'USD'
            }

        return account_info

    def get_positions(self, symbol: Optional[str] = None) -> List[Dict[str, any]]:
        """
        Get open positions from MT5.

        Args:
            symbol: Trading symbol to filter positions (optional)

        Returns:
            List[Dict[str, any]]: List of open positions
        """
        def _get_positions():
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection.is_connected:
                raise ConnectionError(f"Failed to connect to MT5 terminal {self.terminal_id}")

            if symbol is None:
                positions = mt5.positions_get()
            else:
                positions = mt5.positions_get(symbol=symbol)

            if positions is None:
                return []

            # Convert to list of dictionaries
            positions_list = []
            for position in positions:
                position_dict = {
                    'ticket': position.ticket,
                    'symbol': position.symbol,
                    'type': 'BUY' if position.type == 0 else 'SELL',
                    'volume': position.volume,
                    'open_price': position.price_open,
                    'current_price': position.price_current,
                    'sl': position.sl,
                    'tp': position.tp,
                    'profit': position.profit,
                    'swap': position.swap,
                    'time_open': datetime.fromtimestamp(position.time),
                    'comment': position.comment
                }
                positions_list.append(position_dict)

            return positions_list

        # Use error handler to safely execute
        positions = self.error_handler.safe_execute(
            _get_positions,
            on_error=lambda e, _: logger.error(f"Error getting positions: {str(e)}"),
            retry_count=self.max_retries,
            retry_delay=self.retry_delay,
            context={'component': 'get_positions', 'symbol': symbol}
        )

        if positions is None:
            logger.error(f"Failed to get positions for {symbol or 'all symbols'}")
            return []

        return positions

    def get_symbol_info(self, symbol: Optional[str] = None) -> Dict[str, any]:
        """
        Get symbol information from MT5.

        Args:
            symbol: Trading symbol (optional, uses default if not provided)

        Returns:
            Dict[str, any]: Symbol information
        """
        symbol = symbol or self.symbol

        def _get_symbol_info():
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection.is_connected:
                raise ConnectionError(f"Failed to connect to MT5 terminal {self.terminal_id}")

            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                raise RuntimeError(f"Failed to get info for symbol {symbol}")

            # Convert to dictionary
            symbol_info_dict = {
                'name': symbol_info.name,
                'bid': symbol_info.bid,
                'ask': symbol_info.ask,
                'spread': symbol_info.spread,
                'digits': symbol_info.digits,
                'volume_min': symbol_info.volume_min,
                'volume_step': symbol_info.volume_step,
                'trade_contract_size': symbol_info.trade_contract_size,
                'trade_tick_value': symbol_info.trade_tick_value,
                'point': symbol_info.point
            }

            return symbol_info_dict

        # Use error handler to safely execute
        symbol_info = self.error_handler.safe_execute(
            _get_symbol_info,
            on_error=lambda e, _: logger.error(f"Error getting symbol info for {symbol}: {str(e)}"),
            retry_count=self.max_retries,
            retry_delay=self.retry_delay,
            context={'component': 'symbol_info', 'symbol': symbol}
        )

        if symbol_info is None:
            logger.error(f"Failed to get symbol information for {symbol}")
            # Return empty dict with default values
            return {
                'name': symbol,
                'bid': 0.0,
                'ask': 0.0,
                'spread': 0,
                'digits': 5,
                'volume_min': 0.01,
                'volume_step': 0.01,
                'trade_contract_size': 100000,
                'trade_tick_value': 0.00001,
                'point': 0.00001
            }

        return symbol_info

    def prepare_sequence_data(
        self,
        price_data: Dict[str, pd.DataFrame],
        sequence_length: int = 60
    ) -> Tuple[np.ndarray, pd.DataFrame]:
        """
        Prepare sequence data for model input.

        Args:
            price_data: Dictionary mapping timeframe to price data DataFrame
            sequence_length: Length of sequence for model input

        Returns:
            Tuple[np.ndarray, pd.DataFrame]: Tuple containing model input array and latest data
        """
        return self.error_handler.safe_execute(
            self._prepare_sequence_data_impl,
            args=(price_data, sequence_length),
            retry_count=2,
            context={'component': 'prepare_sequence_data'}
        )

    def _prepare_sequence_data_impl(
        self,
        price_data: Dict[str, pd.DataFrame],
        sequence_length: int
    ) -> Tuple[np.ndarray, pd.DataFrame]:
        """
        Implementation of sequence data preparation.

        Args:
            price_data: Dictionary mapping timeframe to price data DataFrame
            sequence_length: Length of sequence for model input

        Returns:
            Tuple[np.ndarray, pd.DataFrame]: Tuple containing model input array and latest data
        """
        # Check if price_data is empty
        if not price_data:
            raise ValueError("Price data is empty")

        # Get features from each timeframe
        features_list = []
        latest_data = {}

        for timeframe, df in price_data.items():
            # Ensure we have enough data
            if len(df) < sequence_length:
                logger.warning(f"Not enough data for {timeframe}, need {sequence_length} but got {len(df)}")
                continue

            # Add technical indicators
            df_features = self._add_technical_indicators(df)

            # Normalize features
            df_normalized = self._normalize_features(df_features)

            # Get the last sequence_length rows
            df_sequence = df_normalized.iloc[-sequence_length:]

            # Store latest data for reference
            latest_data[timeframe] = df.iloc[-1:].copy()

            # Add to features list
            features_list.append(df_sequence)

        # Combine features from all timeframes
        if not features_list:
            raise ValueError("No valid data after processing timeframes")

        # Check memory during processing
        self.memory_manager.check_memory_usage()

        # Create a single DataFrame with all features
        combined_df = pd.concat(features_list, axis=1)

        # Convert to numpy array for model input
        sequence_array = combined_df.values

        # Reshape for model input (adding batch dimension)
        sequence_array = np.expand_dims(sequence_array, axis=0)

        # Combine latest data from all timeframes
        latest_combined = pd.concat(latest_data.values(), keys=latest_data.keys())

        return sequence_array, latest_combined

    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators to price data.

        Args:
            df: Price data DataFrame

        Returns:
            pd.DataFrame: DataFrame with technical indicators added
        """
        # Make a copy to avoid modifying the original
        df_with_indicators = df.copy()

        try:
            # Add basic indicators

            # Moving averages
            df_with_indicators['ma7'] = df['close'].rolling(window=7).mean()
            df_with_indicators['ma21'] = df['close'].rolling(window=21).mean()
            df_with_indicators['ma50'] = df['close'].rolling(window=50).mean()

            # Price changes
            df_with_indicators['price_change'] = df['close'].pct_change()
            df_with_indicators['price_change_abs'] = df['close'].pct_change().abs()

            # Volatility
            df_with_indicators['volatility'] = df['close'].pct_change().rolling(window=14).std()

            # Price relative to moving averages
            df_with_indicators['price_ma7_ratio'] = df['close'] / df_with_indicators['ma7']
            df_with_indicators['price_ma21_ratio'] = df['close'] / df_with_indicators['ma21']

            # Price range and volume
            df_with_indicators['range'] = df['high'] - df['low']
            df_with_indicators['range_pct'] = (df['high'] - df['low']) / df['close']

            if 'volume' in df.columns:
                df_with_indicators['volume_change'] = df['volume'].pct_change()
                df_with_indicators['volume_ma7'] = df['volume'].rolling(window=7).mean()
                df_with_indicators['volume_ma7_ratio'] = df['volume'] / df_with_indicators['volume_ma7']

            # Fill NaN values (which occur due to rolling windows)
            df_with_indicators = df_with_indicators.bfill().ffill()

            # If still have NaNs, fill with zeros
            df_with_indicators = df_with_indicators.fillna(0)

            return df_with_indicators

        except Exception as e:
            logger.warning(f"Error adding technical indicators: {str(e)}")
            # If there's an error, return the original data to avoid breaking the pipeline
            return df

    def _normalize_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize features to have similar scale.

        Args:
            df: DataFrame with features

        Returns:
            pd.DataFrame: Normalized DataFrame
        """
        try:
            # Make a copy to avoid modifying the original
            df_normalized = df.copy()

            # Keep the original column names
            columns = df_normalized.columns

            # Z-score normalization (mean=0, std=1)
            for col in columns:
                # Skip time or date columns
                if col == 'time' or df_normalized[col].dtype == 'datetime64[ns]':
                    continue

                # Calculate mean and std (using a rolling window to avoid lookahead bias)
                rolling_mean = df_normalized[col].rolling(window=100, min_periods=10).mean()
                rolling_std = df_normalized[col].rolling(window=100, min_periods=10).std()

                # Replace zeros in std to avoid division by zero
                rolling_std = rolling_std.replace(0, 1)

                # Normalize using rolling stats
                df_normalized[col] = (df_normalized[col] - rolling_mean) / rolling_std

                # Clip extreme values
                df_normalized[col] = df_normalized[col].clip(-3, 3)

            # Fill NaN values that may have been introduced
            df_normalized = df_normalized.fillna(0)

            return df_normalized

        except Exception as e:
            logger.warning(f"Error normalizing features: {str(e)}")
            # If there's an error, return the original data to avoid breaking the pipeline
            return df