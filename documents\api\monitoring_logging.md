# Monitoring and Logging

## Logging System

The trading bot implements comprehensive logging across all components for debugging, monitoring, and audit purposes.

## Enhanced Logging (`utils/enhanced_logging.py`)

### Logging Configuration
```python
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/main.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
```

### Structured Logging
```python
def log_trading_event(event_type: str, data: Dict[str, Any]):
    """Log trading events with structured data."""
    logger.info(
        f"Trading event: {event_type}",
        extra={
            'event_type': event_type,
            'terminal_id': data.get('terminal_id'),
            'symbol': data.get('symbol'),
            'action': data.get('action'),
            'volume': data.get('volume'),
            'price': data.get('price'),
            'timestamp': datetime.now().isoformat()
        }
    )
```

### Log Levels and Usage
- **DEBUG**: Detailed debugging information for development
- **INFO**: General operational information
- **WARNING**: Warning conditions that don't prevent operation
- **ERROR**: Error conditions that may affect functionality
- **CRITICAL**: Critical errors that may cause system failure

## Performance Monitoring (`monitoring/performance.py`)

### PerformanceMonitor Class
```python
class PerformanceMonitor:
    def __init__(self, bot: TradingBot, update_interval: int = 30):
        self.bot = bot
        self.update_interval = update_interval
        self.metrics_history = []
        self.running = False
    
    def start(self):
        """Start performance monitoring."""
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.start()
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.running:
            try:
                metrics = self._collect_metrics()
                self._update_metrics(metrics)
                self._check_alerts(metrics)
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in performance monitoring: {str(e)}")
```

### Metrics Collection
```python
def collect_system_metrics() -> Dict[str, Any]:
    """Collect comprehensive system metrics."""
    import psutil
    
    return {
        'timestamp': datetime.now().isoformat(),
        'cpu_usage': psutil.cpu_percent(interval=1),
        'memory_usage': psutil.virtual_memory().percent,
        'disk_usage': psutil.disk_usage('/').percent,
        'network_io': psutil.net_io_counters()._asdict(),
        'process_count': len(psutil.pids()),
        'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
    }
```

### Trading Metrics
```python
def collect_trading_metrics(bot: TradingBot) -> Dict[str, Any]:
    """Collect trading-specific metrics."""
    return {
        'active_positions': bot.get_active_positions_count(),
        'daily_pnl': bot.get_daily_pnl(),
        'total_trades': bot.get_total_trades_count(),
        'win_rate': bot.get_win_rate(),
        'current_drawdown': bot.get_current_drawdown(),
        'account_balance': bot.get_account_balance(),
        'margin_usage': bot.get_margin_usage(),
        'last_trade_time': bot.get_last_trade_time()
    }
```

## Progress Visualization (`monitoring/progress.py`)

### ProgressVisualizer Class
```python
class ProgressVisualizer:
    def __init__(self, output_dir: str = "monitoring/plots"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.data_history = {}
    
    def update(self, bot_data: Dict[str, Any]):
        """Update visualization with new data."""
        timestamp = datetime.now()
        
        for terminal_id, metrics in bot_data.items():
            if terminal_id not in self.data_history:
                self.data_history[terminal_id] = []
            
            self.data_history[terminal_id].append({
                'timestamp': timestamp,
                **metrics
            })
        
        # Generate updated plots
        self._generate_plots()
```

### Real-time Plotting
```python
def generate_performance_plots(self):
    """Generate real-time performance plots."""
    import matplotlib.pyplot as plt
    import seaborn as sns
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Plot 1: P&L over time
    for terminal_id, history in self.data_history.items():
        timestamps = [entry['timestamp'] for entry in history]
        pnl_values = [entry.get('daily_pnl', 0) for entry in history]
        axes[0, 0].plot(timestamps, pnl_values, label=f'Terminal {terminal_id}')
    
    axes[0, 0].set_title('Daily P&L by Terminal')
    axes[0, 0].legend()
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Plot 2: Win rate comparison
    terminal_ids = list(self.data_history.keys())
    win_rates = [
        self.data_history[tid][-1].get('win_rate', 0) 
        for tid in terminal_ids
    ]
    axes[0, 1].bar(terminal_ids, win_rates)
    axes[0, 1].set_title('Win Rate by Terminal')
    axes[0, 1].set_ylabel('Win Rate')
    
    # Plot 3: Active positions
    active_positions = [
        self.data_history[tid][-1].get('active_positions', 0)
        for tid in terminal_ids
    ]
    axes[1, 0].bar(terminal_ids, active_positions)
    axes[1, 0].set_title('Active Positions by Terminal')
    axes[1, 0].set_ylabel('Number of Positions')
    
    # Plot 4: System resource usage
    cpu_usage = [
        self.data_history[tid][-1].get('cpu_usage', 0)
        for tid in terminal_ids
    ]
    memory_usage = [
        self.data_history[tid][-1].get('memory_usage', 0)
        for tid in terminal_ids
    ]
    
    x = np.arange(len(terminal_ids))
    width = 0.35
    axes[1, 1].bar(x - width/2, cpu_usage, width, label='CPU %')
    axes[1, 1].bar(x + width/2, memory_usage, width, label='Memory %')
    axes[1, 1].set_title('Resource Usage by Terminal')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(terminal_ids)
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig(self.output_dir / f'performance_dashboard_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
    plt.close()
```

## Alert System

### Alert Configuration
```python
ALERT_THRESHOLDS = {
    'max_drawdown': 0.05,      # 5% maximum drawdown
    'daily_loss_limit': 50.0,   # $50 daily loss limit
    'memory_usage': 90.0,       # 90% memory usage
    'cpu_usage': 85.0,          # 85% CPU usage
    'connection_failures': 3,    # 3 consecutive connection failures
    'prediction_errors': 5       # 5 consecutive prediction errors
}
```

### Alert Generation
```python
def check_alerts(metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Check metrics against alert thresholds."""
    alerts = []
    
    # Check drawdown alert
    if metrics.get('current_drawdown', 0) > ALERT_THRESHOLDS['max_drawdown']:
        alerts.append({
            'type': 'DRAWDOWN_ALERT',
            'severity': 'HIGH',
            'message': f"Drawdown exceeded threshold: {metrics['current_drawdown']:.2%}",
            'timestamp': datetime.now().isoformat()
        })
    
    # Check daily loss alert
    if metrics.get('daily_pnl', 0) < -ALERT_THRESHOLDS['daily_loss_limit']:
        alerts.append({
            'type': 'DAILY_LOSS_ALERT',
            'severity': 'HIGH',
            'message': f"Daily loss limit exceeded: ${metrics['daily_pnl']:.2f}",
            'timestamp': datetime.now().isoformat()
        })
    
    # Check system resource alerts
    if metrics.get('memory_usage', 0) > ALERT_THRESHOLDS['memory_usage']:
        alerts.append({
            'type': 'MEMORY_ALERT',
            'severity': 'MEDIUM',
            'message': f"High memory usage: {metrics['memory_usage']:.1f}%",
            'timestamp': datetime.now().isoformat()
        })
    
    return alerts
```

## Log Management

### Log Rotation
```python
import logging.handlers

def setup_rotating_logs():
    """Setup rotating log files."""
    
    # Main application log
    main_handler = logging.handlers.RotatingFileHandler(
        'logs/main.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    
    # Trading-specific log
    trading_handler = logging.handlers.RotatingFileHandler(
        'logs/trading.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10
    )
    
    # Error log
    error_handler = logging.handlers.RotatingFileHandler(
        'logs/errors.log',
        maxBytes=5*1024*1024,   # 5MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    
    # Configure formatters
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    for handler in [main_handler, trading_handler, error_handler]:
        handler.setFormatter(formatter)
    
    return [main_handler, trading_handler, error_handler]
```

### Log Analysis
```python
def analyze_logs(log_file: str, time_range: int = 24) -> Dict[str, Any]:
    """Analyze log files for patterns and issues."""
    
    analysis = {
        'error_count': 0,
        'warning_count': 0,
        'trade_count': 0,
        'connection_issues': 0,
        'performance_issues': 0,
        'top_errors': {},
        'time_range_hours': time_range
    }
    
    cutoff_time = datetime.now() - timedelta(hours=time_range)
    
    with open(log_file, 'r') as f:
        for line in f:
            try:
                # Parse log line
                timestamp_str = line.split(' - ')[0]
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                
                if timestamp < cutoff_time:
                    continue
                
                # Count different types of events
                if 'ERROR' in line:
                    analysis['error_count'] += 1
                    error_msg = line.split('ERROR - ')[-1].strip()
                    analysis['top_errors'][error_msg] = analysis['top_errors'].get(error_msg, 0) + 1
                
                elif 'WARNING' in line:
                    analysis['warning_count'] += 1
                
                elif 'Trading event' in line:
                    analysis['trade_count'] += 1
                
                elif 'connection' in line.lower() and 'failed' in line.lower():
                    analysis['connection_issues'] += 1
                
            except Exception as e:
                continue  # Skip malformed log lines
    
    return analysis
```
