"""
GRU model implementation for time series prediction using PyTorch.
"""
# Standard library imports
import logging
from typing import Dict, Optional, Any, Tuple
import os
from pathlib import Path

# Third-party imports
import numpy as np
from model_adapter import ModelInputAdapter
# Use PyTorch
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler

# Local imports
from .base_model import BaseModel

logger = logging.getLogger(__name__)

# Log which PyTorch version is being used
logger.info(f"GRU Model: Using PyTorch {torch.__version__}")

class GRUNetwork(nn.Module):
    """PyTorch GRU network implementation."""

    def __init__(self, input_dim: int, hidden_units: int, num_layers: int,
                 output_dim: int, dropout_rate: float = 0.2, dense_units: int = 32):
        super(GRUNetwork, self).__init__()

        self.hidden_units = hidden_units
        self.num_layers = num_layers

        # GRU layers
        self.gru = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_units,
            num_layers=num_layers,
            dropout=dropout_rate if num_layers > 1 else 0,
            batch_first=True
        )

        # Dropout layer
        self.dropout = nn.Dropout(dropout_rate)

        # Dense layers
        if dense_units and dense_units > 0:
            self.dense = nn.Linear(hidden_units, dense_units)
            self.dense_activation = nn.ReLU()
            self.dense_dropout = nn.Dropout(dropout_rate)
            self.output_layer = nn.Linear(dense_units, output_dim)
        else:
            self.dense = None
            self.output_layer = nn.Linear(hidden_units, output_dim)

    def forward(self, x):
        # GRU forward pass
        gru_out, _ = self.gru(x)

        # Take the last output from the sequence
        last_output = gru_out[:, -1, :]

        # Apply dropout
        output = self.dropout(last_output)

        # Apply dense layers if configured
        if self.dense is not None:
            output = self.dense(output)
            output = self.dense_activation(output)
            output = self.dense_dropout(output)

        # Final output layer
        output = self.output_layer(output)
        return output

class GRUModel(BaseModel):
    def __init__(self, config: Dict[str, Any]):
        """Initialize GRU model with a configuration dictionary using PyTorch."""
        super().__init__(config) # Pass config to BaseModel constructor

        # Extract parameters from config using .get() with defaults
        # Shared parameters with LSTMModel
        self.sequence_length = self.config.get('sequence_length')
        self.input_dim = self.config.get('input_dim')
        self.output_dim = self.config.get('output_dim', 1)
        self.hidden_units = self.config.get('hidden_units', 64) # GRU hidden units
        self.num_layers = self.config.get('num_layers', 2)
        self.dense_units = self.config.get('dense_units', 32)
        self.dropout_rate = self.config.get('dropout', 0.2)
        self.learning_rate = self.config.get('learning_rate', 0.001)
        self.epochs = self.config.get('epochs', 50)
        self.batch_size = self.config.get('batch_size', 32)
        self.patience = self.config.get('patience', 10)
        # --- Device Configuration ---
        self.device = self._configure_device()

        # Validation
        if self.input_dim is None: raise ValueError(f"Model '{self.model_name}' config missing 'input_dim'.")
        if self.sequence_length is None: raise ValueError(f"Model '{self.model_name}' config missing 'sequence_length'.")

        # Scaler
        self.feature_scaler = MinMaxScaler()
        # self.target_scaler = MinMaxScaler()

        # Model placeholder
        self.model: Optional[GRUNetwork] = None # PyTorch GRU model
        self.optimizer = None
        self.criterion = None

    def _configure_device(self):
        """Configure PyTorch device (GPU/CPU) for model training."""
        use_gpu = self.config.get('use_gpu', True)

        try:
            # Check CUDA availability
            cuda_available = torch.cuda.is_available()
            device_count = torch.cuda.device_count() if cuda_available else 0

            logger.info(f"PyTorch GPU Detection for '{self.model_name}':")
            logger.info(f"  - CUDA available: {cuda_available}")
            logger.info(f"  - GPU devices: {device_count}")

            if cuda_available:
                for i in range(device_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    logger.info(f"  - GPU {i}: {gpu_name}")

            if use_gpu and cuda_available:
                device = torch.device('cuda')
                logger.info(f"PyTorch: Using GPU for '{self.model_name}'")

                # Test GPU functionality
                try:
                    test_tensor = torch.tensor([1.0, 2.0, 3.0]).to(device)
                    result = torch.sum(test_tensor)
                    logger.info(f"GPU test successful for '{self.model_name}': {result.item()}")
                except Exception as gpu_error:
                    logger.error(f"GPU test failed for '{self.model_name}': {gpu_error}")
                    device = torch.device('cpu')
                    logger.warning(f"Falling back to CPU for '{self.model_name}'")

            elif use_gpu:
                logger.warning(f"use_gpu=True for '{self.model_name}' but CUDA not available. Using CPU.")
                logger.error("CRITICAL: No GPU devices found!")
                logger.error("To fix GPU support:")
                logger.error("  1. Install PyTorch with CUDA: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
                logger.error("  2. Verify CUDA installation")
                logger.error("  3. Check GPU drivers")
                device = torch.device('cpu')
            else:
                logger.info(f"use_gpu=False for '{self.model_name}'. Using CPU.")
                device = torch.device('cpu')

            return device

        except Exception as e:
            logger.error(f"Error in device configuration for '{self.model_name}': {str(e)}")
            logger.warning(f"Falling back to CPU for '{self.model_name}'")
            return torch.device('cpu')

    def build(self) -> None:
        """Build the GRU model architecture using PyTorch."""
        try:
            # Create PyTorch GRU model
            self.model = GRUNetwork(
                input_dim=self.input_dim,
                hidden_units=self.hidden_units,
                num_layers=self.num_layers,
                output_dim=self.output_dim,
                dropout_rate=self.dropout_rate,
                dense_units=self.dense_units
            )

            # Move model to device
            self.model = self.model.to(self.device)

            # Setup optimizer
            optimizer_name = self.config.get('optimizer', 'Adam').lower()
            if optimizer_name == 'adam':
                self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            elif optimizer_name == 'rmsprop':
                self.optimizer = optim.RMSprop(self.model.parameters(), lr=self.learning_rate)
            elif optimizer_name == 'sgd':
                self.optimizer = optim.SGD(self.model.parameters(), lr=self.learning_rate)
            else:
                logger.warning(f"Unsupported optimizer '{optimizer_name}' for '{self.model_name}'. Defaulting to Adam.")
                self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)

            # Setup loss function
            loss_name = self.config.get('loss', 'mse').lower()
            if loss_name == 'mse':
                self.criterion = nn.MSELoss()
            elif loss_name == 'mae':
                self.criterion = nn.L1Loss()
            elif loss_name == 'huber':
                self.criterion = nn.HuberLoss()
            else:
                logger.warning(f"Unsupported loss '{loss_name}' for '{self.model_name}'. Defaulting to MSE.")
                self.criterion = nn.MSELoss()

            logger.info(f"PyTorch GRU model ('{self.model_name}') built successfully.")

            # Print model summary
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            logger.info(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
            logger.info(f"Model architecture: {self.model}")

        except Exception as e:
            logger.error(f"Error building PyTorch GRU model '{self.model_name}': {str(e)}", exc_info=True)
            self.model = None
            raise

    # --- Reusing data preparation logic from Keras LSTMModel ---
    def _prepare_data(self, X: np.ndarray, y: Optional[np.ndarray] = None, fit_scaler: bool = False) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Reshape data to (samples, seq_len, features) and scale features using NumPy/Sklearn."""
        try:
            # Use adapter to handle input shape mismatches
            try:
                X = ModelInputAdapter.adapt_input(X, (self.sequence_length, self.input_dim))
                if X is None:
                    raise ValueError(f"Input X shape {X.shape} mismatch for '{self.model_name}'.")
            except Exception as e:
                logger.warning(f"Error adapting input: {str(e)}")
                # Continue with original code to get proper error message

            if X.ndim == 2:
                expected_flat_dim = self.sequence_length * self.input_dim
                if X.shape[1] == expected_flat_dim:
                     X_reshaped = X.reshape(X.shape[0], self.sequence_length, self.input_dim)
                elif self.sequence_length == 1 and X.shape[1] == self.input_dim:
                     X_reshaped = np.expand_dims(X, axis=1)
                else:
                     raise ValueError(f"Input X shape {X.shape} mismatch for '{self.model_name}'.")
            elif X.ndim == 3:
                if X.shape[1] != self.sequence_length or X.shape[2] != self.input_dim:
                    raise ValueError(f"Input X shape {X.shape} mismatch for '{self.model_name}'.")
                X_reshaped = X
            else: raise ValueError(f"Unsupported input X dimensions: {X.ndim}.")

            num_samples, seq_len, num_features = X_reshaped.shape
            X_flat = X_reshaped.reshape(-1, num_features)

            if fit_scaler:
                logger.info(f"Fitting feature scaler on training data of shape {X_flat.shape}")
                X_scaled_flat = self.feature_scaler.fit_transform(X_flat)
            else:
                 if not hasattr(self.feature_scaler, 'scale_') or self.feature_scaler.scale_ is None:
                     raise RuntimeError("Scaler not fitted.")
                 X_scaled_flat = self.feature_scaler.transform(X_flat)
            X_scaled = X_scaled_flat.reshape(num_samples, seq_len, num_features)

            y_processed = None
            if y is not None:
                if y.ndim == 1: y = y.reshape(-1, 1)
                if y.shape[1] != self.output_dim: raise ValueError(f"Target y shape {y.shape} mismatch.")
                y_processed = y # Assuming no target scaling
            return X_scaled, y_processed
        except Exception as e:
            logger.error(f"Error preparing data for PyTorch GRU model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def train(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[tuple] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Train the PyTorch GRU model."""
        try:
            # --- Build Model ---
            if self.model is None:
                logger.info(f"Model '{self.model_name}' not built. Building now...")
                self.build()
            if self.model is None:
                 raise RuntimeError(f"Failed to build PyTorch model '{self.model_name}' before training.")

            # --- Prepare Data ---
            logger.info(f"Preparing training data for '{self.model_name}'...")
            X_train_processed, y_train_processed = self._prepare_data(X, y, fit_scaler=True)

            val_data_processed = None
            if validation_data:
                logger.info(f"Preparing validation data for '{self.model_name}'...")
                X_val, y_val = validation_data
                X_val_processed, y_val_processed = self._prepare_data(X_val, y_val, fit_scaler=False)
                val_data_processed = (X_val_processed, y_val_processed)

            # Convert to PyTorch tensors
            X_train_tensor = torch.FloatTensor(X_train_processed).to(self.device)
            y_train_tensor = torch.FloatTensor(y_train_processed.reshape(-1, 1)).to(self.device)

            if val_data_processed:
                X_val_tensor = torch.FloatTensor(X_val_processed).to(self.device)
                y_val_tensor = torch.FloatTensor(y_val_processed.reshape(-1, 1)).to(self.device)

            # Create data loaders
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)

            if val_data_processed:
                val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
                val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)

            # --- Training Loop ---
            logger.info(f"Starting training for PyTorch GRU model '{self.model_name}' for {self.epochs} epochs...")

            train_losses = []
            val_losses = []
            best_val_loss = float('inf')
            patience_counter = 0

            self.model.train()

            for epoch in range(self.epochs):
                # Training phase
                epoch_train_loss = 0.0
                num_batches = 0

                for batch_X, batch_y in train_loader:
                    self.optimizer.zero_grad()

                    # Forward pass
                    outputs = self.model(batch_X)
                    loss = self.criterion(outputs, batch_y)

                    # Backward pass
                    loss.backward()
                    self.optimizer.step()

                    epoch_train_loss += loss.item()
                    num_batches += 1

                avg_train_loss = epoch_train_loss / num_batches
                train_losses.append(avg_train_loss)

                # Validation phase
                if val_data_processed:
                    self.model.eval()
                    epoch_val_loss = 0.0
                    val_batches = 0

                    with torch.no_grad():
                        for batch_X, batch_y in val_loader:
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y)
                            epoch_val_loss += loss.item()
                            val_batches += 1

                    avg_val_loss = epoch_val_loss / val_batches
                    val_losses.append(avg_val_loss)

                    # Early stopping
                    if avg_val_loss < best_val_loss:
                        best_val_loss = avg_val_loss
                        patience_counter = 0
                        # Save best model state
                        self.best_model_state = self.model.state_dict().copy()
                    else:
                        patience_counter += 1

                    if patience_counter >= self.patience:
                        logger.info(f"Early stopping at epoch {epoch + 1}")
                        # Restore best model
                        if hasattr(self, 'best_model_state'):
                            self.model.load_state_dict(self.best_model_state)
                        break

                    self.model.train()

                    if (epoch + 1) % 10 == 0 or epoch == 0:
                        logger.info(f"Epoch {epoch + 1}/{self.epochs} - Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}")
                else:
                    if (epoch + 1) % 10 == 0 or epoch == 0:
                        logger.info(f"Epoch {epoch + 1}/{self.epochs} - Train Loss: {avg_train_loss:.6f}")

            # Store training history
            self.history = {
                'loss': train_losses,
                'val_loss': val_losses if val_data_processed else []
            }

            logger.info(f"PyTorch GRU model '{self.model_name}' training finished.")
            if val_data_processed and val_losses:
                logger.info(f"Final validation loss: {val_losses[-1]:.6f}")

            return self.history

        except Exception as e:
            logger.error(f"Error training PyTorch GRU model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using the trained PyTorch GRU model with batch processing for memory efficiency."""
        try:
            if self.model is None:
                 raise RuntimeError(f"PyTorch model '{self.model_name}' must be built and loaded before prediction.")

            # Prepare data (use scaler fitted during training)
            X_processed, _ = self._prepare_data(X, fit_scaler=False)

            # Use batch processing for large datasets to avoid GPU memory issues
            batch_size = min(self.batch_size * 4, 1024)  # Use larger batch size for inference
            num_samples = X_processed.shape[0]
            predictions_list = []

            logger.info(f"Making predictions for {num_samples} samples using batch size {batch_size}")

            self.model.eval()
            with torch.no_grad():
                for i in range(0, num_samples, batch_size):
                    end_idx = min(i + batch_size, num_samples)
                    batch_X = X_processed[i:end_idx]

                    # Convert batch to PyTorch tensor
                    X_tensor = torch.FloatTensor(batch_X).to(self.device)

                    # Make predictions for this batch
                    batch_predictions = self.model(X_tensor)
                    predictions_list.append(batch_predictions.cpu().numpy())

                    # Clear GPU cache periodically
                    if i % (batch_size * 10) == 0:
                        torch.cuda.empty_cache()

            # Concatenate all batch predictions
            predictions_scaled = np.concatenate(predictions_list, axis=0)

            # Assuming no target scaling for now
            predictions = predictions_scaled
            if self.config.get('target_scaled_externally', False):
                 logger.warning(f"Target likely scaled externally for '{self.model_name}'. Returning raw model output.")

            logger.info(f"Predictions completed for {num_samples} samples")
            return predictions

        except Exception as e:
            logger.error(f"Error making predictions with PyTorch GRU model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def save_model(self, path: str) -> None:
        """Save the PyTorch GRU model to the specified full path."""
        try:
            if self.model is None:
                raise ValueError(f"PyTorch model '{self.model_name}' has not been built or trained. Cannot save.")

            # Ensure directory exists
            Path(path).parent.mkdir(parents=True, exist_ok=True)

            # Save model state dict in PyTorch format
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict() if self.optimizer else None,
                'model_config': {
                    'input_dim': self.input_dim,
                    'hidden_units': self.hidden_units,
                    'num_layers': self.num_layers,
                    'output_dim': self.output_dim,
                    'dropout_rate': self.dropout_rate,
                    'dense_units': self.dense_units
                }
            }, path)

            # Save the scaler state separately
            scaler_path = f"{path}.scaler.joblib"
            try:
                 import joblib
                 joblib.dump(self.feature_scaler, scaler_path)
                 logger.info(f"Feature scaler state saved to {scaler_path}")
            except ImportError:
                 logger.warning("joblib not installed. Cannot save scaler state.")
            except Exception as scaler_err:
                 logger.error(f"Error saving feature scaler state to {scaler_path}: {scaler_err}")

            logger.info(f"PyTorch GRU model '{self.model_name}' saved to {path}")

        except Exception as e:
            logger.error(f"Error saving PyTorch GRU model '{self.model_name}' to {path}: {str(e)}", exc_info=True)
            raise

    def load_model(self, path: str) -> None:
        """Load the PyTorch GRU model from the specified full path. Sets self.model."""
        try:
            # Check for model file
            if os.path.exists(path):
                logger.info(f"Loading PyTorch model from: {path}")

                # Load checkpoint
                checkpoint = torch.load(path, map_location=self.device)

                # Extract model configuration
                model_config = checkpoint.get('model_config', {})

                # Create model with saved configuration
                self.model = GRUNetwork(
                    input_dim=model_config.get('input_dim', self.input_dim),
                    hidden_units=model_config.get('hidden_units', self.hidden_units),
                    num_layers=model_config.get('num_layers', self.num_layers),
                    output_dim=model_config.get('output_dim', self.output_dim),
                    dropout_rate=model_config.get('dropout_rate', self.dropout_rate),
                    dense_units=model_config.get('dense_units', self.dense_units)
                )

                # Load model state
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.model = self.model.to(self.device)

                # Load optimizer state if available
                if 'optimizer_state_dict' in checkpoint and checkpoint['optimizer_state_dict'] is not None:
                    if self.optimizer is None:
                        # Create optimizer if not already created
                        self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
                    self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

            else:
                raise FileNotFoundError(f"PyTorch GRU model file not found: {path}")

            # Load the scaler state separately
            scaler_path = f"{path}.scaler.joblib"
            if os.path.exists(scaler_path):
                 try:
                      import joblib
                      self.feature_scaler = joblib.load(scaler_path)
                      logger.info(f"Feature scaler state loaded from {scaler_path}")
                 except ImportError:
                      logger.warning("joblib not installed. Cannot load scaler state.")
                 except Exception as scaler_err:
                      logger.error(f"Error loading feature scaler state from {scaler_path}: {scaler_err}. Scaler may need refitting.")
            else:
                 logger.warning(f"Scaler state file not found at {scaler_path}. Scaler will need refitting.")

            logger.info(f"PyTorch GRU model '{self.model_name}' loaded successfully from {path}")

            # Print model summary
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            logger.info(f"Loaded model parameters: {total_params:,} total, {trainable_params:,} trainable")

        except Exception as e:
            logger.error(f"Error loading PyTorch GRU model '{self.model_name}' from {path}: {str(e)}", exc_info=True)
            self.model = None # Ensure model is None on failure
            raise

    # Override _get_model_extension for PyTorch models
    def _get_model_extension(self) -> str:
        """
        Return the appropriate file extension for the model.
        This is used by BaseModel.save to construct the full path.
        """
        return ".pt"  # Standard PyTorch extension