# Data Collection Setup

## Overview

The system collects historical data from 5 MT5 terminals for multiple timeframes and symbols.

## Data Collection Scripts

### Primary Collection Script: `collect_historical_data.py`

Collects historical OHLCV data with technical indicators:

```python
def collect_data_from_terminal(
    terminal_id: str,
    symbol: str,
    timeframe: str,
    years: int = 3
) -> bool
```

### Supported Symbols
- **BTCUSD.a**: Primary trading symbol
- Additional symbols can be configured in `config.json`

### Supported Timeframes
- **M5**: 5-minute bars
- **M15**: 15-minute bars  
- **M30**: 30-minute bars
- **H1**: 1-hour bars
- **H4**: 4-hour bars

## Data Storage Structure

### Directory Structure
```
data/
├── storage/
│   └── historical/
│       ├── terminal_1/
│       │   ├── BTCUSD.a_M5/
│       │   │   └── 2021-01-01_2024-01-01_terminal_1_20240101_120000.parquet
│       │   ├── BTCUSD.a_M15/
│       │   ├── BTCUSD.a_M30/
│       │   ├── BTCUSD.a_H1/
│       │   └── BTCUSD.a_H4/
│       ├── terminal_2/
│       ├── terminal_3/
│       ├── terminal_4/
│       └── terminal_5/
├── processed/
│   ├── train/
│   ├── validation/
│   └── test/
└── cache/
    ├── terminal_1/
    ├── terminal_2/
    ├── terminal_3/
    ├── terminal_4/
    └── terminal_5/
```

### File Naming Convention
```
{symbol}_{timeframe}_{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
```

Example: `BTCUSD.a_M5_2021-01-01_2024-01-01_terminal_1_20240101_120000.parquet`

## Data Collection Process

### 1. Terminal Initialization
```bash
python scripts/open_mt5_terminals.py
```

### 2. Data Collection Execution
```bash
# Collect from all terminals
python collect_from_all_terminals.py

# Collect specific symbol/timeframe
python collect_historical_data.py --symbol BTCUSD.a --timeframe M5 --years 3
```

### 3. Data Validation
```bash
python data/preprocessor.py --validate
```

## Data Features

### OHLCV Data
- **Open**: Opening price
- **High**: Highest price
- **Low**: Lowest price
- **Close**: Closing price
- **Volume**: Tick volume

### Technical Indicators
- **RSI**: Relative Strength Index
- **MACD**: Moving Average Convergence Divergence
- **Bollinger Bands**: Upper and lower bands
- **ATR**: Average True Range
- **SMA**: Simple Moving Averages (20, 50)

### Data Quality Checks
- Missing data detection
- Outlier identification
- Data consistency validation
- Timestamp continuity checks

## Data Preprocessing (`data/preprocessor.py`)

### Preprocessing Pipeline
1. **Data cleaning**: Remove invalid data points
2. **Feature engineering**: Calculate technical indicators
3. **Normalization**: Scale features appropriately
4. **Sequence creation**: Create sequences for time series models
5. **Train/validation/test split**: Split data for model training

### Preprocessing Configuration
```python
PREPROCESSING_CONFIG = {
    "sequence_length": 60,
    "validation_split": 0.2,
    "test_split": 0.1,
    "feature_scaling": "standard",
    "handle_missing": "interpolate"
}
```

## Data Manager (`data/data_manager.py`)

### Data Loading
```python
class DataManager:
    def load_historical_data(
        self,
        symbol: str,
        timeframe: str,
        terminal_id: str = None
    ) -> pd.DataFrame
    
    def load_processed_data(
        self,
        symbol: str,
        timeframe: str,
        split: str = "train"
    ) -> pd.DataFrame
```

### Data Caching
- Intelligent caching system
- Memory-efficient data loading
- Automatic cache invalidation
- Cache size management

## Collection Monitoring

### Progress Tracking
- Real-time collection progress
- Data point counting
- Error tracking
- Performance metrics

### Validation Reports
- Data quality reports
- Coverage analysis
- Missing data reports
- Statistical summaries

## Troubleshooting

### Common Issues
1. **MT5 Connection Failed**: Check terminal status and credentials
2. **Insufficient Data**: Verify symbol availability and market hours
3. **Storage Space**: Ensure adequate disk space
4. **Memory Issues**: Monitor memory usage during collection

### Data Recovery
- Automatic retry mechanisms
- Partial data recovery
- Data integrity checks
- Backup and restore procedures
