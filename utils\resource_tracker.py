"""
Resource Tracker - Centralized system for tracking and managing application resources.
Ensures proper cleanup of resources to prevent leaks.
"""

import threading
import weakref
import logging
import gc
import time
from typing import Dict, Set, Any, Callable, Optional, List, Tuple
from datetime import datetime
import sys

logger = logging.getLogger(__name__)

class ResourceTracker:
    """
    Tracks and manages application resources to prevent leaks.
    
    This class provides a centralized way to track resources such as
    file handles, network connections, GPU memory, and other objects
    that need explicit cleanup. It ensures proper cleanup of resources
    even in the presence of exceptions or unexpected termination.
    """
    
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls):
        """Get the singleton instance of ResourceTracker."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """Initialize the resource tracker."""
        self._resources: Dict[str, Set[weakref.ref]] = {}
        self._cleanup_handlers: Dict[str, Callable[[Any], None]] = {}
        self._resource_creation_time: Dict[str, Dict[int, datetime]] = {}
        self._cleanup_scheduled = False
        self._monitor_thread = None
        self._stop_monitor = threading.Event()
        
        # Statistics
        self._creation_counts: Dict[str, int] = {}
        self._cleanup_counts: Dict[str, int] = {}
        
        logger.info("Resource tracker initialized")
        
        # Register exit handler
        self._register_exit_handler()
    
    def _register_exit_handler(self):
        """Register an exit handler to clean up resources on interpreter shutdown."""
        import atexit
        atexit.register(self.cleanup_all)
    
    def register(self, resource_type: str, resource: Any):
        """
        Register a resource for tracking.
        
        Args:
            resource_type: Type of the resource (e.g., 'file', 'connection', 'model')
            resource: The resource to track
        """
        if resource is None:
            return
            
        # Create a weak reference to the resource
        resource_id = id(resource)
        weak_ref = weakref.ref(resource, lambda ref: self._on_resource_collected(resource_type, resource_id))
        
        with self._lock:
            # Initialize containers if needed
            if resource_type not in self._resources:
                self._resources[resource_type] = set()
                self._resource_creation_time[resource_type] = {}
                self._creation_counts[resource_type] = 0
                self._cleanup_counts[resource_type] = 0
            
            # Add the weak reference
            self._resources[resource_type].add(weak_ref)
            self._resource_creation_time[resource_type][resource_id] = datetime.now()
            self._creation_counts[resource_type] += 1
            
            logger.debug(f"Registered {resource_type} resource (id: {resource_id})")
            
            # Periodically clean up expired references
            if len(self._resources[resource_type]) % 10 == 0:  # Check every 10 resources
                self._cleanup_expired_refs(resource_type)
            
            # Start monitoring if not already running
            if not self._cleanup_scheduled:
                self._schedule_cleanup()
    
    def _on_resource_collected(self, resource_type: str, resource_id: int):
        """
        Called when a weak reference is garbage collected.
        
        Args:
            resource_type: Type of the resource
            resource_id: ID of the resource
        """
        with self._lock:
            if resource_type in self._resource_creation_time:
                if resource_id in self._resource_creation_time[resource_type]:
                    del self._resource_creation_time[resource_type][resource_id]
                    logger.debug(f"{resource_type} resource (id: {resource_id}) was garbage collected")
    
    def register_cleanup_handler(self, resource_type: str, handler: Callable[[Any], None]):
        """
        Register a cleanup handler for a resource type.
        
        Args:
            resource_type: Type of the resource
            handler: Function to call to clean up the resource
        """
        with self._lock:
            self._cleanup_handlers[resource_type] = handler
            logger.info(f"Registered cleanup handler for {resource_type} resources")
    
    def _cleanup_expired_refs(self, resource_type: str):
        """
        Clean up expired weak references.
        
        Args:
            resource_type: Type of the resource
        """
        with self._lock:
            if resource_type in self._resources:
                active = {ref for ref in self._resources[resource_type] if ref() is not None}
                expired = len(self._resources[resource_type]) - len(active)
                if expired > 0:
                    logger.debug(f"Cleaned up {expired} expired {resource_type} references")
                self._resources[resource_type] = active
    
    def cleanup_resource(self, resource_type: str, resource: Any) -> bool:
        """
        Clean up a specific resource.
        
        Args:
            resource_type: Type of the resource
            resource: The resource to clean up
            
        Returns:
            bool: True if cleanup was successful, False otherwise
        """
        if resource is None:
            return False
            
        with self._lock:
            # Find and remove the weak reference
            if resource_type in self._resources:
                resource_id = id(resource)
                refs_to_remove = []
                
                for ref in self._resources[resource_type]:
                    if ref() is not None and id(ref()) == resource_id:
                        refs_to_remove.append(ref)
                
                for ref in refs_to_remove:
                    self._resources[resource_type].remove(ref)
                
                # Remove from creation time tracking
                if resource_type in self._resource_creation_time and resource_id in self._resource_creation_time[resource_type]:
                    del self._resource_creation_time[resource_type][resource_id]
            
            # Call the cleanup handler if registered
            if resource_type in self._cleanup_handlers:
                try:
                    self._cleanup_handlers[resource_type](resource)
                    self._cleanup_counts[resource_type] += 1
                    logger.debug(f"Cleaned up {resource_type} resource (id: {resource_id})")
                    return True
                except Exception as e:
                    logger.error(f"Error cleaning up {resource_type} resource: {e}")
                    return False
            
            return False
    
    def cleanup_by_type(self, resource_type: str) -> int:
        """
        Clean up all resources of a specific type.
        
        Args:
            resource_type: Type of the resources to clean up
            
        Returns:
            int: Number of resources cleaned up
        """
        cleaned_count = 0
        
        with self._lock:
            if resource_type in self._resources:
                refs = list(self._resources[resource_type])
                
                for ref in refs:
                    resource = ref()
                    if resource is not None:
                        if self.cleanup_resource(resource_type, resource):
                            cleaned_count += 1
                
                # Clear the resources
                self._resources[resource_type] = set()
        
        logger.info(f"Cleaned up {cleaned_count} {resource_type} resources")
        return cleaned_count
    
    def cleanup_all(self):
        """Clean up all tracked resources."""
        logger.info("Cleaning up all tracked resources")
        
        with self._lock:
            # Stop the monitor thread if running
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._stop_monitor.set()
                self._monitor_thread.join(timeout=5)
            
            # Clean up resources by type
            total_cleaned = 0
            for resource_type in list(self._resources.keys()):
                cleaned = self.cleanup_by_type(resource_type)
                total_cleaned += cleaned
            
            # Clear resource tracking
            self._resources.clear()
            self._resource_creation_time.clear()
            
            # Force garbage collection
            gc.collect()
            
            logger.info(f"Cleaned up {total_cleaned} resources total")
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """
        Get statistics about tracked resources.
        
        Returns:
            Dict[str, Any]: Resource statistics
        """
        with self._lock:
            stats = {
                "resource_counts": {},
                "creation_counts": dict(self._creation_counts),
                "cleanup_counts": dict(self._cleanup_counts),
                "leaked_resources": {},
                "resource_ages": {}
            }
            
            for resource_type in self._resources:
                # Count active resources
                active_resources = [ref() for ref in self._resources[resource_type] if ref() is not None]
                stats["resource_counts"][resource_type] = len(active_resources)
                
                # Calculate leak rate
                if self._creation_counts[resource_type] > 0:
                    created = self._creation_counts[resource_type]
                    cleaned = self._cleanup_counts[resource_type]
                    leaked = created - cleaned - len(active_resources)
                    stats["leaked_resources"][resource_type] = leaked
                
                # Calculate resource ages
                current_time = datetime.now()
                ages = []
                
                for resource_id, creation_time in self._resource_creation_time[resource_type].items():
                    age_seconds = (current_time - creation_time).total_seconds()
                    ages.append(age_seconds)
                
                if ages:
                    stats["resource_ages"][resource_type] = {
                        "min": min(ages),
                        "max": max(ages),
                        "avg": sum(ages) / len(ages)
                    }
            
            return stats
    
    def _schedule_cleanup(self):
        """Schedule periodic cleanup of resources."""
        with self._lock:
            if self._cleanup_scheduled:
                return
            
            self._cleanup_scheduled = True
            self._stop_monitor = threading.Event()
            self._monitor_thread = threading.Thread(
                target=self._monitor_resources,
                daemon=True,
                name="ResourceMonitor"
            )
            self._monitor_thread.start()
    
    def _monitor_resources(self):
        """Monitor and periodically clean up resources."""
        while not self._stop_monitor.is_set():
            try:
                # Wait for a period
                for _ in range(60):  # Check every minute
                    if self._stop_monitor.is_set():
                        break
                    time.sleep(1)
                
                if self._stop_monitor.is_set():
                    break
                
                # Identify and clean up stale resources
                stale_resources = self._find_stale_resources()
                if stale_resources:
                    logger.info(f"Found {sum(len(res) for res in stale_resources.values())} stale resources")
                    for resource_type, resources in stale_resources.items():
                        for resource in resources:
                            self.cleanup_resource(resource_type, resource)
                
                # Log resource statistics periodically
                stats = self.get_resource_stats()
                
                # Only log if we have active resources
                if any(count > 0 for count in stats["resource_counts"].values()):
                    logger.info(
                        f"Resource statistics: "
                        f"Active: {stats['resource_counts']}, "
                        f"Created: {sum(stats['creation_counts'].values())}, "
                        f"Cleaned: {sum(stats['cleanup_counts'].values())}"
                    )
                
                # Check for potential leaks
                for resource_type, leaked in stats.get("leaked_resources", {}).items():
                    if leaked > 10:  # Threshold for considering leaks
                        logger.warning(f"Potential {resource_type} resource leak detected: {leaked} resources unaccounted for")
                
            except Exception as e:
                logger.error(f"Error in resource monitor: {e}")
    
    def _find_stale_resources(self) -> Dict[str, List[Any]]:
        """
        Find stale resources that should be cleaned up.
        
        Returns:
            Dict[str, List[Any]]: Dictionary mapping resource types to lists of stale resources
        """
        stale_resources = {}
        current_time = datetime.now()
        
        with self._lock:
            for resource_type in self._resources:
                stale_for_type = []
                
                # Resource-type specific age thresholds
                age_threshold = {
                    'file': 300,  # 5 minutes
                    'connection': 600,  # 10 minutes
                    'model': 1800,  # 30 minutes
                    'thread': 300,  # 5 minutes
                    'database': 900,  # 15 minutes
                }.get(resource_type, 3600)  # Default: 1 hour
                
                # Find resources older than the threshold
                for ref in self._resources[resource_type]:
                    resource = ref()
                    if resource is not None:
                        resource_id = id(resource)
                        if (resource_id in self._resource_creation_time[resource_type] and
                            (current_time - self._resource_creation_time[resource_type][resource_id]).total_seconds() > age_threshold):
                            stale_for_type.append(resource)
                
                if stale_for_type:
                    stale_resources[resource_type] = stale_for_type
        
        return stale_resources
    
    def register_mt5_resource(self, terminal_id: str):
        """
        Register an MT5 terminal connection.
        
        Args:
            terminal_id: ID of the MT5 terminal
        """
        # Create a proxy object to track the MT5 connection
        class MT5ResourceProxy:
            def __init__(self, terminal_id):
                self.terminal_id = terminal_id
                self.created_at = datetime.now()
                
            def __str__(self):
                return f"MT5Connection(terminal_id={self.terminal_id})"
        
        proxy = MT5ResourceProxy(terminal_id)
        self.register('mt5_connection', proxy)
    
    def register_model(self, model_name: str, model_instance: Any):
        """
        Register a machine learning model.
        
        Args:
            model_name: Name of the model
            model_instance: The model instance
        """
        # Create a proxy with the model name
        class ModelResourceProxy:
            def __init__(self, name, instance):
                self.name = name
                self.instance = instance
                self.created_at = datetime.now()
                
            def __str__(self):
                return f"Model(name={self.name})"
        
        proxy = ModelResourceProxy(model_name, model_instance)
        self.register('model', proxy)
    
    def register_thread(self, thread: threading.Thread, name: str = None):
        """
        Register a thread for tracking.
        
        Args:
            thread: The thread to track
            name: Optional name for the thread
        """
        # Create a proxy with the thread info
        class ThreadResourceProxy:
            def __init__(self, thread, name):
                self.thread = thread
                self.name = name or thread.name
                self.created_at = datetime.now()
                
            def __str__(self):
                return f"Thread(name={self.name}, alive={self.thread.is_alive()})"
        
        proxy = ThreadResourceProxy(thread, name)
        self.register('thread', proxy) 