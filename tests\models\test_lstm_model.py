"""
Test script for LSTM model with comprehensive evaluation.
"""
import sys
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Tuple
from bayes_opt import BayesianOptimization
from sklearn.model_selection import TimeSeriesSplit

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Local imports with error handling
try:
    from models.lstm_model import LSTMModel
    from .base_test import BaseModelTest, model_config
except ImportError as e:
    print(f"Import error: {str(e)}")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lstm_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LSTMTest(BaseModelTest):
    """Test class for LSTM model with comprehensive evaluation."""

    def __init__(self):
        """Initialize LSTM test class."""
        super().__init__('lstm')
        self.model = None

    def prepare_sequences(self, X: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM model."""
        try:
            X_sequences = []
            y_sequences = []

            for i in range(len(X) - sequence_length):
                X_sequences.append(X[i:(i + sequence_length)])
                y_sequences.append(X[i + sequence_length, 3])  # Using close price as target

            X_sequences = np.array(X_sequences, dtype=np.float32)
            y_sequences = np.array(y_sequences, dtype=np.float32)

            self.logger.info(f"Prepared {len(X_sequences)} sequences of length {sequence_length}")
            return X_sequences, y_sequences

        except Exception as e:
            self.logger.error(f"Error preparing sequences: {str(e)}")
            raise

    def optimize_hyperparameters(self, X_train: np.ndarray, y_train: np.ndarray,
                               X_val: np.ndarray, y_val: np.ndarray) -> Dict:
        """Optimize model hyperparameters using Bayesian optimization."""
        try:
            def objective(units, dropout_rate, learning_rate, batch_size):
                # Create model with current parameters
                params = {
                    'units': int(units),
                    'dropout_rate': dropout_rate,
                    'learning_rate': learning_rate,
                    'batch_size': int(batch_size)
                }

                model = LSTMModel(model_config)
                model.build()
                model.set_model_params(params)

                # Train and evaluate
                metrics = model.train(X_train, y_train, X_val, y_val)
                return -metrics['rmse']  # Minimize RMSE

            # Define parameter bounds
            pbounds = {
                'units': (32, 256),
                'dropout_rate': (0.1, 0.5),
                'learning_rate': (0.001, 0.01),
                'batch_size': (16, 128)
            }

            # Run optimization
            optimizer = BayesianOptimization(
                f=objective,
                pbounds=pbounds,
                random_state=42
            )

            optimizer.maximize(
                init_points=5,
                n_iter=20
            )

            # Get best parameters
            best_params = optimizer.max['params']
            best_params['units'] = int(best_params['units'])
            best_params['batch_size'] = int(best_params['batch_size'])

            self.logger.info(f"Best hyperparameters: {best_params}")
            return best_params

        except Exception as e:
            self.logger.error(f"Error optimizing hyperparameters: {str(e)}")
            raise

    def test_model(self):
        """Test the LSTM model with comprehensive evaluation."""
        try:
            # Load and preprocess data
            data_path = project_root / "data" / "terminal_1" / "M5_data.csv"
            X, _ = self.load_and_preprocess_data(data_path)

            # Prepare sequences
            sequence_length = model_config.SEQUENCE_LENGTH
            X_sequences, y_sequences = self.prepare_sequences(X, sequence_length)

            # Initialize cross-validation
            tscv = TimeSeriesSplit(n_splits=5)

            # Store metrics across folds
            self.all_metrics = []

            for fold, (train_idx, test_idx) in enumerate(tscv.split(X_sequences)):
                self.logger.info(f"\nProcessing fold {fold + 1}/5")

                # Split data
                X_train, X_test = X_sequences[train_idx], X_sequences[test_idx]
                y_train, y_test = y_sequences[train_idx], y_sequences[test_idx]

                # Further split for validation
                val_size = int(len(X_train) * 0.2)
                X_train, X_val = X_train[:-val_size], X_train[-val_size:]
                y_train, y_val = y_train[:-val_size], y_train[-val_size:]

                # Optimize hyperparameters
                best_params = self.optimize_hyperparameters(X_train, y_train, X_val, y_val)

                # Initialize and train model
                self.model = LSTMModel(model_config)
                self.model.build()
                self.model.set_model_params(best_params)

                # Monitor resources
                self.monitor_gpu_usage()
                self.monitor_memory_usage()

                # Train model
                train_metrics = self.model.train(X_train, y_train, X_val, y_val)
                self.logger.info(f"Training metrics: {train_metrics}")

                # Make predictions
                y_pred = self.model.predict(X_test)

                # Calculate metrics
                metrics = self.calculate_metrics(y_test, y_pred)
                self.all_metrics.append(metrics)
                self.logger.info(f"Test metrics: {metrics}")

                # Plot results
                self.plot_predictions(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Actual vs Predicted Values",
                    save_path=self.output_dir / f"fold_{fold}_predictions.png"
                )

                self.plot_residuals(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Prediction Residuals",
                    save_path=self.output_dir / f"fold_{fold}_residuals.png"
                )

                self.plot_error_distribution(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Error Distribution",
                    save_path=self.output_dir / f"fold_{fold}_error_dist.png"
                )

            # Calculate and plot average metrics
            avg_metrics = pd.DataFrame(self.all_metrics).mean().to_dict()
            self.plot_metrics(
                avg_metrics,
                title="Average Model Evaluation Metrics",
                save_path=self.output_dir / "average_metrics.png"
            )

            # Generate comprehensive report
            self.generate_report()

            self.logger.info("LSTM model test completed successfully")

        except Exception as e:
            self.logger.error(f"Error in LSTM model test: {str(e)}")
            raise

if __name__ == "__main__":
    test = LSTMTest()
    test.test_model()