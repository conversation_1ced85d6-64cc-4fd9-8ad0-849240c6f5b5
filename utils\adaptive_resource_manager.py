"""
Adaptive Resource Management System.

This module provides mechanisms for dynamically adjusting resource usage
based on system capabilities and workload. It monitors system resources
and adapts application behavior to optimize performance and reliability.

Key features:
1. Dynamic resource allocation based on system load
2. Workload-aware throttling and prioritization
3. Automatic scaling of concurrent operations
4. Integration with memory manager and circuit breaker
5. Resource usage prediction and preemptive adaptation
"""

import os
import sys
import time
import logging
import threading
import psutil
import platform
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import random

# Configure logging
logger = logging.getLogger(__name__)

class ResourceType(Enum):
    """Types of resources that can be managed."""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"
    NETWORK = "network"
    CONNECTIONS = "connections"
    THREADS = "threads"

class ResourcePriority(Enum):
    """Priority levels for resource allocation."""
    CRITICAL = 1  # Must have resources
    HIGH = 2      # Important operations
    MEDIUM = 3    # Normal operations
    LOW = 4       # Background operations
    IDLE = 5      # Only when system is idle

class AdaptationStrategy(Enum):
    """Strategies for adapting resource usage."""
    THROTTLE = "throttle"       # Slow down operations
    LIMIT = "limit"             # Limit concurrent operations
    SCHEDULE = "schedule"       # Delay operations
    OFFLOAD = "offload"         # Move to background
    COMPRESS = "compress"       # Use more CPU to save memory
    CACHE = "cache"             # Cache results to avoid recomputation

@dataclass
class ResourceUsage:
    """Current resource usage information."""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    disk_percent: float = 0.0
    network_bytes_sent: int = 0
    network_bytes_recv: int = 0
    connection_count: int = 0
    thread_count: int = 0
    timestamp: datetime = field(default_factory=datetime.now)

    def is_cpu_high(self, threshold: float = 80.0) -> bool:
        """Check if CPU usage is high."""
        return self.cpu_percent >= threshold

    def is_memory_high(self, threshold: float = 80.0) -> bool:
        """Check if memory usage is high."""
        return self.memory_percent >= threshold

    def is_disk_high(self, threshold: float = 80.0) -> bool:
        """Check if disk usage is high."""
        return self.disk_percent >= threshold

@dataclass
class ResourceLimits:
    """Resource limits for adaptive management."""
    max_cpu_percent: float = 90.0
    max_memory_percent: float = 90.0
    max_disk_percent: float = 90.0
    max_connections: int = 100
    max_threads: int = 50
    max_concurrent_operations: Dict[str, int] = field(default_factory=dict)

@dataclass
class ResourceRequest:
    """Request for resources from a component."""
    component_name: str
    resource_type: ResourceType
    amount: float  # Percentage or absolute value
    priority: ResourcePriority
    operation_id: Optional[str] = None
    timeout_seconds: Optional[float] = None
    callback: Optional[Callable] = None

class AdaptiveResourceManager:
    """
    Adaptive resource management system that dynamically adjusts resource
    usage based on system capabilities and workload.
    """

    _instance = None
    _lock = threading.RLock()

    @classmethod
    def get_instance(cls):
        """Get the singleton instance."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the adaptive resource manager."""
        self._resource_usage_history: List[ResourceUsage] = []
        self._resource_limits = ResourceLimits()
        self._component_usage: Dict[str, Dict[ResourceType, float]] = {}
        self._operation_semaphores: Dict[str, threading.Semaphore] = {}
        self._active_operations: Dict[str, int] = {}
        self._resource_callbacks: Dict[ResourceType, List[Callable]] = {
            resource_type: [] for resource_type in ResourceType
        }

        # Integration with other components
        self._memory_manager = None
        self._circuit_breaker_registry = None
        self._graceful_degradation = None

        # Monitoring thread
        self._monitoring_thread = None
        self._monitoring_interval = 5.0  # seconds
        self._should_stop = threading.Event()

        # Initialize operation semaphores
        self._initialize_semaphores()

        # Start monitoring thread
        self._start_monitoring()

        logger.info("Adaptive resource manager initialized")

    def _initialize_semaphores(self) -> None:
        """Initialize operation semaphores."""
        # Default operation types and their limits
        default_operations = {
            "mt5_query": 10,
            "mt5_trade": 5,
            "data_processing": 20,
            "network_request": 15,
            "background_task": 30
        }

        for operation, limit in default_operations.items():
            self._operation_semaphores[operation] = threading.Semaphore(limit)
            self._active_operations[operation] = 0
            self._resource_limits.max_concurrent_operations[operation] = limit

    def _start_monitoring(self) -> None:
        """Start the resource monitoring thread."""
        if self._monitoring_thread is not None and self._monitoring_thread.is_alive():
            return

        self._should_stop.clear()
        self._monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="AdaptiveResourceMonitor"
        )
        self._monitoring_thread.start()
        logger.info("Resource monitoring thread started")

    def _stop_monitoring(self) -> None:
        """Stop the resource monitoring thread."""
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            return

        self._should_stop.set()
        self._monitoring_thread.join(timeout=2.0)
        logger.info("Resource monitoring thread stopped")

    def _monitoring_loop(self) -> None:
        """Main monitoring loop that collects resource usage data."""
        while not self._should_stop.is_set():
            try:
                # Collect resource usage
                usage = self._collect_resource_usage()

                # Store in history
                with self._lock:
                    self._resource_usage_history.append(usage)

                    # Keep only recent history (last hour)
                    one_hour_ago = datetime.now() - timedelta(hours=1)
                    self._resource_usage_history = [
                        u for u in self._resource_usage_history
                        if u.timestamp >= one_hour_ago
                    ]

                # Check for resource pressure and adapt if needed
                self._check_and_adapt(usage)

                # Wait for next monitoring interval
                self._should_stop.wait(self._monitoring_interval)

            except Exception as e:
                logger.error(f"Error in resource monitoring loop: {str(e)}")
                # Wait a bit before retrying
                self._should_stop.wait(10.0)

    def _collect_resource_usage(self) -> ResourceUsage:
        """Collect current resource usage information."""
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Get disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent

            # Get network usage
            net_io = psutil.net_io_counters()
            network_bytes_sent = net_io.bytes_sent
            network_bytes_recv = net_io.bytes_recv

            # Get connection count
            try:
                connections = len(psutil.net_connections())
            except (psutil.AccessDenied, PermissionError):
                # This may require admin privileges on some systems
                connections = 0

            # Get thread count
            thread_count = threading.active_count()

            return ResourceUsage(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv,
                connection_count=connections,
                thread_count=thread_count
            )

        except Exception as e:
            logger.error(f"Error collecting resource usage: {str(e)}")
            # Return default values
            return ResourceUsage()

    def _check_and_adapt(self, usage: ResourceUsage) -> None:
        """
        Check resource usage and adapt if needed.

        Args:
            usage: Current resource usage
        """
        # Check CPU usage
        if usage.is_cpu_high(self._resource_limits.max_cpu_percent * 0.8):
            logger.warning(f"High CPU usage detected: {usage.cpu_percent:.1f}%")
            self._adapt_for_high_cpu(usage)

        # Check memory usage
        if usage.is_memory_high(self._resource_limits.max_memory_percent * 0.8):
            logger.warning(f"High memory usage detected: {usage.memory_percent:.1f}%")
            self._adapt_for_high_memory(usage)

        # Check disk usage
        if usage.is_disk_high(self._resource_limits.max_disk_percent * 0.8):
            logger.warning(f"High disk usage detected: {usage.disk_percent:.1f}%")
            self._adapt_for_high_disk(usage)

        # Check thread count
        if usage.thread_count > self._resource_limits.max_threads * 0.8:
            logger.warning(f"High thread count detected: {usage.thread_count}")
            self._adapt_for_high_threads(usage)

    def _adapt_for_high_cpu(self, usage: ResourceUsage) -> None:
        """
        Adapt for high CPU usage.

        Args:
            usage: Current resource usage
        """
        # Reduce concurrent operations
        self._reduce_concurrent_operations(ResourceType.CPU)

        # Notify callbacks
        self._notify_resource_callbacks(ResourceType.CPU, usage)

        # Integrate with other components
        if self._graceful_degradation:
            try:
                from utils.graceful_degradation import ServiceLevel, DegradationReason
                if usage.cpu_percent > self._resource_limits.max_cpu_percent * 0.9:
                    self._graceful_degradation.set_service_level(
                        ServiceLevel.MINIMAL, DegradationReason.HIGH_LOAD
                    )
                elif usage.cpu_percent > self._resource_limits.max_cpu_percent * 0.8:
                    self._graceful_degradation.set_service_level(
                        ServiceLevel.REDUCED, DegradationReason.HIGH_LOAD
                    )
            except Exception as e:
                logger.error(f"Error integrating with graceful degradation: {str(e)}")

    def _adapt_for_high_memory(self, usage: ResourceUsage) -> None:
        """
        Adapt for high memory usage.

        Args:
            usage: Current resource usage
        """
        # Try to free memory if memory manager is available
        if self._memory_manager:
            try:
                if usage.memory_percent > self._resource_limits.max_memory_percent * 0.9:
                    self._memory_manager.cleanup_memory("AGGRESSIVE")
                elif usage.memory_percent > self._resource_limits.max_memory_percent * 0.8:
                    self._memory_manager.cleanup_memory("MODERATE")
            except Exception as e:
                logger.error(f"Error cleaning up memory: {str(e)}")

        # Reduce concurrent operations
        self._reduce_concurrent_operations(ResourceType.MEMORY)

        # Notify callbacks
        self._notify_resource_callbacks(ResourceType.MEMORY, usage)

    def _adapt_for_high_disk(self, usage: ResourceUsage) -> None:
        """
        Adapt for high disk usage.

        Args:
            usage: Current resource usage
        """
        # Reduce concurrent operations
        self._reduce_concurrent_operations(ResourceType.DISK)

        # Notify callbacks
        self._notify_resource_callbacks(ResourceType.DISK, usage)

    def _adapt_for_high_threads(self, usage: ResourceUsage) -> None:
        """
        Adapt for high thread count.

        Args:
            usage: Current resource usage
        """
        # Reduce concurrent operations
        self._reduce_concurrent_operations(ResourceType.THREADS)

        # Notify callbacks
        self._notify_resource_callbacks(ResourceType.THREADS, usage)

    def _reduce_concurrent_operations(self, resource_type: ResourceType) -> None:
        """
        Reduce concurrent operations for a resource type.

        Args:
            resource_type: Type of resource to reduce
        """
        with self._lock:
            # Determine which operations to reduce based on resource type
            operations_to_reduce = []

            if resource_type == ResourceType.CPU:
                operations_to_reduce = ["data_processing", "background_task"]
            elif resource_type == ResourceType.MEMORY:
                operations_to_reduce = ["data_processing", "mt5_query"]
            elif resource_type == ResourceType.DISK:
                operations_to_reduce = ["background_task"]
            elif resource_type == ResourceType.THREADS:
                operations_to_reduce = ["background_task", "network_request"]

            # Reduce limits for selected operations
            for operation in operations_to_reduce:
                if operation in self._resource_limits.max_concurrent_operations:
                    current_limit = self._resource_limits.max_concurrent_operations[operation]
                    new_limit = max(1, int(current_limit * 0.7))  # Reduce by 30%

                    if new_limit < current_limit:
                        logger.info(f"Reducing {operation} limit from {current_limit} to {new_limit}")
                        self._resource_limits.max_concurrent_operations[operation] = new_limit

                        # Create new semaphore with reduced limit
                        # Note: This won't affect operations that have already acquired the semaphore
                        self._operation_semaphores[operation] = threading.Semaphore(new_limit)

    def _notify_resource_callbacks(self, resource_type: ResourceType, usage: ResourceUsage) -> None:
        """
        Notify callbacks for a resource type.

        Args:
            resource_type: Type of resource
            usage: Current resource usage
        """
        callbacks = self._resource_callbacks.get(resource_type, [])
        for callback in callbacks:
            try:
                callback(resource_type, usage)
            except Exception as e:
                logger.error(f"Error in resource callback: {str(e)}")

    def set_memory_manager(self, memory_manager) -> None:
        """Set the memory manager for integration."""
        self._memory_manager = memory_manager
        logger.info("Memory manager integrated with adaptive resource manager")

    def set_circuit_breaker_registry(self, circuit_breaker_registry) -> None:
        """Set the circuit breaker registry for integration."""
        self._circuit_breaker_registry = circuit_breaker_registry
        logger.info("Circuit breaker registry integrated with adaptive resource manager")

    def set_graceful_degradation(self, graceful_degradation) -> None:
        """Set the graceful degradation system for integration."""
        self._graceful_degradation = graceful_degradation
        logger.info("Graceful degradation system integrated with adaptive resource manager")

    def register_resource_callback(self, resource_type: ResourceType, callback: Callable) -> None:
        """
        Register a callback for resource adaptation.

        Args:
            resource_type: Type of resource to monitor
            callback: Function to call when adaptation occurs
        """
        with self._lock:
            self._resource_callbacks[resource_type].append(callback)
            logger.info(f"Registered callback for {resource_type.value} resource")

    def set_operation_limit(self, operation: str, limit: int) -> None:
        """
        Set the limit for concurrent operations of a specific type.

        Args:
            operation: Operation type
            limit: Maximum concurrent operations
        """
        with self._lock:
            self._resource_limits.max_concurrent_operations[operation] = limit
            self._operation_semaphores[operation] = threading.Semaphore(limit)
            logger.info(f"Set {operation} operation limit to {limit}")

    def acquire_operation_permit(self, operation: str, timeout: Optional[float] = None) -> bool:
        """
        Acquire a permit to perform an operation.

        Args:
            operation: Operation type
            timeout: Maximum time to wait for permit in seconds

        Returns:
            bool: True if permit acquired, False otherwise
        """
        if operation not in self._operation_semaphores:
            # Create semaphore if it doesn't exist
            with self._lock:
                if operation not in self._operation_semaphores:
                    limit = 10  # Default limit
                    self._operation_semaphores[operation] = threading.Semaphore(limit)
                    self._active_operations[operation] = 0
                    self._resource_limits.max_concurrent_operations[operation] = limit

        # Try to acquire semaphore
        if timeout is not None:
            # Use timeout
            start_time = time.time()
            while time.time() - start_time < timeout:
                if self._operation_semaphores[operation].acquire(blocking=False):
                    with self._lock:
                        # Ensure the operation is in the active_operations dict
                        if operation not in self._active_operations:
                            self._active_operations[operation] = 0
                        self._active_operations[operation] += 1
                    return True
                time.sleep(0.01)
            return False
        else:
            # Block until acquired
            self._operation_semaphores[operation].acquire()
            with self._lock:
                # Ensure the operation is in the active_operations dict
                if operation not in self._active_operations:
                    self._active_operations[operation] = 0
                self._active_operations[operation] += 1
            return True

    def release_operation_permit(self, operation: str) -> None:
        """
        Release a permit after completing an operation.

        Args:
            operation: Operation type
        """
        if operation in self._operation_semaphores:
            self._operation_semaphores[operation].release()
            with self._lock:
                if operation in self._active_operations and self._active_operations[operation] > 0:
                    self._active_operations[operation] -= 1

    def with_operation_permit(self, operation: str, timeout: Optional[float] = None):
        """
        Decorator for operations that need a permit.

        Example:
            @adaptive_resource_manager.with_operation_permit("data_processing")
            def process_data():
                # This will only run when a permit is available
                pass
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                # Acquire permit
                if not self.acquire_operation_permit(operation, timeout):
                    logger.warning(f"Failed to acquire permit for {operation} operation")
                    raise TimeoutError(f"Timed out waiting for {operation} permit")

                try:
                    # Execute operation
                    return func(*args, **kwargs)
                finally:
                    # Release permit
                    self.release_operation_permit(operation)

            return wrapper

        return decorator

    def get_resource_usage(self) -> ResourceUsage:
        """
        Get current resource usage.

        Returns:
            ResourceUsage: Current resource usage
        """
        return self._collect_resource_usage()

    def get_resource_limits(self) -> ResourceLimits:
        """
        Get current resource limits.

        Returns:
            ResourceLimits: Current resource limits
        """
        with self._lock:
            return self._resource_limits

    def acquire_multiple_permits(self, operations: List[str], timeout: Optional[float] = None) -> bool:
        """
        Acquire multiple operation permits in a consistent order to prevent deadlocks.

        Args:
            operations: List of operation types to acquire permits for
            timeout: Maximum time to wait for all permits in seconds

        Returns:
            bool: True if all permits acquired, False otherwise
        """
        # Sort operations to ensure consistent acquisition order
        sorted_ops = sorted(operations)

        # Track acquired permits for cleanup in case of failure
        acquired = []

        try:
            # Calculate timeout per operation if specified
            op_timeout = None
            if timeout is not None:
                op_timeout = timeout / len(sorted_ops)

            # Acquire permits in sorted order
            for op in sorted_ops:
                if not self.acquire_operation_permit(op, op_timeout):
                    # Release any permits already acquired
                    for acquired_op in acquired:
                        self.release_operation_permit(acquired_op)
                    logger.warning(f"Failed to acquire permit for {op} operation")
                    return False
                acquired.append(op)

            return True
        except Exception as e:
            # Release any permits already acquired
            for acquired_op in acquired:
                self.release_operation_permit(acquired_op)
            logger.error(f"Error acquiring multiple permits: {str(e)}")
            return False

    def release_multiple_permits(self, operations: List[str]) -> None:
        """
        Release multiple operation permits.

        Args:
            operations: List of operation types to release permits for
        """
        # Release in reverse order of acquisition (sorted order)
        for op in sorted(operations, reverse=True):
            self.release_operation_permit(op)

    def get_active_operations(self) -> Dict[str, int]:
        """
        Get count of active operations by type.

        Returns:
            Dict[str, int]: Active operations by type
        """
        with self._lock:
            return self._active_operations.copy()

    def get_resource_usage_history(self, minutes: int = 60) -> List[ResourceUsage]:
        """
        Get resource usage history.

        Args:
            minutes: Number of minutes of history to return

        Returns:
            List[ResourceUsage]: Resource usage history
        """
        with self._lock:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            return [u for u in self._resource_usage_history if u.timestamp >= cutoff_time]

    def shutdown(self) -> None:
        """Shutdown the adaptive resource manager."""
        self._stop_monitoring()
        logger.info("Adaptive resource manager shut down")

# Create global instance
adaptive_resource_manager = AdaptiveResourceManager.get_instance()

# Convenience functions
def get_resource_usage() -> ResourceUsage:
    """Get current resource usage."""
    return adaptive_resource_manager.get_resource_usage()

def get_resource_limits() -> ResourceLimits:
    """Get current resource limits."""
    return adaptive_resource_manager.get_resource_limits()

def get_active_operations() -> Dict[str, int]:
    """Get count of active operations by type."""
    return adaptive_resource_manager.get_active_operations()

def acquire_operation_permit(operation: str, timeout: Optional[float] = None) -> bool:
    """Acquire a permit to perform an operation."""
    return adaptive_resource_manager.acquire_operation_permit(operation, timeout)

def release_operation_permit(operation: str) -> None:
    """Release a permit after completing an operation."""
    adaptive_resource_manager.release_operation_permit(operation)

def with_operation_permit(operation: str, timeout: Optional[float] = None):
    """Decorator for operations that need a permit."""
    return adaptive_resource_manager.with_operation_permit(operation, timeout)

def acquire_multiple_permits(operations: List[str], timeout: Optional[float] = None) -> bool:
    """Acquire multiple permits in a consistent order to prevent deadlocks."""
    return adaptive_resource_manager.acquire_multiple_permits(operations, timeout)

def release_multiple_permits(operations: List[str]) -> None:
    """Release multiple permits."""
    adaptive_resource_manager.release_multiple_permits(operations)

# Try to import dependencies for integration
try:
    from utils.enhanced_memory_manager import enhanced_memory_manager
    adaptive_resource_manager.set_memory_manager(enhanced_memory_manager)
except ImportError:
    logger.debug("Enhanced memory manager not available for adaptive resource manager integration")

try:
    from utils.enhanced_circuit_breaker import EnhancedCircuitBreaker
    adaptive_resource_manager.set_circuit_breaker_registry(EnhancedCircuitBreaker)
except ImportError:
    logger.debug("Enhanced circuit breaker not available for adaptive resource manager integration")

try:
    from utils.graceful_degradation import graceful_degradation
    adaptive_resource_manager.set_graceful_degradation(graceful_degradation)
except ImportError:
    logger.debug("Graceful degradation not available for adaptive resource manager integration")
