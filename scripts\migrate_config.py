"""
Migration script to help transition from the old configuration system to the new consolidated one.

This script:
1. Reads the existing config.json file
2. Converts it to the new format
3. Saves it to a new file (config.json.new)
4. Provides instructions for updating imports in code

Usage:
    python scripts/migrate_config.py [--apply]

Options:
    --apply    Apply the changes directly to config.json (backup will be created)
"""
import os
import sys
import json
import shutil
import argparse
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

def migrate_config(apply_changes=False):
    """
    Migrate the configuration from the old format to the new consolidated format.
    
    Args:
        apply_changes: Whether to apply the changes directly to config.json
    
    Returns:
        bool: True if migration was successful, False otherwise
    """
    try:
        # Paths
        config_path = project_root / 'config' / 'config.json'
        new_config_path = project_root / 'config' / 'config.json.new'
        backup_path = project_root / 'config' / 'config.json.bak'
        
        # Check if config.json exists
        if not config_path.exists():
            logger.error(f"Configuration file not found: {config_path}")
            return False
        
        # Read existing config
        logger.info(f"Reading configuration from {config_path}")
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        # The new format is largely compatible with the old one,
        # but we need to ensure all required fields are present
        
        # Ensure MT5 configuration is complete
        if 'mt5' not in config_data:
            config_data['mt5'] = {}
        
        if 'max_connections' not in config_data['mt5']:
            config_data['mt5']['max_connections'] = 5
        
        if 'timeout' not in config_data['mt5']:
            config_data['mt5']['timeout'] = 60000
        
        if 'retry_interval' not in config_data['mt5']:
            config_data['mt5']['retry_interval'] = 5
        
        if 'auto_start_terminals' not in config_data['mt5']:
            config_data['mt5']['auto_start_terminals'] = True
        
        # Ensure strategy configuration is complete
        if 'strategy' not in config_data:
            config_data['strategy'] = {}
        
        strategy_defaults = {
            'symbol': 'BTCUSD.a',
            'timeframes': ['M5', 'M15', 'M30', 'H1', 'H4'],
            'sequence_length': 288,
            'lot_size': 0.01,
            'max_positions': 1,
            'stop_loss_pips': 100,
            'take_profit_pips': 200,
            'max_spread_pips': 10,
            'risk_per_trade': 0.02,
            'max_daily_loss': 0.05,
            'max_daily_trades': 5,
            'cooldown_period': 300,
            'volatility_threshold': 2.0,
            'trend_threshold': 0.7,
            'position_sizing_factor': 0.5
        }
        
        for key, value in strategy_defaults.items():
            if key not in config_data['strategy']:
                config_data['strategy'][key] = value
        
        # Ensure model configurations are complete
        if 'models' not in config_data:
            config_data['models'] = {}
        
        # Add system configuration
        if 'system' not in config_data:
            config_data['system'] = {
                'log_level': config_data.get('log_level', 'INFO'),
                'log_file': 'logs/system.log',
                'min_memory_gb': 4,
                'environment': 'development',
                'data_dir': config_data.get('data_base_path', 'data'),
                'models_dir': config_data.get('models_base_path', 'models'),
                'reports_dir': 'reports'
            }
        
        # Add monitoring configuration
        if 'monitoring' not in config_data:
            config_data['monitoring'] = {
                'report_interval': 10,
                'metrics_interval': 5,
                'plot_interval': 10,
                'save_interval': 10,
                'output_dir': 'monitoring',
                'plots_dir': 'plots',
                'reports_dir': 'reports',
                'max_log_files': 10,
                'max_log_size_mb': 100
            }
        
        # Save the new configuration
        logger.info(f"Saving new configuration to {new_config_path}")
        with open(new_config_path, 'w') as f:
            json.dump(config_data, f, indent=4)
        
        # Apply changes if requested
        if apply_changes:
            logger.info(f"Creating backup of original configuration at {backup_path}")
            shutil.copy2(config_path, backup_path)
            
            logger.info(f"Applying changes to {config_path}")
            shutil.move(new_config_path, config_path)
        
        # Print instructions
        print("\nMigration completed successfully!")
        if not apply_changes:
            print(f"\nNew configuration saved to: {new_config_path}")
            print("\nTo apply the changes, run:")
            print(f"    python {__file__} --apply")
        else:
            print(f"\nChanges applied to: {config_path}")
            print(f"Backup saved to: {backup_path}")
        
        print("\nCode Migration Instructions:")
        print("----------------------------")
        print("1. Update imports in your code:")
        print("   Old: from config.config import ConfigurationManager")
        print("   New: from config.consolidated_config import ConfigurationManager")
        print()
        print("2. Update imports in your code:")
        print("   Old: from config.unified_config import config_manager, get_config, TradingConfig, ModelConfig")
        print("   New: from config.consolidated_config import ConfigurationManager, TradingConfig, ModelConfig")
        print("   And then: config_manager = ConfigurationManager()")
        print("             trading_config = config_manager.get_config()")
        
        return True
    
    except Exception as e:
        logger.error(f"Error during migration: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Migrate configuration from old format to new consolidated format")
    parser.add_argument('--apply', action='store_true', help="Apply changes directly to config.json")
    args = parser.parse_args()
    
    return 0 if migrate_config(apply_changes=args.apply) else 1

if __name__ == "__main__":
    sys.exit(main())
