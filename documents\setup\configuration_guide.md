# Multi-Terminal Configuration Guide

## Overview

The multi-terminal trading bot uses a sophisticated configuration system managed by the `ConfigurationManager` class. All configuration is stored in `config/config.json` and validated using comprehensive dataclass schemas for type safety and validation.

## Configuration Files

### Main Configuration: `config/config.json`

The main configuration file contains all system settings for **5 MT5 terminals**:

```json
{
  "mt5": {
    "max_connections": 5,
    "timeout": 60000,
    "retry_interval": 5,
    "auto_start_terminals": true,
    "enable_algorithmic_trading": true,
    "terminals": {
      "1": {
        "login": "61336224",
        "password": "126820Al_",
        "server": "ICMarkets-Demo",
        "path": "C:/Users/<USER>/Desktop/MT5 IC 01/terminal64.exe",
        "primary_model": "lstm"
      },
      "2": {
        "login": "61336225",
        "password": "126820Al_",
        "server": "ICMarkets-Demo",
        "path": "C:/Users/<USER>/Desktop/MT5 IC 02/terminal64.exe",
        "primary_model": "gru"
      },
      "3": {
        "login": "61336226",
        "password": "126820Al_",
        "server": "ICMarkets-Demo",
        "path": "C:/Users/<USER>/Desktop/MT5 IC 03/terminal64.exe",
        "primary_model": "tft"
      },
      "4": {
        "login": "61336227",
        "password": "126820Al_",
        "server": "PepperStone-Demo",
        "path": "C:/Users/<USER>/Desktop/MT5 Pepper 02/terminal64.exe",
        "primary_model": "xgboost"
      },
      "5": {
        "login": "61336228",
        "password": "126820Al_",
        "server": "PepperStone-Demo",
        "path": "C:/Users/<USER>/Desktop/MT5 Pepper 03/terminal64.exe",
        "primary_model": "lightgbm"
      }
    },
    "symbol": "BTCUSD.a",
    "timeframes": ["M5", "M15", "M30", "H1", "H4"],
    "max_bars": 10000
  }
}
```

### Configuration Manager: `config/consolidated_config.py`

The `ConfigurationManager` class provides centralized configuration management:

```python
from config.consolidated_config import ConfigurationManager

# Initialize configuration manager
config_manager = ConfigurationManager()

# Access configuration sections
mt5_config = config_manager.get_mt5_config()
model_config = config_manager.get_model_config('lstm')
strategy_config = config_manager.get_strategy_config()
```

**Key Features:**
- Loads configuration from JSON files
- Validates configuration parameters
- Provides typed configuration objects
- Manages environment-specific settings
- Handles directory creation and path resolution

## Key Configuration Sections

### 1. MT5 Terminal Configuration
```json
{
  "mt5": {
    "max_connections": 5,
    "timeout": 60000,
    "retry_interval": 5,
    "terminals": {
      "1": {
        "login": "your_login",
        "password": "your_password",
        "server": "your_server",
        "path": "C:/Path/To/MT5/terminal64.exe"
      }
    }
  }
}
```

### 2. Trading Strategy Configuration
```json
{
  "strategy": {
    "symbol": "BTCUSD.a",
    "timeframes": ["M5", "M15", "M30", "H1", "H4"],
    "sequence_length": 288,
    "lot_size": 0.01,
    "risk_per_trade": 0.02,
    "max_daily_loss": 0.05
  }
}
```

### 3. Model Configuration (All 9 Models)
Each model has specific configuration with standardized structure:
```json
{
  "models": {
    "lstm": {
      "model_path": "lstm_model.pt",
      "model_type": "lstm",
      "input_dim": 5,
      "sequence_length": 60,
      "hidden_units": 64,
      "num_layers": 2,
      "dropout_rate": 0.2,
      "learning_rate": 0.001,
      "batch_size": 32,
      "epochs": 100,
      "FEATURE_COLUMNS": ["open", "high", "low", "close", "tick_volume"]
    },
    "gru": {
      "model_path": "gru_model.pt",
      "model_type": "gru",
      "input_dim": 5,
      "sequence_length": 60,
      "hidden_units": 64,
      "num_layers": 2,
      "dropout_rate": 0.2,
      "learning_rate": 0.001,
      "batch_size": 32,
      "epochs": 100
    },
    "transformer": {
      "model_path": "transformer_model.pt",
      "model_type": "transformer",
      "input_dim": 5,
      "sequence_length": 60,
      "d_model": 64,
      "nhead": 8,
      "num_layers": 2,
      "dropout_rate": 0.1,
      "learning_rate": 0.001,
      "batch_size": 32,
      "epochs": 100
    },
    "tft": {
      "model_path": "tft_model.pt",
      "model_type": "tft",
      "max_encoder_length": 60,
      "max_prediction_length": 1,
      "hidden_size": 16,
      "lstm_layers": 1,
      "num_attention_heads": 4,
      "dropout": 0.1,
      "learning_rate": 0.03,
      "batch_size": 64,
      "epochs": 100
    },
    "xgboost": {
      "model_path": "xgboost_model.json",
      "model_type": "xgboost",
      "n_estimators": 100,
      "max_depth": 6,
      "learning_rate": 0.1,
      "subsample": 0.8,
      "colsample_bytree": 0.8,
      "random_state": 42
    },
    "lightgbm": {
      "model_path": "lightgbm_model.txt",
      "model_type": "lightgbm",
      "n_estimators": 100,
      "max_depth": 6,
      "learning_rate": 0.1,
      "subsample": 0.8,
      "colsample_bytree": 0.8,
      "random_state": 42
    },
    "arima": {
      "model_path": "arima_model.pkl",
      "model_type": "arima",
      "order": [1, 1, 1],
      "seasonal_order": [0, 0, 0, 0],
      "auto_arima": true,
      "max_p": 5,
      "max_q": 5,
      "max_d": 2
    },
    "lstm_arima": {
      "model_path": "lstm_arima_ensemble.pkl",
      "model_type": "ensemble",
      "lstm_weight": 0.7,
      "arima_weight": 0.3,
      "base_models": ["lstm", "arima"]
    },
    "tft_arima": {
      "model_path": "tft_arima_ensemble.pkl",
      "model_type": "ensemble",
      "tft_weight": 0.8,
      "arima_weight": 0.2,
      "base_models": ["tft", "arima"]
    }
  }
}
```

### 4. Data Configuration
```json
{
  "data": {
    "data_base_path": "data/storage",
    "models_base_path": "models",
    "validation_split_ratio": 0.15,
    "test_split_ratio": 0.15,
    "feature_columns": ["open", "high", "low", "close", "tick_volume"]
  }
}
```

### 5. System Configuration
```json
{
  "system": {
    "log_level": "INFO",
    "max_memory_usage": 80,
    "debug_mode": false,
    "environment": "development",
    "max_workers": 2
  }
}
```

## Model Directory Structure

The system uses a standardized directory structure for model storage:
```
models/
├── terminal_1/M5/
│   ├── lstm_model/
│   ├── gru_model/
│   └── arima_model/
├── terminal_2/M5/
├── terminal_3/M5/
├── terminal_4/M5/
└── terminal_5/M5/
```

## Model Manager Configuration

The `ModelManager` class handles model loading and management:
```python
from utils.model_manager import ModelManager

# Initialize model manager
model_manager = ModelManager(
    terminal_id="1",
    timeframe="M5",
    check_model_health=True
)

# Available model types
model_types = ['lstm', 'gru', 'transformer', 'tft', 'xgboost',
               'lightgbm', 'arima', 'lstm_arima', 'tft_arima']
```

## Configuration Validation

The `ConfigurationManager` validates configuration on startup:
- **Required Fields**: Checks all mandatory configuration fields
- **Data Types**: Validates parameter types and ranges
- **File Paths**: Verifies model and data paths exist
- **MT5 Connections**: Tests terminal connectivity
- **Model Compatibility**: Ensures model configurations are valid

## Environment Variables

For enhanced security, use environment variables for sensitive data:
```bash
# Windows
set MT5_PASSWORD_1=your_password_1
set MT5_PASSWORD_2=your_password_2

# Linux/Mac
export MT5_PASSWORD_1=your_password_1
export MT5_PASSWORD_2=your_password_2
```
