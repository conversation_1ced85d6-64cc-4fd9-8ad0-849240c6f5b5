"""
Test script for GRU model with comprehensive evaluation.
"""
import os
import sys
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from bayes_opt import BayesianOptimization
from sklearn.model_selection import TimeSeriesSplit

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from models.gru_model import GRUModel
from config import config_manager

# Get model configuration
model_config = config_manager.get_model_config('gru')
from tests.models.base_test import BaseModelTest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gru_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GRUTest(BaseModelTest):
    """Test class for GRU model with comprehensive evaluation."""

    def __init__(self):
        """Initialize GRU test class."""
        super().__init__('gru')
        self.model = None

    def prepare_sequences(self, X: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for GRU model."""
        try:
            X_sequences = []
            y_sequences = []

            for i in range(len(X) - sequence_length):
                X_sequences.append(X[i:(i + sequence_length)])
                y_sequences.append(X[i + sequence_length, 3])  # Using close price as target

            X_sequences = np.array(X_sequences, dtype=np.float32)
            y_sequences = np.array(y_sequences, dtype=np.float32)

            self.logger.info(f"Prepared {len(X_sequences)} sequences of length {sequence_length}")
            self.logger.info(f"X_sequences shape: {X_sequences.shape}, y_sequences shape: {y_sequences.shape}")
            return X_sequences, y_sequences

        except Exception as e:
            self.logger.error(f"Error preparing sequences: {str(e)}")
            raise

    def optimize_hyperparameters(self, X_train: np.ndarray, y_train: np.ndarray,
                               X_val: np.ndarray, y_val: np.ndarray) -> Dict:
        """Optimize model hyperparameters using Bayesian optimization."""
        try:
            def objective(gru_units, dropout_rate, learning_rate, batch_size):
                # Create model with current parameters
                params = {
                    'gru_units': int(gru_units),
                    'dropout_rate': dropout_rate,
                    'learning_rate': learning_rate,
                    'batch_size': int(batch_size),
                    'dense_units': 64,
                    'epochs': 20,  # Reduced from 100
                    'patience': 5  # Reduced from 10
                }

                print(f"Trying parameters: {params}")

                model = GRUModel(model_config)
                model.build()
                model.set_model_params(params)

                # Train and evaluate
                metrics = model.train(X_train, y_train, X_val, y_val)
                print(f"Validation metrics: {metrics}")
                return -metrics['rmse']  # Minimize RMSE

            # Define parameter bounds
            pbounds = {
                'gru_units': (32, 256),
                'dropout_rate': (0.1, 0.5),
                'learning_rate': (0.001, 0.01),
                'batch_size': (16, 128)
            }

            print("Starting hyperparameter optimization...")

            # Run optimization with fewer iterations
            optimizer = BayesianOptimization(
                f=objective,
                pbounds=pbounds,
                random_state=42,
                verbose=2
            )

            optimizer.maximize(
                init_points=3,  # Reduced from 5
                n_iter=10      # Reduced from 20
            )

            # Get best parameters
            best_params = optimizer.max['params']
            best_params['gru_units'] = int(best_params['gru_units'])
            best_params['batch_size'] = int(best_params['batch_size'])
            best_params['dense_units'] = 64
            best_params['epochs'] = 20
            best_params['patience'] = 5

            print(f"Best hyperparameters: {best_params}")
            return best_params

        except Exception as e:
            print(f"Error optimizing hyperparameters: {str(e)}")
            raise

    def test_model(self):
        """Test the GRU model with comprehensive evaluation."""
        try:
            print("\nStarting GRU model test...")

            # Load and preprocess data
            print("Loading data...")
            data_path = project_root / "data" / "terminal_1" / "M5_data.csv"
            X, y = self.load_and_preprocess_data(data_path)

            print(f"Loaded data shapes - X: {X.shape}, y: {y.shape}")

            # Prepare sequences
            print("\nPreparing sequences...")
            sequence_length = model_config.SEQUENCE_LENGTH
            X_sequences, y_sequences = self.prepare_sequences(X, sequence_length)

            print(f"Prepared sequences - X: {X_sequences.shape}, y: {y_sequences.shape}")

            # Initialize cross-validation with fewer splits
            tscv = TimeSeriesSplit(n_splits=3)  # Reduced from 5

            # Store metrics across folds
            self.all_metrics = []

            for fold, (train_idx, test_idx) in enumerate(tscv.split(X_sequences)):
                print(f"\nProcessing fold {fold + 1}/3")

                # Split data
                X_train, X_test = X_sequences[train_idx], X_sequences[test_idx]
                y_train, y_test = y_sequences[train_idx], y_sequences[test_idx]

                print(f"Train shapes - X: {X_train.shape}, y: {y_train.shape}")
                print(f"Test shapes - X: {X_test.shape}, y: {y_test.shape}")

                # Further split for validation
                val_size = int(len(X_train) * 0.2)
                X_train, X_val = X_train[:-val_size], X_train[-val_size:]
                y_train, y_val = y_train[:-val_size], y_train[-val_size:]

                print(f"Validation shapes - X: {X_val.shape}, y: {y_val.shape}")

                # Optimize hyperparameters
                print("\nOptimizing hyperparameters...")
                best_params = self.optimize_hyperparameters(X_train, y_train, X_val, y_val)
                print(f"Best parameters found: {best_params}")

                # Initialize and train model
                print("\nTraining model...")
                self.model = GRUModel(model_config)
                self.model.build()
                self.model.set_model_params(best_params)

                # Monitor resources
                self.monitor_gpu_usage()
                self.monitor_memory_usage()

                # Train model
                print("Starting model training...")
                train_metrics = self.model.train(X_train, y_train, X_val, y_val)
                print(f"Training metrics: {train_metrics}")

                # Make predictions
                print("Making predictions...")
                y_pred = self.model.predict(X_test)

                # Calculate metrics
                metrics = self.calculate_metrics(y_test, y_pred)
                self.all_metrics.append(metrics)
                print(f"Test metrics: {metrics}")

                # Plot results
                print("Generating plots...")
                self.plot_predictions(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Actual vs Predicted Values",
                    save_path=self.output_dir / f"fold_{fold}_predictions.png"
                )

                self.plot_residuals(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Prediction Residuals",
                    save_path=self.output_dir / f"fold_{fold}_residuals.png"
                )

                self.plot_error_distribution(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Error Distribution",
                    save_path=self.output_dir / f"fold_{fold}_error_dist.png"
                )

                print(f"Completed fold {fold + 1}/3")

            # Calculate and plot average metrics
            print("\nCalculating average metrics...")
            avg_metrics = pd.DataFrame(self.all_metrics).mean().to_dict()
            self.plot_metrics(
                avg_metrics,
                title="Average Model Evaluation Metrics",
                save_path=self.output_dir / "average_metrics.png"
            )

            # Generate comprehensive report
            print("Generating test report...")
            self.generate_report()

            print("\nGRU model test completed successfully!")

        except Exception as e:
            print(f"\nError in GRU model test: {str(e)}")
            self.logger.error(f"Error in GRU model test: {str(e)}")
            raise

def test_gru():
    """Test the GRU model."""
    test = GRUTest()
    test.test_model()

if __name__ == "__main__":
    test = GRUTest()
    test.test_model()