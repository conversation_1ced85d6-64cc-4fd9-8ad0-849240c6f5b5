#!/usr/bin/env python
"""
Script to collect 6-year historical BTCUSD.a data from all 5 MT5 terminals
across multiple timeframes (M5, M15, M30, H1, H4) in parquet format.

This script:
1. Connects to all available MT5 terminals
2. Collects historical OHLCV data for BTCUSD.a
3. Processes and validates the data
4. Saves the data in parquet format
5. Verifies the collected data for completeness and quality
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import argparse
import time
from concurrent.futures import ThreadPoolExecutor
import MetaTrader5 as mt5
import sys
import os

# Add the project root to the Python path to ensure imports work correctly
sys.path.insert(0, os.path.abspath('.'))

# Import local modules
from data.data_collector import MT5DataCollector
from config.consolidated_config import ConfigurationManager
from trading.mt5_connection_manager import MT5ConnectionManager

# Create a simple error handler class if the import fails
class SimpleErrorHandler:
    def handle_error(self, error, context=None, source=None):
        logger.error(f"Error in {source or 'unknown'}: {str(error)}")
        if context:
            logger.error(f"Context: {context}")
        return {"error": str(error), "context": context}

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('data_collection')

# Constants
SYMBOL = "BTCUSD.a"
TIMEFRAMES = ["M5", "M15", "M30", "H1", "H4"]
YEARS = 6  # 6 years of historical data
MAX_WORKERS = 5  # Maximum number of concurrent workers

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect historical BTCUSD.a data from MT5 terminals')
    parser.add_argument('--terminal_ids', nargs='+', type=int, default=[1, 2, 3, 4, 5],
                        help='List of terminal IDs to collect data from')
    parser.add_argument('--output_dir', type=str, default='data',
                        help='Base output directory for collected data')
    parser.add_argument('--years', type=int, default=6,
                        help='Number of years of historical data to collect')
    parser.add_argument('--force', action='store_true',
                        help='Force data collection even if files already exist')
    return parser.parse_args()

def collect_data_for_terminal_timeframe(terminal_id, timeframe, start_date, end_date, output_dir):
    """
    Collect data for a specific terminal and timeframe.

    Args:
        terminal_id: MT5 terminal ID
        timeframe: Timeframe to collect (e.g., "M5")
        start_date: Start date for data collection
        end_date: End date for data collection
        output_dir: Output directory for collected data

    Returns:
        Tuple of (success, message, data_path)
    """
    try:
        logger.info(f"Collecting {SYMBOL} {timeframe} data from terminal {terminal_id}")

        # Initialize configuration manager
        config_manager = ConfigurationManager()

        # Initialize MT5 connection manager
        mt5_manager = MT5ConnectionManager(config_manager=config_manager)

        # Get MT5 connection
        connection = mt5_manager.get_connection(terminal_id)
        if not connection or not connection.is_connected:
            logger.warning(f"Failed to connect to MT5 terminal {terminal_id} using connection manager, trying direct initialization")

            # Try direct initialization as fallback
            terminal_config = config_manager.get_mt5_config().terminals[str(terminal_id)]
            if not mt5.initialize(path=terminal_config.path, portable=True):
                return False, f"Failed to connect to MT5 terminal {terminal_id}: {mt5.last_error()}", None

            # Check if connection is working
            if not mt5.terminal_info():
                return False, f"Failed to get terminal info for MT5 terminal {terminal_id}", None

            logger.info(f"Successfully connected to MT5 terminal {terminal_id} using direct initialization")

        # Initialize data collector
        collector = MT5DataCollector(
            terminal_config=config_manager.get_mt5_config().terminals[str(terminal_id)],
            error_handler=SimpleErrorHandler(),
            cache_dir=f"data/cache/terminal_{terminal_id}"
        )

        # Prepare output path following the required structure:
        # data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        start_date_str = start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")

        terminal_dir = Path(output_dir) / "storage" / "historical" / f"terminal_{terminal_id}"
        symbol_timeframe_dir = terminal_dir / f"{SYMBOL}_{timeframe}"
        symbol_timeframe_dir.mkdir(parents=True, exist_ok=True)

        filename = f"{start_date_str}_{end_date_str}_terminal_{terminal_id}_{timestamp}.parquet"
        output_path = symbol_timeframe_dir / filename

        # Check if file already exists and is not empty
        if output_path.exists() and output_path.stat().st_size > 0:
            logger.info(f"Data file already exists: {output_path}")
            return True, f"Data file already exists: {output_path}", output_path

        # Create output directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Collect data in optimized chunks to manage memory and improve performance
        all_data = []
        current_start = start_date

        # Optimize chunk size based on timeframe
        timeframe_chunk_map = {
            'M5': timedelta(days=7),   # 7 days for M5 (2016 bars)
            'M15': timedelta(days=21), # 21 days for M15 (2016 bars)
            'M30': timedelta(days=42), # 42 days for M30 (2016 bars)
            'H1': timedelta(days=84),  # 84 days for H1 (2016 bars)
            'H4': timedelta(days=336)  # 336 days for H4 (2016 bars)
        }
        chunk_size = timeframe_chunk_map.get(timeframe, timedelta(days=30))

        while current_start < end_date:
            chunk_end = min(current_start + chunk_size, end_date)

            logger.info(f"Collecting chunk from {current_start} to {chunk_end} for {SYMBOL} {timeframe} from terminal {terminal_id}")

            try:
                # Get data for this chunk with optimized parameters
                df = collector.get_historical_data(
                    symbol=SYMBOL,
                    timeframe=timeframe,
                    start_time=current_start,
                    include_technical_indicators=True
                )

                if df is not None and not df.empty:
                    # Filter data to the current chunk's date range
                    df = df.loc[current_start:chunk_end]
                    if not df.empty:
                        # Optimize memory usage before appending
                        df = df.astype({
                            'open': 'float32',
                            'high': 'float32',
                            'low': 'float32',
                            'close': 'float32',
                            'tick_volume': 'int32'
                        }, errors='ignore')

                        all_data.append(df)
                        logger.info(f"Collected {len(df)} rows for {SYMBOL} {timeframe} from terminal {terminal_id} ({current_start} to {chunk_end})")
                    else:
                        logger.warning(f"No data in range for {SYMBOL} {timeframe} from terminal {terminal_id} ({current_start} to {chunk_end})")
                else:
                    logger.warning(f"Failed to collect data for {SYMBOL} {timeframe} from terminal {terminal_id} ({current_start} to {chunk_end})")

            except Exception as e:
                logger.error(f"Error collecting chunk for {SYMBOL} {timeframe} from terminal {terminal_id}: {str(e)}")

            # Move to next chunk
            current_start = chunk_end

            # Adaptive sleep based on data size and timeframe
            sleep_time = 0.5 if timeframe in ['M5', 'M15'] else 0.2
            time.sleep(sleep_time)

        # Combine all chunks efficiently
        if not all_data:
            logger.error(f"No data collected for {SYMBOL} {timeframe} from terminal {terminal_id}")
            return False, f"No data collected for {SYMBOL} {timeframe} from terminal {terminal_id}", None

        logger.info(f"Combining {len(all_data)} data chunks...")

        # Combine chunks efficiently
        combined_df = pd.concat(all_data, ignore_index=False, copy=False)

        # Remove duplicates and sort efficiently
        logger.info("Removing duplicates and sorting data...")
        combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
        combined_df = combined_df.sort_index()

        # Optimize data types for storage
        logger.info("Optimizing data types for storage...")
        for col in combined_df.select_dtypes(include=['float64']).columns:
            combined_df[col] = combined_df[col].astype('float32')
        for col in combined_df.select_dtypes(include=['int64']).columns:
            combined_df[col] = combined_df[col].astype('int32')

        # Save to parquet with compression
        logger.info(f"Saving {len(combined_df)} rows to parquet file...")
        combined_df.to_parquet(
            output_path,
            compression='snappy',
            engine='pyarrow'
        )

        logger.info(f"Successfully saved {len(combined_df)} rows of {SYMBOL} {timeframe} data from terminal {terminal_id} to {output_path}")

        return True, f"Successfully collected data for {SYMBOL} {timeframe} from terminal {terminal_id}", output_path

    except Exception as e:
        logger.error(f"Error collecting data for {SYMBOL} {timeframe} from terminal {terminal_id}: {str(e)}")
        return False, f"Error: {str(e)}", None

def verify_data(data_path):
    """
    Verify the collected data for completeness and quality.

    Args:
        data_path: Path to the parquet file

    Returns:
        Tuple of (is_valid, message)
    """
    try:
        # Load the data
        df = pd.read_parquet(data_path)

        # Check if data is empty
        if df.empty:
            return False, "Data is empty"

        # Check for required columns
        required_columns = ['open', 'high', 'low', 'close', 'tick_volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return False, f"Missing required columns: {missing_columns}"

        # Check for NaN values in critical columns
        nan_counts = df[required_columns].isna().sum()
        if nan_counts.sum() > 0:
            nan_cols = [f"{col}: {count}" for col, count in nan_counts.items() if count > 0]
            return False, f"NaN values found: {', '.join(nan_cols)}"

        # Validate price relationships (high >= low, etc.)
        invalid_rows = df[(df['high'] < df['low']) |
                        (df['open'] > df['high']) |
                        (df['open'] < df['low']) |
                        (df['close'] > df['high']) |
                        (df['close'] < df['low'])]
        if not invalid_rows.empty:
            return False, f"Found {len(invalid_rows)} rows with invalid price relationships"

        # Check for timestamp gaps
        df_sorted = df.sort_index()
        time_diffs = df_sorted.index.to_series().diff().dt.total_seconds() / 60

        # Get expected interval in minutes based on timeframe in filename
        timeframe = data_path.stem.split('_')[1]
        interval_map = {
            'M1': 1, 'M5': 5, 'M15': 15, 'M30': 30,
            'H1': 60, 'H4': 240, 'D1': 1440, 'W1': 10080
        }
        expected_interval = interval_map.get(timeframe, 1)

        # Find gaps larger than 2x the expected interval (allowing for some variance)
        time_diffs = time_diffs[1:]  # Skip the first NaN value
        large_gaps = time_diffs[time_diffs > expected_interval * 2]

        if len(large_gaps) > 0.1 * len(df):  # More than 10% of rows have gaps
            return False, f"Significant time gaps detected in {len(large_gaps)} rows"

        return True, f"Data verification successful for {data_path}"

    except Exception as e:
        return False, f"Error verifying data: {str(e)}"

def main():
    """Main function to collect historical data."""
    args = parse_arguments()

    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * args.years)

    logger.info(f"Starting data collection for {SYMBOL} from {start_date} to {end_date}")
    logger.info(f"Collecting data for timeframes: {TIMEFRAMES}")
    logger.info(f"Using terminals: {args.terminal_ids}")

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Collect data using ThreadPoolExecutor
    results = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = []

        # Submit tasks for each terminal and timeframe
        for terminal_id in args.terminal_ids:
            for timeframe in TIMEFRAMES:
                future = executor.submit(
                    collect_data_for_terminal_timeframe,
                    terminal_id,
                    timeframe,
                    start_date,
                    end_date,
                    output_dir
                )
                futures.append((future, terminal_id, timeframe))

        # Process results as they complete
        for future, terminal_id, timeframe in futures:
            try:
                success, message, data_path = future.result()
                results.append({
                    'terminal_id': terminal_id,
                    'timeframe': timeframe,
                    'success': success,
                    'message': message,
                    'data_path': data_path
                })
                logger.info(f"Result for terminal {terminal_id}, timeframe {timeframe}: {message}")
            except Exception as e:
                logger.error(f"Error processing result for terminal {terminal_id}, timeframe {timeframe}: {str(e)}")
                results.append({
                    'terminal_id': terminal_id,
                    'timeframe': timeframe,
                    'success': False,
                    'message': f"Error: {str(e)}",
                    'data_path': None
                })

    # Verify collected data
    logger.info("Verifying collected data...")
    verification_results = []
    for result in results:
        if result['success'] and result['data_path']:
            is_valid, message = verify_data(result['data_path'])
            verification_results.append({
                'terminal_id': result['terminal_id'],
                'timeframe': result['timeframe'],
                'is_valid': is_valid,
                'message': message,
                'data_path': result['data_path']
            })
            logger.info(f"Verification for terminal {result['terminal_id']}, timeframe {result['timeframe']}: {message}")

    # Print summary
    logger.info("\n=== Data Collection Summary ===")
    logger.info(f"Total tasks: {len(results)}")
    logger.info(f"Successful collections: {sum(1 for r in results if r['success'])}")
    logger.info(f"Failed collections: {sum(1 for r in results if not r['success'])}")

    logger.info("\n=== Data Verification Summary ===")
    logger.info(f"Total verifications: {len(verification_results)}")
    logger.info(f"Valid datasets: {sum(1 for r in verification_results if r['is_valid'])}")
    logger.info(f"Invalid datasets: {sum(1 for r in verification_results if not r['is_valid'])}")

    logger.info("Data collection and verification complete.")

if __name__ == "__main__":
    main()
