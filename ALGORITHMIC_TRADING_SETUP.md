# 🚀 Algorithmic Trading Setup Guide

## ⚠️ CRITICAL: Algorithmic Trading Must Be Enabled Manually

**The system has detected that algorithmic trading is disabled in your MT5 terminals. This is a CRITICAL issue that must be resolved before the trading bot can execute any trades.**

## 🔧 Quick Fix

### Step 1: Run the Setup Script
```bash
python setup_terminals.py
```

This script will:
- ✅ Test all terminal connections
- ✅ Identify which terminals have algorithmic trading disabled
- ✅ Provide detailed instructions for each terminal
- ✅ Optionally open the terminals for you

### Step 2: Enable Algorithmic Trading (Manual Process)

**For EACH terminal that shows "Algo Trading DISABLED":**

1. **📂 Open the MT5 Terminal**
   - The terminal window should appear on your screen
   - If not open, double-click the terminal executable

2. **🔍 Locate the 'Algo Trading' Button**
   - Look in the top toolbar of the MT5 terminal
   - It appears as a small robot icon or "AT" button
   - When disabled, it will have a RED light or be grayed out

3. **🖱️ Click the 'Algo Trading' Button**
   - Click once on the button
   - The button should turn GREEN when enabled
   - You should hear a confirmation sound (if enabled)

4. **✅ Verify the Status**
   - Check the bottom status bar of the terminal
   - It should display "Algo Trading enabled"
   - The button should show a GREEN light

5. **🔄 Repeat for ALL Terminals**
   - Each terminal must be enabled individually
   - Don't forget any terminal!

## 🎯 Visual Guide

```
MT5 Terminal Window
┌─────────────────────────────────────────────────────────┐
│ File Edit View Insert Charts Tools Window Help          │
├─────────────────────────────────────────────────────────┤
│ [📊] [📈] [🔧] [AT] ← CLICK THIS BUTTON                  │
│                    ↑                                    │
│              Algo Trading Button                        │
├─────────────────────────────────────────────────────────┤
│                                                         │
│           Trading Charts and Data                       │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ Status: Algo Trading enabled ← VERIFY THIS APPEARS      │
└─────────────────────────────────────────────────────────┘
```

## 🚨 Why This Happens

1. **Security Feature**: MT5 requires manual enablement of algorithmic trading for security reasons
2. **Terminal Restart**: The setting may reset when terminals are restarted
3. **User Safety**: Prevents unauthorized automated trading

## 🔍 Troubleshooting

### Problem: Button is Grayed Out
**Solution**: 
- Check if you're logged into the terminal
- Verify your account allows algorithmic trading
- Contact your broker if the feature is restricted

### Problem: Button Turns Red Again
**Solution**:
- Check your internet connection
- Verify your account credentials
- Ensure your account has sufficient balance

### Problem: "Algo Trading" Button Not Found
**Solution**:
- Right-click on the toolbar → Customize
- Add the "Algo Trading" button to the toolbar
- Or use the menu: Tools → Options → Expert Advisors

## 📋 Verification Checklist

Before running the trading bot, ensure:

- [ ] All MT5 terminals are open and logged in
- [ ] Algorithmic trading button is GREEN in all terminals
- [ ] Status bar shows "Algo Trading enabled" in all terminals
- [ ] No error messages in the terminal logs
- [ ] Account has sufficient balance for trading

## 🔄 After Enabling Algorithmic Trading

1. **Test the Setup**:
   ```bash
   python setup_terminals.py
   ```

2. **Run the Trading Bot**:
   ```bash
   python main.py
   ```

3. **Monitor the Logs**:
   - Check for "✅ Algorithmic trading ENABLED" messages
   - Ensure no "❌ DISABLED" warnings appear

## 📞 Support

If you continue to experience issues:

1. **Check the Logs**: Look in `logs/` directory for detailed error messages
2. **Verify Configuration**: Ensure `config/config.json` has correct terminal settings
3. **Test Individual Terminals**: Use the setup script to test each terminal separately

## 🎉 Success Indicators

When everything is working correctly, you should see:

```
✅ Algorithmic trading ENABLED on 5 terminals: ['1', '2', '3', '4', '5']
🎉 ALL TERMINALS READY: Algorithmic trading enabled on all terminals!
```

## ⚡ Quick Commands

```bash
# Test terminal setup
python setup_terminals.py

# Run trading bot
python main.py

# Check logs
tail -f logs/main.log
```

---

**Remember**: Algorithmic trading enablement is a **manual security requirement** by MetaTrader 5. This cannot be automated and must be done by the user for each terminal session.
