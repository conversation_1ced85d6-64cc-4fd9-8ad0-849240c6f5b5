#!/bin/bash
# Trading Bot Startup Script for Linux/Mac
# This script starts the MT5 trading bot with proper error handling

echo "========================================"
echo "MT5 Trading Bot Startup Script"
echo "========================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if Python is available
if ! command_exists python3; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8+ and add it to your PATH"
    exit 1
fi

# Check if virtual environment exists
if [ ! -f "venv/bin/activate" ]; then
    echo "WARNING: Virtual environment not found"
    echo "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to create virtual environment"
        exit 1
    fi
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install/update requirements
echo "Installing/updating requirements..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install requirements"
    exit 1
fi

# Check if configuration exists
if [ ! -f "config/config.json" ]; then
    echo "ERROR: Configuration file not found"
    echo "Please run setup_config.py first"
    exit 1
fi

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p logs data models reports

# Start the trading bot (MT5 terminals are Windows-only)
echo ""
echo "Starting trading bot..."
echo "Press Ctrl+C to stop the bot"
echo ""
python main.py

# Handle exit
echo ""
echo "Trading bot stopped."
read -p "Press Enter to continue..."
