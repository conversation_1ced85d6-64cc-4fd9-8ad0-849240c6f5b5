"""
Consolidated configuration manager for the trading bot.
Combines the best aspects of both existing configuration systems.
"""
import os
import json
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass, field, asdict

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class MT5TerminalConfig:
    """Configuration for a single MT5 terminal."""
    path: str
    login: Union[str, int]
    password: str
    server: str
    trade_mode: bool = True
    auto_trading: bool = True
    timeout: int = 60000
    retry_interval: int = 5

    def __post_init__(self):
        """Validate configuration parameters."""
        if not self.path or not os.path.exists(self.path):
            raise ValueError(f"Invalid MT5 terminal path: {self.path}")
        if not self.login:
            raise ValueError("Login cannot be empty")
        if not self.password:
            raise ValueError("Password cannot be empty")
        if not self.server:
            raise ValueError("Server cannot be empty")
        if self.timeout < 1000:
            raise ValueError("Timeout must be at least 1000ms")
        if self.retry_interval < 1:
            raise ValueError("Retry interval must be at least 1 second")

@dataclass
class MT5Config:
    """Configuration for MT5 connection management."""
    max_connections: int
    timeout: int
    retry_interval: int
    terminals: Dict[str, MT5TerminalConfig]
    auto_start_terminals: bool = True

    def __post_init__(self):
        """Validate configuration parameters."""
        if self.max_connections < 1:
            raise ValueError("Max connections must be at least 1")
        if self.timeout < 1000:
            raise ValueError("Timeout must be at least 1000ms")
        if self.retry_interval < 1:
            raise ValueError("Retry interval must be at least 1 second")
        if not self.terminals:
            raise ValueError("At least one terminal configuration is required")

@dataclass
class StrategyConfig:
    """Configuration for trading strategy."""
    symbol: str
    timeframes: List[str]
    sequence_length: int
    lot_size: float
    max_positions: int
    stop_loss_pips: int
    take_profit_pips: int
    max_spread_pips: int
    risk_per_trade: float
    max_daily_loss: float
    max_daily_trades: int
    cooldown_period: int
    volatility_threshold: float = 2.0
    trend_threshold: float = 0.7
    position_sizing_factor: float = 0.5

    def __post_init__(self):
        """Validate configuration parameters."""
        if not self.symbol:
            raise ValueError("Symbol cannot be empty")
        if not self.timeframes:
            raise ValueError("At least one timeframe must be specified")
        if self.sequence_length < 1:
            raise ValueError("Sequence length must be positive")
        if self.lot_size <= 0:
            raise ValueError("Lot size must be positive")
        if self.max_positions < 1:
            raise ValueError("Max positions must be at least 1")
        if self.stop_loss_pips < 0:
            raise ValueError("Stop loss pips cannot be negative")
        if self.take_profit_pips < 0:
            raise ValueError("Take profit pips cannot be negative")
        if self.max_spread_pips < 0:
            raise ValueError("Max spread pips cannot be negative")
        if not 0 < self.risk_per_trade <= 1:
            raise ValueError("Risk per trade must be between 0 and 1")
        if self.max_daily_loss <= 0:
            raise ValueError("Max daily loss must be positive")
        if self.max_daily_trades < 1:
            raise ValueError("Max daily trades must be at least 1")
        if self.cooldown_period < 0:
            raise ValueError("Cooldown period cannot be negative")
        if self.volatility_threshold <= 0:
            raise ValueError("Volatility threshold must be positive")
        if not 0 < self.trend_threshold <= 1:
            raise ValueError("Trend threshold must be between 0 and 1")
        if not 0 < self.position_sizing_factor <= 1:
            raise ValueError("Position sizing factor must be between 0 and 1")

@dataclass
class ModelConfig:
    """Configuration for a single model."""
    model_path: str
    input_dim: int
    output_dim: int
    weight: float
    FEATURE_COLUMNS: List[str] = field(default_factory=list)

    # Common parameters
    sequence_length: int = 288
    batch_size: int = 32
    epochs: int = 100
    patience: int = 10
    learning_rate: float = 0.001
    dropout_rate: float = 0.2

    # LSTM/GRU specific
    hidden_units: int = 64
    num_layers: int = 2
    dense_units: int = 32

    # XGBoost/LightGBM specific
    max_depth: int = 6
    n_estimators: int = 100
    subsample: float = 0.8
    colsample_bytree: float = 0.8
    min_child_weight: int = 1
    gamma: float = 0
    num_leaves: int = 31

    # Training configuration
    validation_split: float = 0.2
    test_split: float = 0.15
    early_stopping: bool = True
    save_best_only: bool = True
    monitor_metric: str = 'val_loss'

    # Model type for proper handling
    model_type: str = ''

    def __post_init__(self):
        """Validate configuration parameters."""
        if not self.model_path:
            raise ValueError("Model path cannot be empty")
        if self.input_dim <= 0:
            raise ValueError("Input dimension must be positive")
        if self.output_dim <= 0:
            raise ValueError("Output dimension must be positive")
        if not 0 <= self.weight <= 1:
            raise ValueError("Weight must be between 0 and 1")
        if not self.FEATURE_COLUMNS:
            raise ValueError("Feature columns cannot be empty")
        if self.sequence_length < 1:
            raise ValueError("Sequence length must be positive")
        if self.batch_size < 1:
            raise ValueError("Batch size must be positive")
        if self.epochs < 1:
            raise ValueError("Epochs must be positive")
        if self.patience < 1:
            raise ValueError("Patience must be positive")
        if self.learning_rate <= 0:
            raise ValueError("Learning rate must be positive")
        if not 0 <= self.dropout_rate <= 1:
            raise ValueError("Dropout rate must be between 0 and 1")
        if self.hidden_units < 1:
            raise ValueError("Hidden units must be positive")
        if self.num_layers < 1:
            raise ValueError("Number of layers must be positive")
        if self.dense_units < 1:
            raise ValueError("Dense units must be positive")
        if self.max_depth < 1:
            raise ValueError("Max depth must be positive")
        if self.n_estimators < 1:
            raise ValueError("Number of estimators must be positive")
        if not 0 < self.subsample <= 1:
            raise ValueError("Subsample must be between 0 and 1")
        if not 0 < self.colsample_bytree <= 1:
            raise ValueError("Colsample by tree must be between 0 and 1")
        if self.min_child_weight < 0:
            raise ValueError("Min child weight cannot be negative")
        if self.gamma < 0:
            raise ValueError("Gamma cannot be negative")
        if self.num_leaves < 1:
            raise ValueError("Number of leaves must be positive")

        # Validate new training parameters
        if not 0 < self.validation_split < 1:
            raise ValueError("Validation split must be between 0 and 1")
        if not 0 < self.test_split < 1:
            raise ValueError("Test split must be between 0 and 1")
        if self.validation_split + self.test_split >= 1:
            raise ValueError("Validation split + test split must be less than 1")
        if self.monitor_metric not in ['val_loss', 'val_accuracy', 'val_mae', 'val_mse', 'val_rmse']:
            raise ValueError("Monitor metric must be a valid validation metric")

@dataclass
class SystemConfig:
    """System configuration."""
    log_level: str = "INFO"
    log_file: str = "logs/system.log"
    min_memory_gb: int = 4
    environment: str = "development"
    data_dir: str = "data"
    models_dir: str = "models"
    reports_dir: str = "reports"

    def __post_init__(self):
        """Validate configuration parameters."""
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            raise ValueError(f"Invalid log level. Must be one of {valid_log_levels}")
        if self.min_memory_gb < 1:
            raise ValueError("Minimum memory must be at least 1GB")
        valid_environments = ["development", "testing", "production"]
        if self.environment not in valid_environments:
            raise ValueError(f"Invalid environment. Must be one of {valid_environments}")

@dataclass
class MonitoringConfig:
    """Monitoring configuration."""
    report_interval: int = 10
    metrics_interval: int = 5
    plot_interval: int = 10
    save_interval: int = 10
    output_dir: str = "monitoring"
    plots_dir: str = "plots"
    reports_dir: str = "reports"
    max_log_files: int = 10
    max_log_size_mb: int = 100

    def __post_init__(self):
        """Validate configuration parameters."""
        if self.report_interval < 1:
            raise ValueError("Report interval must be positive")
        if self.metrics_interval < 1:
            raise ValueError("Metrics interval must be positive")
        if self.plot_interval < 1:
            raise ValueError("Plot interval must be positive")
        if self.save_interval < 1:
            raise ValueError("Save interval must be positive")
        if self.max_log_files < 1:
            raise ValueError("Max log files must be positive")
        if self.max_log_size_mb < 1:
            raise ValueError("Max log size must be positive")

@dataclass
class TradingConfig:
    """Main trading configuration."""
    mt5: MT5Config
    strategy: StrategyConfig
    models: Dict[str, ModelConfig]
    data_base_path: str
    models_base_path: str
    confidence_threshold: float
    update_interval: int
    max_memory_usage: float
    log_level: str
    debug_mode: bool
    system: SystemConfig = field(default_factory=SystemConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)

    def __post_init__(self):
        """Validate configuration parameters."""
        if not self.data_base_path:
            raise ValueError("Data base path cannot be empty")
        if not self.models_base_path:
            raise ValueError("Models base path cannot be empty")
        if not 0 <= self.confidence_threshold <= 1:
            raise ValueError("Confidence threshold must be between 0 and 1")
        if self.update_interval < 1:
            raise ValueError("Update interval must be positive")
        if not 0 < self.max_memory_usage <= 100:
            raise ValueError("Max memory usage must be between 0 and 100")
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            raise ValueError(f"Invalid log level. Must be one of {valid_log_levels}")

class ConfigurationManager:
    """
    Unified configuration manager for the trading bot.
    Handles loading, validation, and access to configuration values.
    Properly handles sensitive data and ensures consistent path handling.
    """

    _instance = None
    _config = None

    def __new__(cls, *_args, **_kwargs):
        """Singleton pattern implementation."""
        # Ignore args and kwargs for singleton pattern
        _ = _args, _kwargs  # Suppress unused parameter warnings
        if cls._instance is None:
            cls._instance = super(ConfigurationManager, cls).__new__(cls)
        return cls._instance

    def __init__(self,
                config_path: Union[str, Path] = 'config/config.json',
                credentials_path: Union[str, Path] = 'config/credentials.py',
                local_config_path: Union[str, Path] = 'config/local_config.json',
                schema_path: Union[str, Path] = 'config/schemas/config.json'):
        """
        Initialize the configuration manager.

        Args:
            config_path: Path to the main configuration file
            credentials_path: Path to the credentials file
            local_config_path: Path to the local configuration file
            schema_path: Path to the schema file for validation
        """
        if self._config is None:
            self.config_path = Path(config_path)
            self.credentials_path = Path(credentials_path)
            self.local_config_path = Path(local_config_path)
            self.schema_path = Path(schema_path)
            self._lock = threading.RLock()

            # Load configuration
            self._load_config()

            # Set up directories
            self._setup_directories()

            logger.info("Configuration manager initialized")

    def _load_config(self):
        """Load configuration from files."""
        try:
            # Load main configuration
            with open(self.config_path, 'r') as f:
                config_data = json.load(f)

            # Load local configuration if available
            local_config = {}
            if self.local_config_path.exists():
                try:
                    with open(self.local_config_path, 'r') as f:
                        local_config = json.load(f)
                except Exception as e:
                    logger.warning(f"Failed to load local configuration: {str(e)}")

            # Load credentials if available
            credentials = {}
            if self.credentials_path.exists():
                try:
                    # Import credentials module
                    import importlib.util
                    spec = importlib.util.spec_from_file_location("credentials", self.credentials_path)
                    credentials_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(credentials_module)

                    # Get MT5 terminals from credentials
                    if hasattr(credentials_module, 'MT5_TERMINALS'):
                        credentials['mt5_terminals'] = credentials_module.MT5_TERMINALS
                except Exception as e:
                    logger.warning(f"Failed to load credentials: {str(e)}")

            # Create MT5 terminal configurations
            mt5_terminals = {}

            # First, use terminals from main config
            for terminal_id, terminal_config in config_data['mt5']['terminals'].items():
                mt5_terminals[terminal_id] = MT5TerminalConfig(
                    path=terminal_config['path'],
                    login=terminal_config['login'],
                    password=terminal_config['password'],
                    server=terminal_config['server'],
                    trade_mode=terminal_config.get('trade_mode', True),
                    auto_trading=terminal_config.get('auto_trading', True)
                )

            # Override with terminals from local config if available
            if 'mt5_terminals' in local_config:
                for terminal_id, terminal_config in local_config['mt5_terminals'].items():
                    # Convert terminal_id to string if it's not already
                    terminal_id_str = str(terminal_id)

                    # Create or update terminal config
                    if terminal_id_str in mt5_terminals:
                        # Update existing terminal config
                        for key, value in terminal_config.items():
                            if value:  # Only update if value is not empty
                                setattr(mt5_terminals[terminal_id_str], key, value)
                    else:
                        # Create new terminal config
                        mt5_terminals[terminal_id_str] = MT5TerminalConfig(
                            path=terminal_config['path'],
                            login=terminal_config['login'],
                            password=terminal_config['password'],
                            server=terminal_config['server'],
                            trade_mode=terminal_config.get('trade_mode', True),
                            auto_trading=terminal_config.get('auto_trading', True)
                        )

            # Override with terminals from credentials if available
            if 'mt5_terminals' in credentials:
                for terminal_id, terminal_config in credentials['mt5_terminals'].items():
                    # Convert terminal_id to string if it's not already
                    terminal_id_str = str(terminal_id)

                    # Create or update terminal config
                    if terminal_id_str in mt5_terminals:
                        # Update existing terminal config
                        for key, value in terminal_config.items():
                            if value:  # Only update if value is not empty
                                setattr(mt5_terminals[terminal_id_str], key, value)
                    else:
                        # Create new terminal config
                        mt5_terminals[terminal_id_str] = MT5TerminalConfig(
                            path=terminal_config['path'],
                            login=terminal_config['login'],
                            password=terminal_config['password'],
                            server=terminal_config['server'],
                            trade_mode=terminal_config.get('trade_mode', True),
                            auto_trading=terminal_config.get('auto_trading', True)
                        )

            # Create MT5Config
            mt5_config = MT5Config(
                max_connections=config_data['mt5']['max_connections'],
                timeout=config_data['mt5']['timeout'],
                retry_interval=config_data['mt5']['retry_interval'],
                terminals=mt5_terminals,
                auto_start_terminals=config_data['mt5'].get('auto_start_terminals', True)
            )

            # Create StrategyConfig
            strategy_config = StrategyConfig(
                symbol=config_data['strategy']['symbol'],
                timeframes=config_data['strategy']['timeframes'],
                sequence_length=config_data['strategy']['sequence_length'],
                lot_size=config_data['strategy']['lot_size'],
                max_positions=config_data['strategy']['max_positions'],
                stop_loss_pips=config_data['strategy']['stop_loss_pips'],
                take_profit_pips=config_data['strategy']['take_profit_pips'],
                max_spread_pips=config_data['strategy']['max_spread_pips'],
                risk_per_trade=config_data['strategy']['risk_per_trade'],
                max_daily_loss=config_data['strategy']['max_daily_loss'],
                max_daily_trades=config_data['strategy']['max_daily_trades'],
                cooldown_period=config_data['strategy']['cooldown_period'],
                volatility_threshold=config_data['strategy'].get('volatility_threshold', 2.0),
                trend_threshold=config_data['strategy'].get('trend_threshold', 0.7),
                position_sizing_factor=config_data['strategy'].get('position_sizing_factor', 0.5)
            )

            # Create ModelConfigs
            models_config = {}
            for model_name, model_data in config_data['models'].items():
                models_config[model_name] = ModelConfig(
                    model_path=model_data['model_path'],
                    input_dim=model_data['input_dim'],
                    output_dim=model_data['output_dim'],
                    weight=model_data['weight'],
                    FEATURE_COLUMNS=model_data.get('FEATURE_COLUMNS', []),
                    sequence_length=model_data.get('sequence_length', 288),
                    batch_size=model_data.get('batch_size', 32),
                    epochs=model_data.get('epochs', 100),
                    patience=model_data.get('patience', 10),
                    learning_rate=model_data.get('learning_rate', 0.001),
                    dropout_rate=model_data.get('dropout_rate', 0.2),
                    hidden_units=model_data.get('hidden_units', 64),
                    num_layers=model_data.get('num_layers', 2),
                    dense_units=model_data.get('dense_units', 32),
                    max_depth=model_data.get('max_depth', 6),
                    n_estimators=model_data.get('n_estimators', 100),
                    subsample=model_data.get('subsample', 0.8),
                    colsample_bytree=model_data.get('colsample_bytree', 0.8),
                    min_child_weight=model_data.get('min_child_weight', 1),
                    gamma=model_data.get('gamma', 0),
                    num_leaves=model_data.get('num_leaves', 31),
                    validation_split=model_data.get('validation_split', 0.2),
                    test_split=model_data.get('test_split', 0.15),
                    early_stopping=model_data.get('early_stopping', True),
                    save_best_only=model_data.get('save_best_only', True),
                    monitor_metric=model_data.get('monitor_metric', 'val_loss'),
                    model_type=model_data.get('model_type', model_name.lower())
                )

                # Add transformer-specific parameters if they exist
                for key, value in model_data.items():
                    if key not in ['model_path', 'input_dim', 'output_dim', 'weight', 'FEATURE_COLUMNS',
                                 'sequence_length', 'batch_size', 'epochs', 'patience', 'learning_rate',
                                 'dropout_rate', 'hidden_units', 'num_layers', 'dense_units', 'max_depth',
                                 'n_estimators', 'subsample', 'colsample_bytree', 'min_child_weight',
                                 'gamma', 'num_leaves', 'validation_split', 'test_split', 'early_stopping',
                                 'save_best_only', 'monitor_metric', 'model_type']:
                        setattr(models_config[model_name], key, value)

            # Create SystemConfig
            system_config = SystemConfig(
                log_level=config_data.get('log_level', 'INFO'),
                log_file=config_data.get('log_file', 'logs/system.log'),
                min_memory_gb=config_data.get('min_memory_gb', 4),
                environment=config_data.get('environment', 'development'),
                data_dir=config_data.get('data_base_path', 'data'),
                models_dir=config_data.get('models_base_path', 'models'),
                reports_dir=config_data.get('reports_dir', 'reports')
            )

            # Create MonitoringConfig
            monitoring_config = MonitoringConfig(
                report_interval=config_data.get('report_interval', 10),
                metrics_interval=config_data.get('metrics_interval', 5),
                plot_interval=config_data.get('plot_interval', 10),
                save_interval=config_data.get('save_interval', 10),
                output_dir=config_data.get('output_dir', 'monitoring'),
                plots_dir=config_data.get('plots_dir', 'plots'),
                reports_dir=config_data.get('reports_dir', 'reports'),
                max_log_files=config_data.get('max_log_files', 10),
                max_log_size_mb=config_data.get('max_log_size_mb', 100)
            )

            # Create TradingConfig
            self._config = TradingConfig(
                mt5=mt5_config,
                strategy=strategy_config,
                models=models_config,
                data_base_path=config_data['data_base_path'],
                models_base_path=config_data['models_base_path'],
                confidence_threshold=config_data['confidence_threshold'],
                update_interval=config_data['update_interval'],
                max_memory_usage=config_data['max_memory_usage'],
                log_level=config_data['log_level'],
                debug_mode=config_data['debug_mode'],
                system=system_config,
                monitoring=monitoring_config
            )

            logger.info("Configuration loaded successfully")

        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            raise

    def _setup_directories(self):
        """Set up directories for data, models, and logs."""
        try:
            # Normalize paths using pathlib for cross-platform compatibility
            data_path = Path(self._config.data_base_path).resolve()
            models_path = Path(self._config.models_base_path).resolve()
            log_path = Path(self._config.system.log_file).parent.resolve()
            monitoring_path = Path(self._config.monitoring.output_dir).resolve()
            plots_path = monitoring_path / self._config.monitoring.plots_dir
            reports_path = monitoring_path / self._config.monitoring.reports_dir

            # Create directories if they don't exist
            data_path.mkdir(parents=True, exist_ok=True)
            models_path.mkdir(parents=True, exist_ok=True)
            log_path.mkdir(parents=True, exist_ok=True)
            monitoring_path.mkdir(parents=True, exist_ok=True)
            plots_path.mkdir(parents=True, exist_ok=True)
            reports_path.mkdir(parents=True, exist_ok=True)

            # Update config with normalized paths
            self._config.data_base_path = str(data_path)
            self._config.models_base_path = str(models_path)

            logger.info("Directories set up successfully")
        except Exception as e:
            logger.error(f"Error setting up directories: {str(e)}")
            raise

    def get_config(self) -> TradingConfig:
        """Get the trading configuration.

        Returns:
            TradingConfig: The trading configuration
        """
        with self._lock:
            return self._config

    def get_mt5_config(self) -> MT5Config:
        """Get the MT5 configuration.

        Returns:
            MT5Config: The MT5 configuration
        """
        with self._lock:
            return self._config.mt5

    def get_strategy_config(self) -> StrategyConfig:
        """Get the strategy configuration.

        Returns:
            StrategyConfig: The strategy configuration
        """
        with self._lock:
            return self._config.strategy

    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """Get the configuration for a specific model.

        Args:
            model_name: Name of the model

        Returns:
            ModelConfig: The model configuration, or None if not found
        """
        with self._lock:
            return self._config.models.get(model_name)

    def get_all_model_configs(self) -> Dict[str, ModelConfig]:
        """Get all model configurations.

        Returns:
            Dict[str, ModelConfig]: Dictionary of model configurations
        """
        with self._lock:
            return self._config.models

    def get_system_config(self) -> SystemConfig:
        """Get the system configuration.

        Returns:
            SystemConfig: The system configuration
        """
        with self._lock:
            return self._config.system

    def get_monitoring_config(self) -> MonitoringConfig:
        """Get the monitoring configuration.

        Returns:
            MonitoringConfig: The monitoring configuration
        """
        with self._lock:
            return self._config.monitoring

    def validate(self) -> bool:
        """Validate the configuration.

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Basic validation
            if not self._config:
                logger.error("Configuration not loaded")
                return False

            # Validate MT5 terminals
            if not self._config.mt5.terminals:
                logger.error("No MT5 terminals configured")
                return False

            # Validate strategy
            if not self._config.strategy.timeframes:
                logger.error("No timeframes configured")
                return False

            # Validate models
            if not self._config.models:
                logger.error("No models configured")
                return False

            logger.info("Configuration validation successful")
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    def save_config(self, path: Optional[Union[str, Path]] = None) -> bool:
        """Save the configuration to a file.

        Args:
            path: Path to save the configuration to. If None, uses the default path.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self._lock:
                # Use default path if none provided
                if path is None:
                    path = self.config_path
                else:
                    path = Path(path)

                # Convert config to dictionary
                config_dict = asdict(self._config)

                # Remove sensitive information
                if 'mt5' in config_dict and 'terminals' in config_dict['mt5']:
                    for terminal_id, terminal in config_dict['mt5']['terminals'].items():
                        if 'password' in terminal:
                            terminal['password'] = '********'

                # Save to file
                with open(path, 'w') as f:
                    json.dump(config_dict, f, indent=4)

                logger.info(f"Configuration saved to {path}")
                return True
        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False

    def update_config(self, config_updates: Dict[str, Any]) -> bool:
        """Update the configuration with new values.

        Args:
            config_updates: Dictionary of configuration updates

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self._lock:
                # Update configuration
                for key, value in config_updates.items():
                    if hasattr(self._config, key):
                        setattr(self._config, key, value)
                    else:
                        logger.warning(f"Unknown configuration key: {key}")

                logger.info("Configuration updated")
                return True
        except Exception as e:
            logger.error(f"Error updating configuration: {str(e)}")
            return False

    def reload_config(self) -> bool:
        """Reload the configuration from the file.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self._lock:
                self._load_config()
                logger.info("Configuration reloaded")
                return True
        except Exception as e:
            logger.error(f"Error reloading configuration: {str(e)}")
            return False

    def get_terminal_config(self, terminal_id: str) -> Optional[MT5TerminalConfig]:
        """Get the configuration for a specific terminal.

        Args:
            terminal_id: ID of the terminal

        Returns:
            MT5TerminalConfig: The terminal configuration, or None if not found
        """
        with self._lock:
            # Convert terminal_id to string if it's not already
            terminal_id_str = str(terminal_id)
            return self._config.mt5.terminals.get(terminal_id_str)

    def get_terminal_ids(self) -> List[str]:
        """Get the IDs of all configured terminals.

        Returns:
            List[str]: List of terminal IDs
        """
        with self._lock:
            return list(self._config.mt5.terminals.keys())

    def get_timeframes(self) -> List[str]:
        """Get the timeframes configured for the strategy.

        Returns:
            List[str]: List of timeframes
        """
        with self._lock:
            return self._config.strategy.timeframes

    def get_symbol(self) -> str:
        """Get the symbol configured for the strategy.

        Returns:
            str: The symbol
        """
        with self._lock:
            return self._config.strategy.symbol

    def get_data_path(self, terminal_id: str = None, timeframe: str = None) -> Path:
        """
        Get the standardized data path for a specific terminal and timeframe.

        Args:
            terminal_id: Optional terminal ID
            timeframe: Optional timeframe

        Returns:
            Path: Standardized data path
        """
        with self._lock:
            base_path = Path(self._config.data_base_path)

            if terminal_id is None and timeframe is None:
                return base_path

            if terminal_id is not None:
                # Normalize terminal ID
                terminal_id_str = str(terminal_id).replace("terminal", "")
                base_path = base_path / f"terminal_{terminal_id_str}"

            if timeframe is not None:
                base_path = base_path / timeframe

            return base_path

    def get_model_path(self, model_name: str, terminal_id: str = None, timeframe: str = None) -> Path:
        """
        Get the standardized model path for a specific model, terminal, and timeframe.

        Args:
            model_name: Name of the model
            terminal_id: Optional terminal ID
            timeframe: Optional timeframe

        Returns:
            Path: Standardized model path
        """
        with self._lock:
            base_path = Path(self._config.models_base_path)

            if terminal_id is not None:
                # Normalize terminal ID
                terminal_id_str = str(terminal_id).replace("terminal", "")
                base_path = base_path / f"terminal_{terminal_id_str}"

            if timeframe is not None:
                base_path = base_path / timeframe

            # Add model name to path
            model_path = base_path / f"{model_name}_model"

            return model_path


# Example usage
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        # Create configuration manager
        config_manager = ConfigurationManager()

        # Validate configuration
        if config_manager.validate():
            print("Configuration is valid")

            # Get configuration
            trading_config = config_manager.get_config()

            # Print some configuration details
            print(f"\nMT5 Terminals: {len(trading_config.mt5.terminals)}")
            print(f"Strategy Symbol: {trading_config.strategy.symbol}")
            print(f"Timeframes: {trading_config.strategy.timeframes}")
            print(f"Models:")
            for model_name, model_config in trading_config.models.items():
                print(f"  {model_name}: {model_config.model_path}")
        else:
            print("Configuration validation failed")
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
