"""
Trading signal generator with advanced features including market regime detection,
outlier filtering, and ensemble prediction.
"""
import logging
import numpy as np
import pandas as pd
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import os
import matplotlib.pyplot as plt
from scipy import stats
from enum import Enum
from collections import deque
import MetaTrader5 as mt5

from utils.enhanced_error_handler import EnhancedErrorHandler
from utils.model_manager import ModelManager
from config import ConfigurationManager

# Import ModelInputAdapter from the root level
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from model_adapter import ModelInputAdapter

logger = logging.getLogger(__name__)

@dataclass
class TradingSignal:
    """Trading signal with metadata."""
    action: str  # 'buy', 'sell', or 'hold'
    symbol: str
    confidence: float
    timestamp: datetime
    source_model: str
    price: Optional[float] = None
    volume: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    prediction: Optional[float] = None
    expected_return: Optional[float] = None
    volatility: Optional[float] = None
    market_regime: Optional[str] = None
    signal_strength: Optional[float] = None
    model_predictions: Optional[Dict[str, float]] = None
    metadata: Optional[Dict[str, Any]] = None
    profit: float = 0.0
    profit_loss: float = 0.0

class MarketRegime(Enum):
    """Enum representing different market regimes."""
    TRENDING_UP = "TRENDING_UP"
    TRENDING_DOWN = "TRENDING_DOWN"
    RANGING = "RANGING"
    VOLATILE = "VOLATILE"
    UNKNOWN = "UNKNOWN"

class MarketRegimeDetector:
    """
    Detects market regimes (trending, ranging, volatile) based on various indicators.
    """

    def __init__(
        self,
        lookback_periods: Dict[str, int] = None,
        volatility_threshold: float = 3.0,  # Increased for BTCUSD.a
        trend_threshold: float = 0.8,  # Increased for BTCUSD.a
        regime_change_threshold: float = 0.8  # Increased for BTCUSD.a
    ):
        """
        Initialize market regime detector.
        Adjusted for BTCUSD.a market characteristics.

        Args:
            lookback_periods: Dictionary of lookback periods for different timeframes
            volatility_threshold: Threshold for classifying high volatility
            trend_threshold: Threshold for classifying trending market
            regime_change_threshold: Threshold for confirming regime change
        """
        self.lookback_periods = lookback_periods or {
            'short': 20,  # Increased for crypto
            'medium': 60,  # Increased for crypto
            'long': 120  # Increased for crypto
        }
        self.volatility_threshold = volatility_threshold
        self.trend_threshold = trend_threshold
        self.regime_change_threshold = regime_change_threshold

        # Regime history
        self.regime_history = []
        self.current_regime = None
        self.regime_start_time = None

        logger.info("Market regime detector initialized for BTCUSD.a")

    def detect_regime(self, data: pd.DataFrame) -> str:
        """
        Detect current market regime based on price data.
        Adjusted for BTCUSD.a market characteristics.

        Args:
            data: DataFrame with price data (requires 'close' and datetime index)

        Returns:
            str: Detected regime ('trending_up', 'trending_down', 'ranging', 'volatile')
        """
        try:
            if len(data) < max(self.lookback_periods.values()):
                logger.warning("Insufficient data for regime detection")
                return 'unknown'

            # Ensure we have the close price
            if 'close' not in data.columns:
                logger.error("Data must contain 'close' column")
                return 'unknown'

            # Calculate volatility indicators (adjusted for BTCUSD.a)
            for period_name, period in self.lookback_periods.items():
                # Standard deviation-based volatility
                data[f'volatility_{period_name}'] = data['close'].rolling(period).std() / data['close'].rolling(period).mean()

                # True range-based volatility
                if all(col in data.columns for col in ['high', 'low']):
                    tr = np.maximum(
                        data['high'] - data['low'],
                        np.maximum(
                            np.abs(data['high'] - data['close'].shift(1)),
                            np.abs(data['low'] - data['close'].shift(1))
                        )
                    )
                    data[f'atr_{period_name}'] = tr.rolling(period).mean() / data['close'].rolling(period).mean()

            # Calculate trend indicators (adjusted for BTCUSD.a)
            for period_name, period in self.lookback_periods.items():
                # Directional movement
                data[f'direction_{period_name}'] = np.sign(data['close'].diff(period))

                # Linear regression slope
                data[f'slope_{period_name}'] = self._calculate_slope(data['close'], period)

                # ADX-like trend strength
                if all(col in data.columns for col in ['high', 'low']):
                    data[f'trend_strength_{period_name}'] = self._calculate_trend_strength(data, period)

            # Make decision based on regime indicators
            latest = data.iloc[-1]

            # Get volatility score
            vol_scores = [latest[f'volatility_{p}'] for p in self.lookback_periods.keys()]
            vol_score = np.mean(vol_scores)

            # Get trend scores
            trend_dir_scores = [latest[f'direction_{p}'] for p in self.lookback_periods.keys()]
            trend_slope_scores = [latest[f'slope_{p}'] for p in self.lookback_periods.keys()]

            # Combine trend direction scores
            trend_dir = np.sign(np.sum(trend_dir_scores))

            # Combine trend strength scores
            if 'trend_strength_medium' in latest:
                trend_strength = latest['trend_strength_medium']
            else:
                trend_strength = np.abs(np.mean(trend_slope_scores))

            # Determine regime
            if vol_score > self.volatility_threshold:
                regime = 'volatile'
            elif trend_strength > self.trend_threshold:
                if trend_dir > 0:
                    regime = 'trending_up'
                else:
                    regime = 'trending_down'
            else:
                regime = 'ranging'

            # Check for regime change
            if self.current_regime != regime:
                # Require additional confirmation for regime change
                if len(self.regime_history) > 5:
                    prev_regimes = [r for r, _ in self.regime_history[-5:]]
                    if prev_regimes.count(self.current_regime) >= 3:
                        # Current regime is still dominant, ignore temporary change
                        regime = self.current_regime

            # Update regime history
            timestamp = data.index[-1]
            self.regime_history.append((regime, timestamp))

            # Update current regime
            if self.current_regime != regime:
                logger.info(f"Market regime changed from {self.current_regime} to {regime}")
                self.current_regime = regime
                self.regime_start_time = timestamp

            return regime

        except Exception as e:
            logger.error(f"Error detecting market regime: {str(e)}")
            return 'unknown'

    def _calculate_slope(self, series: pd.Series, period: int) -> float:
        """Calculate linear regression slope for a series."""
        if len(series) < period:
            return 0.0

        values = series.iloc[-period:].values
        x = np.arange(len(values))
        slope, _, _, _, _ = stats.linregress(x, values)

        # Normalize by average price to get comparable slope
        return slope / np.mean(values)

    def _calculate_trend_strength(self, data: pd.DataFrame, period: int) -> float:
        """Calculate trend strength similar to ADX indicator."""
        if len(data) < period + 1:
            return 0.0

        # Use simplified version of directional movement
        up_move = data['high'].diff(1)
        down_move = data['low'].diff(1).abs()

        plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)

        # Create smoothed averages
        tr = np.maximum(
            data['high'] - data['low'],
            np.maximum(
                np.abs(data['high'] - data['close'].shift(1)),
                np.abs(data['low'] - data['close'].shift(1))
            )
        )
        atr = tr.rolling(period).mean()

        plus_di = 100 * pd.Series(plus_dm).rolling(period).mean() / atr
        minus_di = 100 * pd.Series(minus_dm).rolling(period).mean() / atr

        # Calculate DX
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)

        # Return latest value
        return dx.iloc[-1] / 100  # Normalize to 0-1 range

    def get_regime_summary(self) -> Dict[str, Any]:
        """Get summary of recent market regimes."""
        if not self.regime_history:
            return {
                'current_regime': 'unknown',
                'duration': 0,
                'stability': 0,
                'regime_counts': {}
            }

        # Get current regime info
        current_regime = self.current_regime or self.regime_history[-1][0]

        # Calculate duration
        if self.regime_start_time:
            duration = (datetime.now() - self.regime_start_time).total_seconds() / 60  # minutes
        else:
            duration = 0

        # Count recent regimes
        recent_regimes = [r for r, _ in self.regime_history[-20:]]
        regime_counts = {r: recent_regimes.count(r) for r in set(recent_regimes)}

        # Calculate stability (how consistent the regime has been)
        if len(recent_regimes) > 0:
            stability = regime_counts.get(current_regime, 0) / len(recent_regimes)
        else:
            stability = 0

        return {
            'current_regime': current_regime,
            'duration': duration,
            'stability': stability,
            'regime_counts': regime_counts
        }

@dataclass
class MarketContext:
    """Container for market context information."""
    regime: MarketRegime = MarketRegime.UNKNOWN
    volatility: float = 0.0
    trend_strength: float = 0.0
    recent_change: float = 0.0
    volume_profile: str = "NORMAL"
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None


class SignalGenerator:
    """
    Generates trading signals based on model predictions, market context, and ensembling.
    """

    def __init__(self, model_manager: ModelManager, config_manager: ConfigurationManager,
                 error_handler: EnhancedErrorHandler, terminal_id: str, timeframe: str):
        """
        Initialize the SignalGenerator.

        Args:
            model_manager: The context-specific ModelManager instance.
            config_manager: The ConfigurationManager instance.
            error_handler: The shared ErrorHandler instance.
            terminal_id: The terminal ID for this context.
            timeframe: The timeframe for this context.
        """
        self.model_manager = model_manager
        self.config_manager = config_manager
        self.error_handler = error_handler
        self.terminal_id = terminal_id
        self.timeframe = timeframe

        # Get configuration
        try:
            self.strategy_config = self.config_manager.get_strategy_config()
            self.trading_config = self.config_manager.get_config()

            # Thresholds from configuration
            self.confidence_threshold = getattr(self.trading_config, 'confidence_threshold', 0.6)

            # Default values for missing attributes
            self.outlier_threshold = getattr(self.strategy_config, 'outlier_threshold', 3.0)
            self.history_adjustment_factor = getattr(self.strategy_config, 'history_adjustment_factor', 0.9)

            logger.info(f"[{self.terminal_id}] Loaded configuration: confidence_threshold={self.confidence_threshold}, " +
                       f"outlier_threshold={self.outlier_threshold}, history_adjustment_factor={self.history_adjustment_factor}")
        except Exception as e:
            logger.warning(f"[{self.terminal_id}] Error loading configuration: {str(e)}. Using default values.")
            # Set default values if configuration loading fails
            self.strategy_config = None
            self.trading_config = None
            self.confidence_threshold = 0.6
            self.outlier_threshold = 3.0
            self.history_adjustment_factor = 0.9

        # Initialize market regime detector
        self.market_regime_detector = MarketRegimeDetector()

        # Initialize signal tracking
        self.recent_signals = deque(maxlen=10)  # Store last 10 signals for history adjustment
        self.signal_history = []  # For analysis and visualization

        # Initialize prediction cache
        self.prediction_cache = {}
        self.cache_expiry_seconds = 300  # Cache predictions for 5 minutes

        # Initialize performance metrics
        self.performance_metrics = {}
        try:
            for model_name in self.model_manager.get_all_models().keys():
                self.performance_metrics[model_name] = {
                    'correct_predictions': 0,
                    'incorrect_predictions': 0,
                    'accuracy': 0.0,
                    'avg_return': 0.0,
                    'avg_return_count': 0,
                    'last_update': datetime.now()
                }
        except Exception as e:
            logger.warning(f"[{self.terminal_id}] Error initializing performance metrics: {str(e)}")

        logger.info(f"SignalGenerator initialized for Terminal {self.terminal_id}, Timeframe {self.timeframe}")

    def generate_signal(self, X: np.ndarray, current_data_point: Optional[pd.Series] = None) -> Optional[TradingSignal]:
        """
        Generate a trading signal based on model predictions and market context.

        Args:
            X: Preprocessed input data for models (e.g., sequence of features).
            current_data_point: The most recent row of unprocessed data (optional, for price/volume info).

        Returns:
            TradingSignal object or None if no signal is generated.
        """
        try:
            # 1. Detect Market Regime
            market_regime = MarketRegime.UNKNOWN

            # Try to detect market regime if we have data in the right format
            if isinstance(X, np.ndarray) and len(X.shape) == 3:
                try:
                    # Convert X to DataFrame for regime detection if possible
                    # This assumes X has shape (batch_size, seq_len, features)
                    # and the features are in a standard order (open, high, low, close, volume, etc.)
                    if X.shape[2] >= 4:  # Need at least OHLC data
                        # Create a simple DataFrame with just the required columns
                        df_data = {
                            'close': X[0, :, 3],  # Assuming close is the 4th feature
                            'open': X[0, :, 0],   # Assuming open is the 1st feature
                            'high': X[0, :, 1],   # Assuming high is the 2nd feature
                            'low': X[0, :, 2],    # Assuming low is the 3rd feature
                        }

                        if X.shape[2] >= 5:
                            df_data['volume'] = X[0, :, 4]  # Add volume if available

                        # Create DataFrame with dummy index
                        import pandas as pd
                        df = pd.DataFrame(df_data)

                        # Detect market regime
                        market_regime = self._detect_market_regime(df)
                except Exception as e:
                    logger.warning(f"[{self.terminal_id}] Failed to detect market regime from input data: {e}")
                    market_regime = MarketRegime.UNKNOWN

            # Create market context
            market_context = MarketContext(regime=market_regime)
            logger.debug(f"[{self.terminal_id}] Market Regime detected: {market_regime}")

            # 2. Get Predictions from Models (Using ModelManager)
            # predictions = self._get_predictions(model_inputs)
            predictions = self._get_predictions(X) # Use X directly
            if not predictions:
                logger.warning(f"[{self.terminal_id}] No model predictions were generated.")
                return None
            logger.debug(f"[{self.terminal_id}] Raw Predictions: {predictions}")

            # 3. Ensemble Predictions
            ensembled_direction, ensembled_confidence = self._ensemble_predictions(predictions, market_context)
            if ensembled_direction == 0: # Hold signal
                logger.info(f"[{self.terminal_id}] Ensembled signal is HOLD (Direction: {ensembled_direction}, Confidence: {ensembled_confidence:.4f})")
                # Optionally create and return a HOLD signal object
                # return TradingSignal(action='hold', ..., confidence=ensembled_confidence)
                return None # Treat hold as no actionable signal for now
            logger.debug(f"[{self.terminal_id}] Ensembled Signal: Direction={ensembled_direction}, Confidence={ensembled_confidence:.4f}")

            # 4. Apply Confidence Threshold
            if ensembled_confidence < self.confidence_threshold:
                logger.info(f"[{self.terminal_id}] Ensembled confidence ({ensembled_confidence:.4f}) below threshold ({self.confidence_threshold}). Signal ignored.")
                return None

            # 5. Determine Action (Buy/Sell)
            action = 'buy' if ensembled_direction == 1 else 'sell'

            # 6. Calculate SL/TP levels (Needs recent price data)
            # levels = self._calculate_signal_levels(ensembled_direction, ensembled_confidence, features_df.iloc[-1])
            # Placeholder - requires passing recent price data (e.g., current_data_point)
            stop_loss = None
            take_profit = None
            current_price = current_data_point['close'] if current_data_point is not None else None
            current_volume = current_data_point['volume'] if current_data_point is not None else None
            if current_price is not None:
                 sl_pips = self.strategy_config.stop_loss_pips
                 tp_pips = self.strategy_config.take_profit_pips

                 # Get point value safely without direct MT5 dependency
                 try:
                     # Try to get point value from MT5 if available
                     point = mt5.symbol_info(self.strategy_config.symbol).point if mt5 else 0.00001
                 except Exception as e:
                     # Default to standard 5-digit forex point value
                     logger.warning(f"Could not get point value from MT5: {e}. Using default 0.00001")
                     point = 0.00001

                 if action == 'buy':
                      stop_loss = current_price - sl_pips * point
                      take_profit = current_price + tp_pips * point
                 else: # sell
                      stop_loss = current_price + sl_pips * point
                      take_profit = current_price - tp_pips * point

            # 7. Create Signal Object
            signal = TradingSignal(
                action=action,
                symbol=self.strategy_config.symbol,
                confidence=ensembled_confidence,
                timestamp=datetime.now(), # Use current time or data timestamp
                source_model='ensemble',
                price=current_price,
                volume=current_volume,
                stop_loss=stop_loss,
                take_profit=take_profit,
                # prediction=levels.get('prediction'), # If prediction level calculated
                # expected_return=levels.get('expected_return'), # If calculated
                # volatility=levels.get('volatility'), # If calculated
                market_regime=market_regime.value,
                signal_strength=ensembled_confidence, # Or another metric
                model_predictions=predictions,
                metadata=market_context.__dict__ # Store context
            )

            self._store_signal(signal) # Store locally
            logger.info(f"[{self.terminal_id}] Generated Signal: {signal}")
            return signal

        except Exception as e:
            self.error_handler.handle_error(e, context={
                "method": "generate_signal",
                "terminal_id": self.terminal_id,
                "timeframe": self.timeframe
            })
            logger.error(f"[{self.terminal_id}] Error generating signal: {e}", exc_info=True)
            return None

    def _detect_market_regime(self, data: pd.DataFrame) -> MarketRegime:
        """Detect current market regime using the MarketRegimeDetector."""
        try:
            regime_str = self.market_regime_detector.detect_regime(data)

            # Map the string returned by detect_regime to the appropriate MarketRegime enum
            regime_map = {
                'trending_up': MarketRegime.TRENDING_UP,
                'trending_down': MarketRegime.TRENDING_DOWN,
                'ranging': MarketRegime.RANGING,
                'volatile': MarketRegime.VOLATILE,
                'unknown': MarketRegime.UNKNOWN
            }

            return regime_map.get(regime_str.lower(), MarketRegime.UNKNOWN)
        except Exception as e:
            self.error_handler.handle_error(e, context={
                "method": "_detect_market_regime",
                "terminal_id": self.terminal_id,
                "timeframe": self.timeframe
            })
            logger.error(f"[{self.terminal_id}] Failed to detect market regime: {e}")
            return MarketRegime.UNKNOWN

    def _get_predictions(self, X: np.ndarray) -> Dict[str, float]:
        """
        Get predictions from all available models.

        Args:
            X: Input data for prediction

        Returns:
            Dictionary mapping model names to prediction values
        """
        predictions = {}

        # Validate input
        if X is None or not isinstance(X, np.ndarray):
            logger.error(f"[{self.terminal_id}] Invalid input for prediction: {type(X)}")
            return predictions

        # Check if X is empty
        if X.size == 0:
            logger.error(f"[{self.terminal_id}] Empty input array for prediction")
            return predictions

        # Get all models from the model manager
        try:
            models = self.model_manager.get_all_models()
            if not models:
                logger.warning(f"[{self.terminal_id}] No models available for prediction")
                return predictions
        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error getting models: {str(e)}")
            return predictions

        # Process each model
        for model_name, model in models.items():
            try:
                # Check model health status from manager
                if not self.model_manager.model_health.get(model_name, False):
                    logger.warning(f"[{self.terminal_id}] Skipping prediction for unhealthy model: {model_name}")
                    continue

                # Simple cache check using hash of the input array
                try:
                    # Create a more efficient cache key
                    # Use the first and last few values plus shape as a fingerprint
                    if X.size > 10:
                        fingerprint = tuple(X.flatten()[:5]) + tuple(X.flatten()[-5:]) + X.shape
                    else:
                        fingerprint = tuple(X.flatten()) + X.shape

                    cache_key = (model_name, hash(fingerprint))
                    current_time = time.time()

                    if cache_key in self.prediction_cache:
                        pred, timestamp = self.prediction_cache[cache_key]
                        if current_time - timestamp < self.cache_expiry_seconds:
                            logger.debug(f"[{self.terminal_id}] Using cached prediction for {model_name}")
                            predictions[model_name] = pred
                            continue
                        else:
                            del self.prediction_cache[cache_key]  # Expired
                except Exception as cache_error:
                    logger.warning(f"[{self.terminal_id}] Error checking prediction cache: {str(cache_error)}")
                    # Continue without using cache

                # Get model configuration
                try:
                    model_config = self.model_manager.get_model_config(model_name)

                    # Default values if model_config is None or empty
                    if not model_config:
                        model_config = {
                            'sequence_length': 60,
                            'input_dim': 5
                        }
                        logger.warning(f"[{self.terminal_id}] No config found for {model_name}, using defaults")

                    # Get sequence length and input dimension
                    sequence_length = model_config.get('sequence_length', 60)
                    input_dim = model_config.get('input_dim', 5)

                    # Adapt input to match model expectations using the improved adapter
                    adapted_X = ModelInputAdapter.adapt_input(
                        X,
                        (sequence_length, input_dim),
                        model_type=model_name  # Pass model type for specialized handling
                    )

                    if adapted_X is None:
                        logger.warning(f"[{self.terminal_id}] Failed to adapt input for {model_name}. Trying default adapter.")
                        # Try with default settings as fallback
                        adapted_X = ModelInputAdapter.adapt_input(X, (60, 5), model_type=model_name)

                        if adapted_X is None:
                            logger.warning(f"[{self.terminal_id}] Default adapter also failed for {model_name}. Skipping.")
                            continue

                    # Predict using the model's predict method with adapted input
                    logger.debug(f"[{self.terminal_id}] Getting prediction from {model_name} with adapted input shape: {adapted_X.shape if hasattr(adapted_X, 'shape') else type(adapted_X)}")
                    result = model.predict(adapted_X)

                except Exception as e:
                    logger.warning(f"[{self.terminal_id}] Error adapting input for {model_name}: {e}. Trying raw input.")
                    # Last resort: try with raw input
                    try:
                        result = model.predict(X)
                    except Exception as raw_error:
                        logger.error(f"[{self.terminal_id}] Failed to predict with raw input for {model_name}: {raw_error}")
                        continue

                # Process result (assuming single value prediction for ensemble)
                try:
                    if isinstance(result, np.ndarray):
                        # Take the last prediction if it's a sequence, or the first if single batch item
                        if result.ndim == 3:
                            pred_value = result[0, -1, 0] if result.shape[2] > 0 else result[0, -1]
                        elif result.ndim == 2:
                            pred_value = result[0, 0]
                        elif result.ndim == 1:
                            pred_value = result[0]
                        else:
                            pred_value = float(result)
                    elif isinstance(result, (float, int)):
                        pred_value = float(result)
                    elif isinstance(result, dict):
                        # Handle dictionary output (e.g., from TFT models)
                        if 'prediction' in result:
                            pred_value = float(result['prediction'])
                        else:
                            # Try to find a numeric value in the dictionary
                            for _, value in result.items():
                                if isinstance(value, (float, int, np.ndarray)):
                                    pred_value = float(value) if isinstance(value, (float, int)) else float(value.flatten()[0])
                                    break
                            else:
                                logger.warning(f"[{self.terminal_id}] Could not extract prediction from dictionary result: {result}")
                                continue
                    else:
                        logger.warning(f"[{self.terminal_id}] Unexpected prediction result type from {model_name}: {type(result)}")
                        continue

                    # Ensure the prediction is a valid number
                    if np.isnan(pred_value) or np.isinf(pred_value):
                        logger.warning(f"[{self.terminal_id}] Invalid prediction value from {model_name}: {pred_value}")
                        continue

                    predictions[model_name] = pred_value

                    # Update cache
                    try:
                        self.prediction_cache[cache_key] = (pred_value, current_time)
                    except Exception as cache_error:
                        logger.warning(f"[{self.terminal_id}] Error updating prediction cache: {str(cache_error)}")

                except Exception as proc_error:
                    logger.error(f"[{self.terminal_id}] Error processing prediction result from {model_name}: {str(proc_error)}")
                    continue

            except Exception as e:
                self.error_handler.handle_error(e, context={
                    "method": "_get_predictions",
                    "model_name": model_name,
                    "terminal_id": self.terminal_id,
                    "timeframe": self.timeframe
                })
                logger.error(f"[{self.terminal_id}] Error predicting with model {model_name}: {str(e)}")

        return predictions

    def _ensemble_predictions(self, predictions: Dict[str, float], market_context: MarketContext) -> Tuple[int, float]:
        """
        Combine predictions from multiple models using weighted averaging, adjusted by market regime.
        Returns prediction direction (-1 sell, 0 hold, 1 buy) and confidence (0-1).
        """
        if not predictions:
            return 0, 0.0

        total_weight = 0.0
        weighted_sum = 0.0
        final_weights = {}

        # Get model weights from model manager (already terminal-specific)
        model_weights = self.model_manager.get_model_weights()

        # Log the weights being used for this terminal
        logger.debug(f"[{self.terminal_id}] Using model weights: {model_weights}")

        for model_name, prediction in predictions.items():
            # Check if we have a weight for this model
            if model_name not in model_weights:
                logger.warning(f"[{self.terminal_id}] Weight not found for model {model_name} during ensembling.")
                continue

            base_weight = model_weights[model_name]

            # Adjust weight based on market regime
            adjusted_weight = base_weight

            # Apply market regime adjustments if we have a valid regime
            if market_context is not None and market_context.regime != MarketRegime.UNKNOWN:
                # Boost neural models in trending markets
                if market_context.regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]:
                    if model_name in ['lstm', 'gru', 'tft']:
                        adjusted_weight *= 1.2  # Boost neural models in trends

                # Boost tree models in ranging/volatile markets
                elif market_context.regime in [MarketRegime.RANGING, MarketRegime.VOLATILE]:
                    if model_name in ['xgboost', 'lightgbm']:
                        adjusted_weight *= 1.2  # Boost tree models in ranging/volatile markets

            # Store the final weight for logging
            final_weights[model_name] = adjusted_weight
            total_weight += adjusted_weight

            # Apply the weight to the prediction
            weighted_sum += prediction * adjusted_weight

            # Log individual model contributions
            logger.debug(f"[{self.terminal_id}] Model {model_name}: prediction={prediction:.4f}, weight={adjusted_weight:.4f}")

        if total_weight == 0:
            logger.warning(f"[{self.terminal_id}] Total weight is zero, no valid predictions available.")
            return 0, 0.0

        # Calculate final prediction (weighted average)
        final_prediction = weighted_sum / total_weight
        logger.debug(f"[{self.terminal_id}] Final weighted prediction: {final_prediction:.4f}")

        # Convert prediction value to direction (-1 sell, 0 hold, 1 buy)
        # Use a small threshold to avoid noise
        direction_threshold = 0.0001
        if final_prediction > direction_threshold:
             direction = 1
             logger.debug(f"[{self.terminal_id}] Direction: BUY (prediction={final_prediction:.4f})")
        elif final_prediction < -direction_threshold:
             direction = -1
             logger.debug(f"[{self.terminal_id}] Direction: SELL (prediction={final_prediction:.4f})")
        else:
             direction = 0
             logger.debug(f"[{self.terminal_id}] Direction: HOLD (prediction={final_prediction:.4f})")

        # Calculate confidence based on prediction magnitude and consistency
        raw_confidence = abs(final_prediction)

        # Scale confidence based on prediction magnitude and model agreement
        # Higher confidence if prediction is stronger and models agree
        model_agreement = self._calculate_model_agreement(predictions, direction)
        confidence = min(max(raw_confidence * (0.5 + 0.5 * model_agreement), 0.0), 1.0)

        logger.debug(f"[{self.terminal_id}] Confidence: {confidence:.4f} (raw={raw_confidence:.4f}, agreement={model_agreement:.2f})")

        # Apply history adjustment to avoid overtrading in the same direction
        history_adjustment = self._calculate_history_adjustment(direction)
        adjusted_confidence = confidence * history_adjustment

        logger.debug(f"[{self.terminal_id}] Adjusted confidence: {adjusted_confidence:.4f} (history_factor={history_adjustment:.2f})")

        return direction, adjusted_confidence

    def _calculate_model_agreement(self, predictions: Dict[str, float], direction: int) -> float:
        """
        Calculate how much the models agree with each other.

        Args:
            predictions: Dictionary of model predictions
            direction: Overall direction (-1, 0, 1)

        Returns:
            float: Agreement score between 0 and 1
        """
        if not predictions or len(predictions) <= 1:
            return 1.0  # Default to full agreement with only one model

        # Count models that agree with the overall direction
        agreeing_models = 0
        for pred_value in predictions.values():
            # Check if prediction agrees with overall direction
            if (direction > 0 and pred_value > 0) or (direction < 0 and pred_value < 0) or (direction == 0 and abs(pred_value) < 0.0001):
                agreeing_models += 1

        # Calculate agreement ratio
        agreement = agreeing_models / len(predictions)
        return agreement

    def _is_outlier(self, prediction: float, all_predictions: List[float]) -> bool:
        """
        Check if a prediction is an outlier using z-score

        Args:
            prediction: The prediction to check
            all_predictions: List of all predictions

        Returns:
            True if the prediction is an outlier, False otherwise
        """
        if len(all_predictions) < 3:  # Need at least 3 predictions for meaningful z-score
            return False

        # Calculate z-score
        mean = np.mean(all_predictions)
        std = np.std(all_predictions) + 1e-10  # Add small epsilon to avoid division by zero
        z_score = abs((prediction - mean) / std)

        return z_score > self.outlier_threshold

    def _calculate_history_adjustment(self, current_direction: int) -> float:
        """
        Calculate adjustment factor based on signal history consistency

        Args:
            current_direction: Current signal direction

        Returns:
            Adjustment factor between 0.8 and 1.2
        """
        # Check if recent_signals exists and has data
        if not hasattr(self, 'recent_signals') or not self.recent_signals:
            return 1.0

        try:
            # Get last few signals
            last_signals = [s.get('direction', 0) for s in list(self.recent_signals)[-5:]]

            # If no signals after filtering, return default
            if not last_signals:
                return 1.0

            # Count consistent signals
            consistent_count = sum(1 for s in last_signals if s == current_direction)

            # Consistency ratio
            consistency_ratio = consistent_count / len(last_signals)

            # Adjustment factor: 0.8 for inconsistent signals, 1.2 for very consistent ones
            adjustment = 0.8 + 0.4 * consistency_ratio

            return adjustment

        except Exception as e:
            logger.warning(f"Error calculating history adjustment: {str(e)}")
            return 1.0  # Default to neutral adjustment on error

    def _calculate_signal_levels(self, direction: int, confidence: float,
                                last_price_data: pd.Series, market_context: MarketContext = None) -> Dict[str, Any]:
        """
        Calculate entry, stop-loss and take-profit levels for the signal

        Args:
            direction: Signal direction (-1 for sell, 1 for buy)
            confidence: Signal confidence
            last_price_data: Last row from the price dataframe
            market_context: Optional market context information

        Returns:
            Signal dictionary with all required information
        """
        # Extract price information
        close = last_price_data['close']
        # high and low could be used for more advanced SL/TP calculations
        # but we're using a simpler approach based on fixed pip values
        point = 0.00001  # Assume 5-digit forex pair, should be obtained from symbol info

        # Calculate base stop-loss and take-profit distances
        sl_pips = self.strategy_config.stop_loss_pips
        tp_pips = self.strategy_config.take_profit_pips

        # Adjust based on recent volatility (ATR)
        if 'atr' in last_price_data:
            volatility_factor = last_price_data['atr'] / (point * 20)  # Normalize to baseline ATR
            sl_pips = max(sl_pips, int(sl_pips * volatility_factor))
            tp_pips = max(tp_pips, int(tp_pips * volatility_factor))

        # Calculate entry price (current close)
        entry_price = close

        # Calculate stop-loss and take-profit levels
        if direction == 1:  # Buy
            stop_loss = entry_price - (sl_pips * point)
            take_profit = entry_price + (tp_pips * point)
        else:  # Sell
            stop_loss = entry_price + (sl_pips * point)
            take_profit = entry_price - (tp_pips * point)

        # Create signal dictionary
        signal = {
            'symbol': self.strategy_config.symbol,
            'direction': direction,
            'action': 'buy' if direction == 1 else 'sell',
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'risk_percent': self.strategy_config.risk_per_trade,
            'confidence': confidence,
            'timestamp': datetime.now().isoformat(),
            'market_regime': market_context.regime.value if market_context else 'unknown',
            'signal_type': 'market'  # As opposed to 'limit' or 'stop' orders
        }

        return signal

    def preprocess_data(self, data: pd.DataFrame) -> np.ndarray:
        """
        Preprocess data for model input.

        Args:
            data: DataFrame with price data

        Returns:
            np.ndarray: Preprocessed data for model input
        """
        try:
            # Check if data is None or empty
            if data is None or len(data) == 0:
                logger.warning(f"[{self.terminal_id}] No data provided for preprocessing, returning default array")
                return np.zeros((1, 100, 5), dtype=np.float32)

            # Make a copy to avoid modifying the original data
            data = data.copy()

            # Basic preprocessing - can be enhanced based on model requirements
            # Ensure we have the required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    logger.warning(f"[{self.terminal_id}] Missing required column: {col}")
                    # Add dummy column if missing
                    if col == 'volume':
                        data[col] = 0
                    else:
                        data[col] = data['close'] if 'close' in data.columns else 0

            # Calculate basic features
            # Returns
            data['return'] = data['close'].pct_change()
            data['return_1d'] = data['close'].pct_change(1)
            data['return_5d'] = data['close'].pct_change(5)

            # Moving averages
            data['ma_5'] = data['close'].rolling(5).mean()
            data['ma_10'] = data['close'].rolling(10).mean()
            data['ma_20'] = data['close'].rolling(20).mean()

            # Volatility
            data['volatility_5'] = data['close'].rolling(5).std()
            data['volatility_10'] = data['close'].rolling(10).std()

            # Fill NaN values using backward fill then zeros
            data = data.bfill().fillna(0)

            # Normalize data
            for col in data.columns:
                if col != 'time' and data[col].std() > 0:
                    data[col] = (data[col] - data[col].mean()) / data[col].std()

            # Get the last sequence_length rows
            sequence_length = 100  # Adjust based on model requirements
            if len(data) > sequence_length:
                data = data.iloc[-sequence_length:]

            # Convert to numpy array
            X = data.values.astype(np.float32)

            # Reshape for model input (batch_size, sequence_length, features)
            X = X.reshape(1, X.shape[0], X.shape[1])

            return X

        except Exception as e:
            logger.error(f"Error preprocessing data: {str(e)}")
            # Return a default array if preprocessing fails
            return np.zeros((1, 100, 5), dtype=np.float32)

    def _store_signal(self, signal: TradingSignal) -> None:
        """
        Store signal in history

        Args:
            signal: Trading signal
        """
        # Initialize signal_history if not already done
        if not hasattr(self, 'signal_history'):
            self.signal_history = []

        # Store the signal
        self.signal_history.append(signal)

        # Add to recent_signals deque for history adjustment
        signal_dict = {
            'action': signal.action,
            'direction': 1 if signal.action == 'buy' else -1 if signal.action == 'sell' else 0,
            'confidence': signal.confidence,
            'timestamp': signal.timestamp
        }
        self.recent_signals.append(signal_dict)

    def visualize_signals(self, n_days: int = 7) -> None:
        """
        Visualize recent trading signals.

        Args:
            n_days: Number of days to visualize
        """
        # Check if signal_history exists and has data
        if not hasattr(self, 'signal_history') or not self.signal_history:
            logger.warning("No signals in history to visualize")
            return

        try:
            # Filter recent signals
            cutoff_date = datetime.now() - timedelta(days=n_days)
            recent_signals = [
                signal for signal in self.signal_history
                if signal.timestamp >= cutoff_date
            ]

            if not recent_signals:
                logger.warning(f"No signals in the last {n_days} days")
                return

            # Create figure
            plt.figure(figsize=(12, 8))

            # Extract data
            timestamps = [signal.timestamp for signal in recent_signals]
            prices = [signal.price for signal in recent_signals if signal.price is not None]

            # Plot price
            if prices:
                plt.subplot(311)
                plt.plot(timestamps, prices, 'k-', label='Price')
                plt.title(f"{self.strategy_config.symbol} Price and Signals (Last {n_days} Days)")
                plt.legend()
                plt.grid(True)

            # Plot signals
            plt.subplot(312)
            for signal in recent_signals:
                if signal.action == 'buy':
                    plt.plot(signal.timestamp, signal.confidence, 'g^', markersize=10)
                elif signal.action == 'sell':
                    plt.plot(signal.timestamp, signal.confidence, 'rv', markersize=10)

            plt.ylabel('Signal Confidence')
            plt.grid(True)

            # Plot market regimes
            regimes = [signal.market_regime for signal in recent_signals]
            unique_regimes = list(set(regimes))
            regime_colors = {
                'trending_up': 'g',
                'trending_down': 'r',
                'ranging': 'b',
                'volatile': 'y',
                'unknown': 'k'
            }

            plt.subplot(313)
            for i, regime in enumerate(unique_regimes):
                if regime is None:
                    continue

                regime_timestamps = [
                    signal.timestamp for signal in recent_signals
                    if signal.market_regime == regime
                ]

                if regime_timestamps:
                    plt.plot(
                        regime_timestamps,
                        [i] * len(regime_timestamps),
                        marker='o',
                        linestyle='',
                        color=regime_colors.get(regime, 'k'),
                        label=regime
                    )

            plt.yticks(range(len(unique_regimes)), unique_regimes)
            plt.ylabel('Market Regime')
            plt.grid(True)
            plt.legend()

            # Save figure
            output_dir = 'reports'
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, f"{self.strategy_config.symbol}_signals.png"))
            plt.close()

            logger.info(f"Saved signal visualization to {output_dir}/{self.strategy_config.symbol}_signals.png")

        except Exception as e:
            logger.error(f"Error visualizing signals: {str(e)}")

    def record_signal_result(self, signal: TradingSignal, result: Dict[str, Any]) -> None:
        """
        Record the result of a trading signal.

        Args:
            signal: Trading signal
            result: Dictionary with trade result (profit, etc.)
        """
        try:
            # Update performance metrics
            profit = result.get('profit', 0)

            # Initialize performance metrics if not already done
            if not hasattr(self, 'performance_metrics'):
                self.performance_metrics = {}
                for model_name in self.model_manager.get_all_models().keys():
                    self.performance_metrics[model_name] = {
                        'correct_predictions': 0,
                        'incorrect_predictions': 0,
                        'accuracy': 0.0,
                        'avg_return': 0.0,
                        'avg_return_count': 0,
                        'last_update': datetime.now()
                    }

            # Update metrics for each model
            for model_name, prediction in signal.model_predictions.items():
                if model_name not in self.performance_metrics:
                    # Initialize metrics for this model if not already done
                    self.performance_metrics[model_name] = {
                        'correct_predictions': 0,
                        'incorrect_predictions': 0,
                        'accuracy': 0.0,
                        'avg_return': 0.0,
                        'avg_return_count': 0,
                        'last_update': datetime.now()
                    }

                metrics = self.performance_metrics[model_name]

                # Determine if prediction was correct
                prediction_correct = (prediction > 0 and profit > 0) or (prediction < 0 and profit < 0)

                if prediction_correct:
                    metrics['correct_predictions'] += 1
                else:
                    metrics['incorrect_predictions'] += 1

                # Update accuracy
                total_preds = metrics['correct_predictions'] + metrics['incorrect_predictions']
                if total_preds > 0:
                    metrics['accuracy'] = metrics['correct_predictions'] / total_preds

                # Update average return
                if metrics.get('avg_return_count', 0) > 0:
                    metrics['avg_return'] = (
                        (metrics['avg_return'] * metrics['avg_return_count'] + profit) /
                        (metrics['avg_return_count'] + 1)
                    )
                    metrics['avg_return_count'] += 1
                else:
                    metrics['avg_return'] = profit
                    metrics['avg_return_count'] = 1

                metrics['last_update'] = datetime.now()

            # Update model weights periodically if we have performance metrics
            if self.performance_metrics and any('last_update' in metrics for metrics in self.performance_metrics.values()):
                try:
                    last_updates = [metrics['last_update'] for metrics in self.performance_metrics.values()
                                   if 'last_update' in metrics]
                    if last_updates:
                        last_update = min(last_updates)
                        if (datetime.now() - last_update).total_seconds() > 3600:  # Update hourly
                            self._update_model_weights()
                except Exception as weight_error:
                    logger.error(f"Error updating model weights: {str(weight_error)}")

            logger.info(f"Recorded signal result: profit={profit}")

        except Exception as e:
            self.error_handler.handle_error(e, context={
                "method": "record_signal_result",
                "terminal_id": self.terminal_id,
                "timeframe": self.timeframe
            })
            logger.error(f"Error recording signal result: {str(e)}")

    def _update_model_weights(self) -> None:
        """
        Update model weights based on performance metrics.
        Models with higher accuracy and returns get higher weights.
        """
        try:
            if not hasattr(self, 'performance_metrics') or not self.performance_metrics:
                logger.warning("No performance metrics available for updating model weights")
                return

            # Calculate new weights based on accuracy and average return
            new_weights = {}
            total_score = 0.0

            for model_name, metrics in self.performance_metrics.items():
                # Skip models with no predictions
                if metrics.get('correct_predictions', 0) + metrics.get('incorrect_predictions', 0) == 0:
                    continue

                # Calculate score based on accuracy and average return
                accuracy = metrics.get('accuracy', 0.0)
                avg_return = metrics.get('avg_return', 0.0)

                # Combine metrics into a score (can be adjusted)
                # Higher accuracy and positive returns lead to higher score
                score = accuracy * (1.0 + max(0, avg_return))

                # Ensure minimum weight
                score = max(0.05, score)

                new_weights[model_name] = score
                total_score += score

            # Normalize weights
            if total_score > 0:
                for model_name in new_weights:
                    new_weights[model_name] /= total_score

                # Update model weights in model manager
                self.model_manager.set_model_weights(new_weights)
                logger.info(f"Updated model weights based on performance: {new_weights}")
            else:
                logger.warning("Could not update model weights: total score is zero")

        except Exception as e:
            self.error_handler.handle_error(e, context={
                "method": "_update_model_weights",
                "terminal_id": self.terminal_id,
                "timeframe": self.timeframe
            })
            logger.error(f"Error updating model weights: {str(e)}")