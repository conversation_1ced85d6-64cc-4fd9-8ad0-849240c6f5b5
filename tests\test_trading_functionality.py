"""
Test script to validate trading functionality for MT5 terminals.
"""
import os
import sys
import time
from datetime import datetime, timedelta
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
# Try to import from credentials first, fall back to test config if not available
try:
    from config.credentials import MT5_TERMINALS as mt5_configs
except ImportError:
    from tests.test_config import mt5_configs
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_terminal(terminal_id: str = "1"):
    """Test trading functionality for a single terminal."""
    # Convert terminal_id to int if it's a string of digits
    if isinstance(terminal_id, str) and terminal_id.isdigit():
        terminal_id = int(terminal_id)
    config = mt5_configs[terminal_id]

    try:
        logger.info(f"\nTesting terminal {terminal_id}...")
        logger.info(f"Path: {config['path']}")
        logger.info(f"Login: {config['login']}")
        logger.info(f"Server: {config['server']}")

        # Initialize MT5 with minimal configuration
        if not mt5.initialize(path=config['path']):
            error = mt5.last_error()
            logger.error(f"Failed to initialize. Error code: {error[0]}, Description: {error[1]}")
            return False

        # Give MT5 a moment to stabilize
        time.sleep(2)

        # Get terminal info
        terminal_info = mt5.terminal_info()
        if terminal_info is not None:
            logger.info("\nTerminal Info:")
            logger.info(f"Connected: {terminal_info.connected}")
            logger.info(f"Terminal Name: {terminal_info.name}")
            logger.info(f"Terminal Path: {terminal_info.path}")
            logger.info(f"Data Path: {terminal_info.data_path}")
            logger.info(f"Terminal Build: {terminal_info.build}")
        else:
            logger.error("Failed to get terminal info")
            return False

        # Get account info
        account_info = mt5.account_info()
        if account_info is None:
            error = mt5.last_error()
            logger.error(f"Failed to get account info. Error code: {error[0]}, Description: {error[1]}")
            return False

        logger.info(f"\nAccount Info:")
        logger.info(f"Login: {account_info.login}")
        logger.info(f"Server: {account_info.server}")
        logger.info(f"Balance: {account_info.balance}")
        logger.info(f"Equity: {account_info.equity}")
        logger.info(f"Margin: {account_info.margin}")
        logger.info(f"Free Margin: {account_info.margin_free}")
        logger.info(f"Leverage: {account_info.leverage}")

        # Select symbol
        symbol = "BTCUSD.a"
        logger.info(f"\nSelecting symbol {symbol}...")
        if not mt5.symbol_select(symbol, True):
            error = mt5.last_error()
            logger.error(f"Failed to select symbol. Error code: {error[0]}, Description: {error[1]}")
            return False

        # Get symbol info
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            error = mt5.last_error()
            logger.error(f"Failed to get symbol info. Error code: {error[0]}, Description: {error[1]}")
            return False

        logger.info(f"\nSymbol Info:")
        logger.info(f"Bid: {symbol_info.bid}")
        logger.info(f"Ask: {symbol_info.ask}")
        logger.info(f"Spread: {symbol_info.spread} points")
        logger.info(f"Trade Mode: {symbol_info.trade_mode}")

        # Try to place a small test order
        logger.info("\nAttempting to place test order...")
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": 0.01,
            "type": mt5.ORDER_TYPE_BUY,
            "price": symbol_info.ask,
            "deviation": 20,
            "magic": 234000,
            "comment": "Test order",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        result = mt5.order_send(request)
        if result is None:
            error = mt5.last_error()
            logger.error(f"Order failed. Error code: {error[0]}, Description: {error[1]}")
            logger.error("Please ensure AutoTrading is enabled in MT5 (green button)")
            return False

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            logger.error(f"Order failed. Return code: {result.retcode}")
            if hasattr(result, 'comment'):
                logger.error(f"Comment: {result.comment}")
            logger.error("Please ensure AutoTrading is enabled in MT5 (green button)")
            return False

        logger.info("\nTest order placed successfully")
        logger.info(f"Order ticket: {result.order}")
        logger.info(f"Execution price: {result.price}")

        # Close the test order immediately
        logger.info("\nAttempting to close test order...")
        position = mt5.positions_get(ticket=result.order)
        if position is None or len(position) == 0:
            error = mt5.last_error()
            logger.error(f"Failed to get position. Error code: {error[0]}, Description: {error[1]}")
            return False

        close_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": position[0].volume,
            "type": mt5.ORDER_TYPE_SELL,
            "position": result.order,
            "price": symbol_info.bid,
            "deviation": 20,
            "magic": 234000,
            "comment": "Test order close",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        close_result = mt5.order_send(close_request)
        if close_result is None:
            error = mt5.last_error()
            logger.error(f"Close order failed. Error code: {error[0]}, Description: {error[1]}")
            return False

        if close_result.retcode != mt5.TRADE_RETCODE_DONE:
            logger.error(f"Failed to close test order. Return code: {close_result.retcode}")
            if hasattr(close_result, 'comment'):
                logger.error(f"Comment: {close_result.comment}")
            return False

        logger.info("\nTest order closed successfully")
        logger.info(f"Terminal {terminal_id}: All tests passed successfully!")
        assert True  # Test passed

    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        assert False, f"Test failed with error: {str(e)}"

    finally:
        # Don't shut down MT5 to maintain AutoTrading state
        pass

def main():
    """Test trading functionality on a specific terminal."""
    if len(sys.argv) != 2:
        print("\nUsage: python test_trading_functionality.py <terminal_id>")
        print("Example: python test_trading_functionality.py 1")
        print("\nAvailable terminal IDs:", list(mt5_configs.keys()))
        sys.exit(1)

    # Get terminal ID from command line
    terminal_id = sys.argv[1]

    # Convert to int if it's a string of digits
    if terminal_id.isdigit():
        terminal_id_key = int(terminal_id)
    else:
        terminal_id_key = terminal_id

    if terminal_id_key not in mt5_configs:
        print(f"\nError: Terminal ID {terminal_id} not found in config")
        print("Available terminal IDs:", list(mt5_configs.keys()))
        sys.exit(1)

    logger.info("\nStarting trading functionality test...")
    logger.info("\nIMPORTANT: Before proceeding, please ensure:")
    logger.info(f"1. MT5 terminal {terminal_id} is open")
    logger.info("2. Log in to your account in MT5")
    logger.info("3. Enable AutoTrading (green button)")
    logger.info("4. Terminal shows 'AutoTrading enabled' in status bar")
    logger.info("\nPress Enter to continue or Ctrl+C to cancel...")
    input()

    if test_terminal(terminal_id):
        logger.info(f"\nTerminal {terminal_id} passed all trading tests!")
        sys.exit(0)
    else:
        logger.error(f"\nTerminal {terminal_id} failed some trading tests. Please check the logs above.")
        sys.exit(1)

if __name__ == "__main__":
    main()