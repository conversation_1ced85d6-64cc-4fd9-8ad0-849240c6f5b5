#!/usr/bin/env python
"""
Simplified script to collect 6-year historical BTCUSD.a data directly from MT5.

This script:
1. Connects directly to MT5
2. Collects historical OHLCV data for BTCUSD.a for multiple timeframes
3. Saves the data in parquet format
4. Verifies the collected data
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from pathlib import Path
import argparse
import time
import MetaTrader5 as mt5

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('btcusd_data_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('btcusd_data_collection')

# Constants
SYMBOL = "BTCUSD.a"
TIMEFRAMES = ["M5", "M15", "M30", "H1", "H4"]
YEARS = 6  # 6 years of historical data

# MT5 timeframe mapping
TIMEFRAME_MAP = {
    'M1': mt5.TIMEFRAME_M1,
    'M5': mt5.TIMEFRAME_M5,
    'M15': mt5.TIMEFRAME_M15,
    'M30': mt5.TIMEFRAME_M30,
    'H1': mt5.TIMEFRAME_H1,
    'H4': mt5.TIMEFRAME_H4,
    'D1': mt5.TIMEFRAME_D1,
    'W1': mt5.TIMEFRAME_W1,
    'MN1': mt5.TIMEFRAME_MN1
}

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect historical BTCUSD.a data from MT5')
    parser.add_argument('--output_dir', type=str, default='data/raw',
                        help='Output directory for collected data')
    parser.add_argument('--years', type=int, default=6,
                        help='Number of years of historical data to collect')
    parser.add_argument('--force', action='store_true',
                        help='Force data collection even if files already exist')
    return parser.parse_args()

def initialize_mt5():
    """
    Initialize MT5 terminal.
    
    Returns:
        bool: True if initialization was successful, False otherwise
    """
    try:
        # Shutdown MT5 if it's already running
        if mt5.terminal_info() is not None:
            mt5.shutdown()
        
        # Initialize MT5
        if not mt5.initialize():
            logger.error(f"Failed to initialize MT5: {mt5.last_error()}")
            return False
        
        # Check if connection is working
        if not mt5.terminal_info():
            logger.error("Failed to get terminal info")
            return False
        
        logger.info("MT5 initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing MT5: {str(e)}")
        return False

def calculate_technical_indicators(df):
    """
    Calculate technical indicators for a DataFrame.
    
    Args:
        df: DataFrame with OHLCV data
        
    Returns:
        DataFrame with technical indicators added
    """
    try:
        if len(df) < 30:
            logger.warning("Not enough data to calculate all indicators")
            return df
        
        # Make a copy to avoid modifying original
        df = df.copy()
        
        # Calculate RSI (14)
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Calculate MACD
        ema12 = df['close'].ewm(span=12, adjust=False).mean()
        ema26 = df['close'].ewm(span=26, adjust=False).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # Calculate Bollinger Bands (20, 2)
        df['bb_middle'] = df['close'].rolling(20).mean()
        std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (std * 2)
        df['bb_lower'] = df['bb_middle'] - (std * 2)
        
        # Calculate ATR (14)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        df['atr'] = true_range.rolling(14).mean()
        
        # Calculate returns
        df['returns'] = df['close'].pct_change()
        
        # Calculate volatility (14)
        df['volatility'] = df['returns'].rolling(14).std()
        
        # Calculate momentum (14)
        df['momentum'] = df['close'] / df['close'].shift(14) - 1
        
        return df
        
    except Exception as e:
        logger.error(f"Error calculating technical indicators: {str(e)}")
        return df

def collect_historical_data(symbol, timeframe, start_date, end_date, output_dir, force=False):
    """
    Collect historical data for a symbol and timeframe.
    
    Args:
        symbol: Symbol to collect data for
        timeframe: Timeframe string (e.g., "M5")
        start_date: Start date for data collection
        end_date: End date for data collection
        output_dir: Output directory for collected data
        force: Force data collection even if file already exists
        
    Returns:
        Tuple of (success, message, data_path)
    """
    try:
        logger.info(f"Collecting {symbol} {timeframe} data")
        
        # Prepare output path
        output_path = Path(output_dir) / f"{symbol}_{timeframe}.parquet"
        
        # Check if file already exists and is not empty
        if output_path.exists() and output_path.stat().st_size > 0 and not force:
            logger.info(f"Data file already exists: {output_path}")
            
            # Load existing data to verify
            existing_df = pd.read_parquet(output_path)
            logger.info(f"Existing data has {len(existing_df)} rows from {existing_df.index.min()} to {existing_df.index.max()}")
            
            return True, f"Data file already exists: {output_path}", output_path
        
        # Create output directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Get MT5 timeframe constant
        mt5_timeframe = TIMEFRAME_MAP.get(timeframe)
        if mt5_timeframe is None:
            return False, f"Invalid timeframe: {timeframe}", None
        
        # Collect data in chunks to manage memory
        all_data = []
        current_start = start_date
        chunk_size = timedelta(days=30)  # Process 30 days at a time
        
        while current_start < end_date:
            chunk_end = min(current_start + chunk_size, end_date)
            
            logger.info(f"Collecting chunk from {current_start} to {chunk_end} for {symbol} {timeframe}")
            
            # Get rates from MT5
            rates = mt5.copy_rates_range(
                symbol,
                mt5_timeframe,
                current_start,
                chunk_end
            )
            
            if rates is None or len(rates) == 0:
                logger.warning(f"No data returned for {symbol} {timeframe} from {current_start} to {chunk_end}")
                # Move to next chunk
                current_start = chunk_end
                continue
            
            # Convert to DataFrame
            chunk_df = pd.DataFrame(rates)
            chunk_df['time'] = pd.to_datetime(chunk_df['time'], unit='s')
            chunk_df.set_index('time', inplace=True)
            
            # Add to list
            all_data.append(chunk_df)
            logger.info(f"Collected {len(chunk_df)} rows for {symbol} {timeframe} ({current_start} to {chunk_end})")
            
            # Move to next chunk
            current_start = chunk_end
            
            # Sleep to prevent overwhelming MT5
            time.sleep(1)
        
        # Combine all chunks
        if not all_data:
            logger.error(f"No data collected for {symbol} {timeframe}")
            return False, f"No data collected for {symbol} {timeframe}", None
        
        combined_df = pd.concat(all_data)
        
        # Remove duplicates and sort
        combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
        combined_df = combined_df.sort_index()
        
        # Calculate technical indicators
        combined_df = calculate_technical_indicators(combined_df)
        
        # Save to parquet
        combined_df.to_parquet(output_path)
        
        logger.info(f"Successfully saved {len(combined_df)} rows of {symbol} {timeframe} data to {output_path}")
        
        return True, f"Successfully collected data for {symbol} {timeframe}", output_path
        
    except Exception as e:
        logger.error(f"Error collecting data for {symbol} {timeframe}: {str(e)}")
        return False, f"Error: {str(e)}", None

def verify_data(data_path):
    """
    Verify the collected data for completeness and quality.
    
    Args:
        data_path: Path to the parquet file
        
    Returns:
        Tuple of (is_valid, message)
    """
    try:
        # Load the data
        df = pd.read_parquet(data_path)
        
        # Check if data is empty
        if df.empty:
            return False, "Data is empty"
        
        # Check for required columns
        required_columns = ['open', 'high', 'low', 'close', 'tick_volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return False, f"Missing required columns: {missing_columns}"
        
        # Check for NaN values in critical columns
        nan_counts = df[required_columns].isna().sum()
        if nan_counts.sum() > 0:
            nan_cols = [f"{col}: {count}" for col, count in nan_counts.items() if count > 0]
            return False, f"NaN values found: {', '.join(nan_cols)}"
        
        # Validate price relationships (high >= low, etc.)
        invalid_rows = df[(df['high'] < df['low']) |
                        (df['open'] > df['high']) |
                        (df['open'] < df['low']) |
                        (df['close'] > df['high']) |
                        (df['close'] < df['low'])]
        if not invalid_rows.empty:
            return False, f"Found {len(invalid_rows)} rows with invalid price relationships"
        
        return True, f"Data verification successful for {data_path}"
        
    except Exception as e:
        return False, f"Error verifying data: {str(e)}"

def prepare_data_for_training(input_path, output_dir):
    """
    Prepare data for model training by splitting into train/validation/test sets.
    
    Args:
        input_path: Path to the input parquet file
        output_dir: Output directory for processed data
        
    Returns:
        Tuple of (success, message)
    """
    try:
        logger.info(f"Preparing data from {input_path} for model training")
        
        # Load the data
        df = pd.read_parquet(input_path)
        
        # Get symbol and timeframe from filename
        filename = Path(input_path).stem
        parts = filename.split('_')
        symbol = parts[0]
        timeframe = parts[1]
        
        # Create output directories
        train_dir = Path(output_dir) / 'train'
        val_dir = Path(output_dir) / 'validation'
        test_dir = Path(output_dir) / 'test'
        
        for directory in [train_dir, val_dir, test_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Sort by time
        df = df.sort_index()
        
        # Calculate split points (70% train, 15% validation, 15% test)
        total_rows = len(df)
        train_end = int(total_rows * 0.7)
        val_end = train_end + int(total_rows * 0.15)
        
        # Split the data
        train_df = df.iloc[:train_end]
        val_df = df.iloc[train_end:val_end]
        test_df = df.iloc[val_end:]
        
        # Save to parquet files
        train_path = train_dir / f"{symbol}_{timeframe}.parquet"
        val_path = val_dir / f"{symbol}_{timeframe}.parquet"
        test_path = test_dir / f"{symbol}_{timeframe}.parquet"
        
        train_df.to_parquet(train_path)
        val_df.to_parquet(val_path)
        test_df.to_parquet(test_path)
        
        logger.info(f"Saved {len(train_df)} training rows to {train_path}")
        logger.info(f"Saved {len(val_df)} validation rows to {val_path}")
        logger.info(f"Saved {len(test_df)} test rows to {test_path}")
        
        return True, f"Successfully prepared data for {symbol} {timeframe}"
        
    except Exception as e:
        logger.error(f"Error preparing data: {str(e)}")
        return False, f"Error: {str(e)}"

def main():
    """Main function to collect and prepare BTCUSD.a data."""
    args = parse_arguments()
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * args.years)
    
    logger.info(f"Starting data collection for {SYMBOL} from {start_date} to {end_date}")
    logger.info(f"Collecting data for timeframes: {TIMEFRAMES}")
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize MT5
    if not initialize_mt5():
        logger.error("Failed to initialize MT5. Exiting.")
        return 1
    
    # Collect data for each timeframe
    collection_results = []
    for timeframe in TIMEFRAMES:
        success, message, data_path = collect_historical_data(
            SYMBOL,
            timeframe,
            start_date,
            end_date,
            output_dir,
            args.force
        )
        
        collection_results.append({
            'timeframe': timeframe,
            'success': success,
            'message': message,
            'data_path': data_path
        })
        
        logger.info(f"Result for {SYMBOL} {timeframe}: {message}")
    
    # Verify collected data
    logger.info("Verifying collected data...")
    verification_results = []
    for result in collection_results:
        if result['success'] and result['data_path']:
            is_valid, message = verify_data(result['data_path'])
            verification_results.append({
                'timeframe': result['timeframe'],
                'is_valid': is_valid,
                'message': message,
                'data_path': result['data_path']
            })
            logger.info(f"Verification for {SYMBOL} {result['timeframe']}: {message}")
    
    # Prepare data for model training
    logger.info("Preparing data for model training...")
    processed_dir = Path('data/processed')
    processed_dir.mkdir(parents=True, exist_ok=True)
    
    preparation_results = []
    for result in verification_results:
        if result['is_valid']:
            success, message = prepare_data_for_training(
                result['data_path'],
                processed_dir
            )
            preparation_results.append({
                'timeframe': result['timeframe'],
                'success': success,
                'message': message
            })
            logger.info(f"Preparation for {SYMBOL} {result['timeframe']}: {message}")
    
    # Print summary
    logger.info("\n=== Data Collection Summary ===")
    logger.info(f"Total timeframes: {len(TIMEFRAMES)}")
    logger.info(f"Successful collections: {sum(1 for r in collection_results if r['success'])}")
    logger.info(f"Failed collections: {sum(1 for r in collection_results if not r['success'])}")
    
    logger.info("\n=== Data Verification Summary ===")
    logger.info(f"Total verifications: {len(verification_results)}")
    logger.info(f"Valid datasets: {sum(1 for r in verification_results if r['is_valid'])}")
    logger.info(f"Invalid datasets: {sum(1 for r in verification_results if not r['is_valid'])}")
    
    logger.info("\n=== Data Preparation Summary ===")
    logger.info(f"Total preparations: {len(preparation_results)}")
    logger.info(f"Successful preparations: {sum(1 for r in preparation_results if r['success'])}")
    logger.info(f"Failed preparations: {sum(1 for r in preparation_results if not r['success'])}")
    
    # Shutdown MT5
    mt5.shutdown()
    
    logger.info("Data collection, verification, and preparation complete.")
    return 0

if __name__ == "__main__":
    exit(main())
