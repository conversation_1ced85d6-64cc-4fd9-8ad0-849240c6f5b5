"""
Main trading bot module that coordinates all components.
"""
import sys
from pathlib import Path
from typing import Dict, Optional, Any
import logging
from datetime import datetime
import time
import psutil
import threading
import gc
import os
import pandas as pd
from utils.enhanced_error_handler import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON><PERSON>, ErrorSeverity
from utils.circuit_breaker import <PERSON><PERSON>reaker, CircuitOpenError
from utils.model_manager import ModelManager
from utils.thread_manager import ThreadManager
from utils.data_preprocessor import DataPreprocessor
from trading.strategy import TradingStrategy
from trading.executor import TradeExecutor
from trading.signal_generator import SignalGenerator
from monitoring.progress import ProgressVisualizer
from config import ConfigurationManager
from trading.mt5_connection_manager import MT5ConnectionManager

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Import data_collector explicitly if needed, otherwise rely on mt5_manager
# from trading.data_collector import DataCollector # Ambiguous, MT5DataCollector imported above

# Removed: from models.model_registry import ModelRegistry # Not needed
# Removed: from config.config_manager import ConfigurationManager # Imported above

# Removed global config loading - should be passed in
# trading_config = config_service.get_trading_config()
# monitoring_config = config_service.get_monitoring_config()
# model_config = config_service.get_model_config()

logger = logging.getLogger(__name__)

# Removed internal ModelRegistry class definition (lines 72-121)

class TradingBot:
    """
    Main trading bot module responsible for coordinating trading activities for a specific terminal.
    Orchestrates data collection, model prediction, signal generation, and trade execution.
    """

    def __init__(self,
                 config_manager: ConfigurationManager, # Passed in
                 error_handler: EnhancedErrorHandler, # Passed in
                 thread_manager: ThreadManager, # Passed in
                 mt5_manager: MT5ConnectionManager, # Passed in
                 terminal_id: str, # Passed in
                 visualizer: Optional[ProgressVisualizer] = None,
                 memory_manager = None):
        """
        Initialize the trading bot for a specific terminal.

        Args:
            config_manager: Singleton ConfigurationManager instance.
            error_handler: Shared ErrorHandler instance.
            thread_manager: Shared ThreadManager instance.
            mt5_manager: Shared MT5ConnectionManager instance.
            terminal_id: The specific terminal ID this bot instance manages.
            visualizer: Optional shared ProgressVisualizer instance.
        """
        self.config_manager = config_manager
        self.error_handler = error_handler
        self.thread_manager = thread_manager
        self.mt5_manager = mt5_manager
        self.terminal_id = terminal_id
        self.visualizer = visualizer
        self.memory_manager = memory_manager

        # Normalize terminal ID for consistency
        try:
            from utils.common import normalize_terminal_id
            self.terminal_id = normalize_terminal_id(self.terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            self.terminal_id = str(self.terminal_id)

        self.config = self.config_manager.get_config() # Get the main TradingConfig
        self.strategy_config = self.config_manager.get_strategy_config() # Get strategy specifics

        # Determine primary timeframe for this bot instance (can be adjusted if bot needs multi-TF logic)
        # For now, assume first timeframe in strategy config is the primary one
        if not self.strategy_config.timeframes:
            raise ValueError(f"No timeframes defined in strategy config for terminal {self.terminal_id}")
        self.primary_timeframe = self.strategy_config.timeframes[0]
        logger.info(f"Initializing trading bot for Terminal ID: {self.terminal_id}, Primary Timeframe: {self.primary_timeframe}")

        # Setup logging (consider if this should be global or per-bot)
        # self.setup_logging() # Maybe remove if logging is set up globally in main.py
        logger.info(f"Using configuration for symbol: {self.strategy_config.symbol}")

        # BTCUSD.a specific configurations - Consider moving to config file if generalizable
        # self.point_value = 0.1
        # self.min_lot_size = 0.01
        # self.max_lot_size = 1.0
        # self.leverage = 1

        # Memory Manager is likely global, passed in or retrieved if needed
        # self.memory_manager = MemoryManager(...) # Assuming global/passed in

        # Data Collector - uses the passed mt5_manager
        # self.data_collector = DataCollector(...) # Assuming MT5DataCollector used via mt5_manager

        # Data Preprocessor
        try:
            # Create a simple config dictionary with the necessary parameters
            preprocessor_config = {
                'strategy': {
                    'sequence_length': getattr(self.strategy_config, 'sequence_length', 60),
                    'batch_size': 32,
                    'outlier_std_threshold': 3.0,
                    'use_trend_indicators': True,
                    'use_momentum_indicators': True,
                    'use_volatility_indicators': True,
                    'use_volume_indicators': True,
                    'use_time_features': True
                },
                'scaling': {
                    'price_columns': ['open', 'high', 'low', 'close'],
                    'volume_columns': ['volume']
                },
                'preprocessor_max_batch_size': 10000
            }
            self.data_preprocessor = DataPreprocessor(config=preprocessor_config)
            logger.info(f"[{self.terminal_id}] Data preprocessor initialized with sequence length: {preprocessor_config['strategy']['sequence_length']}")
        except Exception as e:
            logger.warning(f"[{self.terminal_id}] Error initializing data preprocessor with config: {str(e)}")
            # Fall back to default initialization
            self.data_preprocessor = DataPreprocessor()
            logger.info(f"[{self.terminal_id}] Data preprocessor initialized with default settings")

        # Instantiate the context-specific ModelManager
        logger.info(f"Initializing ModelManager for Terminal {self.terminal_id}, Timeframe {self.primary_timeframe}")
        self.model_manager = ModelManager(
            config_manager=self.config_manager,
            error_handler=self.error_handler,
            terminal_id=self.terminal_id,
            timeframe=self.primary_timeframe # Use the determined primary timeframe
        )
        # Load models immediately for this context
        self.model_manager.load_all_models()


        # Initialize other trading components, passing necessary managers/configs
        self._init_trading_components()

        # Set up circuit breakers and system health monitors
        self._init_circuit_breakers()

        # Thread for running the bot
        self.running_thread = None
        self.stop_event = threading.Event()

        # Performance metrics
        self.metrics = {
            'terminal_id': self.terminal_id,
            'timeframe': self.primary_timeframe,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'last_update': datetime.now(),
            'processing_stats': None
        }

        # Register recovery handlers for critical errors
        self._register_recovery_handlers()

        logger.info(f"Trading bot initialization complete for Terminal {self.terminal_id}")

    # Removed setup_logging - assume global setup in main.py
    # def setup_logging(self):
    #    ...

    def _register_recovery_handlers(self):
        """Register recovery handlers for critical errors"""
        # Example: MT5 connection issues (MT5Manager likely handles reconnects internally now)
        # self.error_handler.register_recovery_handler(
        #     ConnectionError, # Or specific MT5 error
        #     lambda ctx: self.mt5_manager.reconnect(ctx.get('terminal_id')) # Delegate to manager
        # )
        pass # Add specific handlers as needed

    # Removed _reconnect_mt5 - MT5Manager should handle this
    # def _reconnect_mt5(self, context: Dict[str, Any]) -> bool:
    #    ...

    def _handle_memory_error(self, context: Dict[str, Any]) -> bool:
        """Attempt to recover from a memory error."""
        logger.critical(f"Memory error detected: {context}. Attempting recovery...")
        try:
            # Use memory manager if available
            if self.memory_manager and hasattr(self.memory_manager, 'cleanup_memory'):
                logger.warning("Using memory manager for aggressive cleanup")
                self.memory_manager.cleanup_memory("AGGRESSIVE")
                logger.info("Memory manager cleanup completed")
                return True

            # Fallback to manual cleanup if memory manager not available
            logger.warning("Memory manager not available, performing manual cleanup")

            # Trigger garbage collection
            gc.collect()
            logger.warning("Forced garbage collection.")

            # Clear data caches (if DataProcessor is used and has a clear method)
            if hasattr(self, 'data_processor') and hasattr(self.data_processor, 'clear_cache'):
                self.data_processor.clear_cache()
                logger.warning("Cleared DataProcessor cache.")

            # Clear model caches
            if hasattr(self, 'model_manager') and hasattr(self.model_manager, 'get_all_models'):
                for model in self.model_manager.get_all_models().values():
                    if hasattr(model, 'clear_cache'):
                        try:
                            model.clear_cache()
                            logger.debug(f"Cleared cache for {model.model_name}")
                        except Exception as cache_err:
                            logger.error(f"Error clearing cache for {model.model_name}: {cache_err}")

            # Check memory usage again
            process = psutil.Process(os.getpid())
            mem_info = process.memory_info()
            logger.info(f"Memory usage after recovery attempt: {mem_info.rss / (1024 * 1024):.2f} MB")
            return True # Indicate recovery attempt made
        except Exception as e:
            logger.error(f"Recovery attempt from memory error failed during cleanup: {e}")
            return False # Recovery failed

    def _handle_model_error(self, context: Dict[str, Any]) -> bool:
        """Attempt to recover from a model loading or prediction error."""
        model_name = context.get('model_name', 'unknown')
        logger.error(f"Error related to model '{model_name}': {context.get('error')}. Attempting recovery...")
        try:
            # Mark the specific model as unhealthy
            if hasattr(self, 'model_manager') and model_name != 'unknown':
                self.model_manager.model_health[model_name] = False
                logger.warning(f"Marked model '{model_name}' as unhealthy.")

            # Attempt to reload the problematic model (optional, might retry the error)
            # if hasattr(self, 'model_manager') and model_name != 'unknown':
            #     logger.info(f"Attempting to reload model '{model_name}'...")
            #     reload_success = self.model_manager.reload_model(model_name)
            #     if reload_success:
            #         logger.info(f"Successfully reloaded model '{model_name}'.")
            #         return True # Recovered by reloading
            #     else:
            #         logger.error(f"Failed to reload model '{model_name}'.")

            # Strategy should ideally fall back to other healthy models
            logger.warning("Trading strategy should now attempt to use fallback models.")
            return True # Indicate recovery attempt (marking as unhealthy) was made
        except Exception as e:
            logger.error(f"Recovery from model error failed: {e}")
            return False

    # ... (Keep other _handle_* methods if relevant, update context/logging) ...

    # Removed _start_memory_monitoring - Handled by global MemoryManager in main.py
    # def _start_memory_monitoring(self):
    #    ...

    def _init_trading_components(self):
        """Initialize components like Strategy, Executor, Signal Generator"""
        logger.info("Initializing trading components...")
        try:
            # Signal Generator - needs access to models via ModelManager
            self.signal_generator = SignalGenerator(
                model_manager=self.model_manager, # Pass the context-specific manager
                config_manager=self.config_manager,
                error_handler=self.error_handler,
                terminal_id=self.terminal_id,
                timeframe=self.primary_timeframe
            )

            # Get terminal-specific allocation if available
            terminal_allocation = 1.0  # Default to 100% allocation
            try:
                config = self.config_manager.get_config()
                if hasattr(config, 'terminal_model_pairings') and config.terminal_model_pairings:
                    terminal_config = config.terminal_model_pairings.get(self.terminal_id)
                    if terminal_config and 'allocation' in terminal_config:
                        # Convert percentage to decimal (e.g., 20 -> 0.2)
                        terminal_allocation = float(terminal_config['allocation']) / 100.0
                        logger.info(f"Using terminal-specific allocation of {terminal_allocation:.2f} for terminal {self.terminal_id}")
            except Exception as e:
                logger.warning(f"Error loading terminal allocation: {str(e)}")
                logger.warning(f"Using default allocation of 1.0 for terminal {self.terminal_id}")

            # Trading Strategy - needs SignalGenerator
            self.strategy = TradingStrategy(
                signal_generator=self.signal_generator,
                config=self.strategy_config, # Pass StrategyConfig
                error_handler=self.error_handler,
                allocation_factor=terminal_allocation  # Pass terminal-specific allocation
            )

            # Trade Executor - needs MT5 connection via MT5Manager
            self.trade_executor = TradeExecutor(
                mt5_manager=self.mt5_manager,
                config=self.strategy_config, # Pass StrategyConfig
                error_handler=self.error_handler,
                terminal_id=self.terminal_id
            )
            logger.info("Trading components initialized successfully.")
        except Exception as e:
            self.error_handler.handle_error(
                exception=e,
                context={"method": "_init_trading_components", "terminal_id": self.terminal_id},
                source="TradingBot._init_trading_components",
                severity=ErrorSeverity.CRITICAL,
                category=ErrorCategory.SYSTEM
            )
            logger.critical(f"Failed to initialize critical trading components: {e}", exc_info=True)
            raise # Reraise critical initialization error

    def _init_circuit_breakers(self):
        """Initialize circuit breakers for critical operations."""
        logger.info("Initializing circuit breakers...")
        self.circuit_breakers = {
            'mt5_connection': CircuitBreaker(failure_threshold=3, recovery_timeout=60, name='MT5Connection'),
            'data_collection': CircuitBreaker(failure_threshold=5, recovery_timeout=120, name='DataCollection'),
            'prediction': CircuitBreaker(failure_threshold=3, recovery_timeout=300, name='ModelPrediction'),
            'trade_execution': CircuitBreaker(failure_threshold=2, recovery_timeout=60, name='TradeExecution')
        }
        logger.info("Circuit breakers initialized.")

    # ... (Keep _check_circuit_breakers, _update_performance_metrics) ...
    # ... Update methods below to use self.model_manager ...

    # Example update in _run_trading_cycle:
    def _run_trading_cycle(self) -> bool:
        """Runs a single trading cycle: data -> preprocess -> predict -> decide -> execute."""
        logger.debug(f"[{self.terminal_id}] Starting trading cycle for {self.primary_timeframe}...")
        market_data: Optional[pd.DataFrame] = None # Initialize
        try:
            # 1. Data Collection (Assuming MT5Manager or a dedicated collector is used)
            # This part needs implementation based on how data is fetched.
            # For example, using the mt5_manager directly:
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                 logger.error(f"[{self.terminal_id}] MT5 connection lost. Cannot run cycle.")
                 # Trigger circuit breaker or recovery? Handled by MT5Manager likely.
                 return False

            # Fetch necessary data (e.g., latest N candles)
            # Example: Fetch last N candles needed for sequence length + features
            num_candles_needed = self.strategy_config.sequence_length + 50 # Add buffer for feature calculation

            # Use MT5 directly since connection is just a data class, not a method provider
            import MetaTrader5 as mt5
            timeframe_map = {
                'M1': mt5.TIMEFRAME_M1,
                'M5': mt5.TIMEFRAME_M5,
                'M15': mt5.TIMEFRAME_M15,
                'M30': mt5.TIMEFRAME_M30,
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1,
            }

            mt5_timeframe = timeframe_map.get(self.primary_timeframe, mt5.TIMEFRAME_M5)
            rates = mt5.copy_rates_from_pos(
                self.strategy_config.symbol,
                mt5_timeframe,
                0,  # From current position
                num_candles_needed
            )

            if rates is not None and len(rates) > 0:
                market_data = pd.DataFrame(rates)
                market_data['time'] = pd.to_datetime(market_data['time'], unit='s')
                logger.debug(f"[{self.terminal_id}] Fetched {len(market_data)} candles.")
            else:
                market_data = None
                logger.warning(f"[{self.terminal_id}] Failed to fetch market data.")

            # 2. Check Data Quality
            if market_data is None or market_data.empty or len(market_data) < self.strategy_config.sequence_length:
                 logger.warning(f"[{self.terminal_id}] Insufficient or no market data received ({len(market_data) if market_data is not None else 0} candles) for {self.primary_timeframe}. Skipping cycle.")
                 return False

            # 3. Preprocess Data
            # Ensure data_preprocessor has the necessary methods
            # Fetch feature columns - Assuming they are consistent across models used in signal generator
            try:
                # Get config from *a* model managed by the context-specific manager
                models = self.model_manager.get_all_models()
                if not models:
                    logger.error(f"[{self.terminal_id}] No models loaded by ModelManager. Cannot preprocess.")
                    return False

                # Get the first model name
                any_model_name = next(iter(models))
                model = self.model_manager.get_model(any_model_name)

                if not model or not hasattr(model, 'config'):
                    logger.error(f"[{self.terminal_id}] Model {any_model_name} has no config. Using default feature columns.")
                    # Use default feature columns if model config is not available
                    feature_cols = ['open', 'high', 'low', 'close', 'volume']
                else:
                    # Get feature columns from model config
                    model_config = model.config
                    if hasattr(model_config, 'FEATURE_COLUMNS'):
                        feature_cols = model_config.FEATURE_COLUMNS
                    else:
                        logger.warning(f"[{self.terminal_id}] Model config has no FEATURE_COLUMNS. Using default.")
                        feature_cols = ['open', 'high', 'low', 'close', 'volume']

                logger.debug(f"[{self.terminal_id}] Preprocessing data with feature columns: {feature_cols}")

                # Process the data using the data preprocessor
                processed_df, X = self.data_preprocessor.preprocess_data(
                    market_data,
                    feature_cols=feature_cols
                )

                if processed_df is None or X is None:
                    logger.error(f"[{self.terminal_id}] Data preprocessing failed. Skipping cycle.")
                    return False

            except Exception as e:
                logger.error(f"[{self.terminal_id}] Error during data preprocessing: {str(e)}")
                # Try fallback to simpler processing method
                try:
                    logger.warning(f"[{self.terminal_id}] Trying fallback data processing method.")
                    processed_data = self.data_preprocessor.process_data(market_data)
                    # Convert to numpy array for model input
                    X = processed_data.values.reshape(1, processed_data.shape[0], processed_data.shape[1])
                except Exception as fallback_error:
                    logger.error(f"[{self.terminal_id}] Fallback processing also failed: {str(fallback_error)}")
                    return False

            logger.debug(f"[{self.terminal_id}] Data preprocessed successfully. Shape: {X.shape if hasattr(X, 'shape') else 'unknown'}")

            # 4. Generate Signals
            # SignalGenerator internally uses ModelManager for predictions
            logger.debug(f"[{self.terminal_id}] Generating trading signal...")
            signal_decision = self.signal_generator.generate_signal(X)
            if signal_decision is None:
                 logger.warning(f"[{self.terminal_id}] Signal generator did not produce a decision. Skipping trade execution.")
                 return False # Or True depending on whether skipping is success
            logger.info(f"[{self.terminal_id}] Signal generated: {signal_decision}")

            # 5. Strategy Decision (Apply risk management, position sizing etc.)
            logger.debug(f"[{self.terminal_id}] Applying strategy rules...")
            trade_order = self.strategy.decide_trade(signal_decision)
            if not trade_order:
                 logger.info(f"[{self.terminal_id}] Strategy decided not to trade based on signal and rules.")
                 return True # Cycle completed, no trade placed
            logger.debug(f"[{self.terminal_id}] Strategy decided on trade: {trade_order}")

            # 6. Execute Trade
            logger.info(f"[{self.terminal_id}] Executing trade order: {trade_order}")
            trade_result = self.trade_executor.execute_trade(trade_order)
            if trade_result:
                 logger.info(f"[{self.terminal_id}] Trade execution result: {trade_result}")
                 # Update performance metrics based on execution result
                 self._update_performance_metrics(trade_result)
            else:
                 logger.error(f"[{self.terminal_id}] Trade execution failed.")
                 # Update performance metrics with failure? Or handle error?

            # 7. Post-Cycle Cleanup (Optional)
            gc.collect()
            logger.debug(f"[{self.terminal_id}] Trading cycle completed.")
            return True

        except CircuitOpenError as coe:
             logger.warning(f"[{self.terminal_id}] Circuit breaker '{coe.breaker_name}' is open. Skipping trading cycle. Reason: {coe}")
             return False # Cycle skipped due to open circuit breaker
        except Exception as e:
             logger.error(f"[{self.terminal_id}] Unhandled error in trading cycle: {e}", exc_info=True)
             self.error_handler.handle_error(
                 exception=e,
                 context={"method": "_run_trading_cycle", "terminal_id": self.terminal_id},
                 source="TradingBot._run_trading_cycle",
                 severity=ErrorSeverity.ERROR,
                 category=ErrorCategory.EXECUTION
             )
             # Increment error counts or trigger recovery?
             return False # Cycle failed

    # ... (Keep run method) ...

    def stop(self):
        """Stop the trading bot with proper cleanup"""
        logger.info(f"[{self.terminal_id}] Stopping trading bot...")

        # Set stop event
        self.stop_event.set()

        # Wait for thread to finish
        if self.running_thread and self.running_thread.is_alive():
            logger.debug(f"[{self.terminal_id}] Waiting for trading loop thread to join...")
            self.running_thread.join(timeout=30)
            if self.running_thread.is_alive():
                 logger.warning(f"[{self.terminal_id}] Trading loop thread did not exit cleanly after 30s.")

        # Clean up resources
        logger.debug(f"[{self.terminal_id}] Cleaning up bot resources...")
        try:
            # Shutdown MT5 connection for this bot's perspective (Manager handles actual disconnect)
            # self.mt5_manager.shutdown(self.terminal_id) # Optional: signal manager this bot is done

            # Clear model caches (Iterate through models managed by THIS instance)
            logger.debug(f"[{self.terminal_id}] Clearing model caches...")
            # for model in self.model_manager.models.values(): # Accessing dict directly might be unsafe
            for model in self.model_manager.get_all_models().values(): # Use getter
                if hasattr(model, 'clear_cache'):
                    try:
                         model.clear_cache()
                         logger.debug(f"[{self.terminal_id}] Cleared cache for {model.model_name}")
                    except Exception as cache_err:
                         logger.error(f"[{self.terminal_id}] Error clearing cache for {model.model_name}: {cache_err}")

            # Force garbage collection
            gc.collect()

            logger.info(f"[{self.terminal_id}] Trading bot stopped and resources cleaned up.")

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error during cleanup: {e}", exc_info=True)
            self.error_handler.handle_error(e, context={"method": "stop", "terminal_id": self.terminal_id})

    def _check_system_requirements(self) -> bool:
        """
        Check if system requirements are met

        Returns:
            True if requirements are met, False otherwise
        """
        try:
            # Check memory using memory manager if available
            if self.memory_manager and hasattr(self.memory_manager, 'is_memory_critical'):
                if self.memory_manager.is_memory_critical():
                    logger.warning(f"[{self.terminal_id}] Memory usage is critical according to memory manager")
                    return False
            else:
                # Fallback to direct check if memory manager not available
                process = psutil.Process(os.getpid())
                mem_info = process.memory_info()
                memory_usage_mb = mem_info.rss / (1024 * 1024)

                # Get system memory and calculate percentage
                system_memory = psutil.virtual_memory()
                memory_usage_percent = (mem_info.rss / system_memory.total) * 100
                max_memory_percent = self.config.max_memory_usage # From TradingConfig (percentage)

                if memory_usage_percent > max_memory_percent:
                    logger.warning(f"[{self.terminal_id}] Memory usage ({memory_usage_mb:.2f} MB, {memory_usage_percent:.1f}%) exceeds threshold ({max_memory_percent}%)")
                    return False

            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.5) # Shorter interval might be noisy
            max_cpu = 90.0 # Make this configurable?
            if cpu_percent > max_cpu:
                logger.warning(f"[{self.terminal_id}] CPU usage ({cpu_percent}%) exceeds threshold ({max_cpu}%)")
                return False

            # Check disk space (Check on path where data/models are stored)
            # Use data_base_path as a representative location
            config = self.config_manager.get_config()
            check_path = Path(config.data_base_path).anchor or '/' # Get drive or root
            disk = psutil.disk_usage(check_path)
            min_disk_mb = 1024 # Make this configurable?
            if disk.free / (1024 * 1024) < min_disk_mb:
                logger.warning(f"[{self.terminal_id}] Disk space on {check_path} ({disk.free / (1024 * 1024):.2f} MB) below threshold ({min_disk_mb} MB)")
                return False

            # Check MT5 connection for this bot's terminal_id
            # terminal_id = self.config.get('mt5', {}).get('terminal_id', 'default') # Old way
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                # Check if we're in minimal MT5 mode
                config = self.config_manager.get_config()
                minimal_mode = getattr(config, 'minimal_mode', False) if hasattr(config, 'minimal_mode') else False
                if minimal_mode and self.terminal_id != '1':
                    logger.info(f"[{self.terminal_id}] MT5 connection not active (expected in minimal mode - only Terminal 1 auto-connects)")
                else:
                    logger.warning(f"[{self.terminal_id}] MT5 connection not active")
                # Let MT5Manager handle reconnection attempts
                return False

            return True

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error checking system requirements: {e}", exc_info=True)
            self.error_handler.handle_error(
                exception=e,
                context={"method": "_check_system_requirements", "terminal_id": self.terminal_id},
                source="TradingBot._check_system_requirements",
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.SYSTEM
            )
            return False

    # Removed _handle_trading_cycle_error method as we're now using EnhancedErrorHandler's with_error_handling

    # Keep _check_circuit_breakers, _update_performance_metrics as they seem self-contained for now
    # Although _update_performance_metrics might need adjustment based on trade_result structure



    def _check_circuit_breakers(self) -> bool:
        """Check circuit breakers for critical operations."""
        for breaker_name, breaker in self.circuit_breakers.items():
            if breaker.is_open:
                logger.warning(f"[{self.terminal_id}] Circuit breaker '{breaker_name}' is open. Skipping operation.")
                return False
        return True

    def _update_performance_metrics(self, trade_result):
        """Update performance metrics based on trade result."""
        self.metrics['total_trades'] += 1

        # Handle both dictionary and object types
        if isinstance(trade_result, dict):
            profit = trade_result.get('profit', 0.0)  # Get profit from dict
        else:
            # Handle object type (like TradingSignal or TradeOrder)
            # Initialize profit to 0.0 as a safe default
            profit = 0.0

            # Try to get profit from various attributes
            try:
                if hasattr(trade_result, 'profit'):
                    profit = trade_result.profit
                elif hasattr(trade_result, 'profit_loss'):
                    profit = trade_result.profit_loss

                # Ensure profit and profit_loss attributes exist
                if not hasattr(trade_result, 'profit'):
                    trade_result.profit = profit
                if not hasattr(trade_result, 'profit_loss'):
                    trade_result.profit_loss = profit
            except Exception as e:
                logger.warning(f"Could not access or set profit attributes on trade_result: {e}")

        if profit > 0:
            self.metrics['winning_trades'] += 1
        else:
            self.metrics['losing_trades'] += 1

        self.metrics['total_profit'] += profit  # Use actual profit
        self.metrics['last_update'] = datetime.now()

        # Get processing stats if available
        if hasattr(self.data_preprocessor, 'get_stats'):
            self.metrics['processing_stats'] = self.data_preprocessor.get_stats()

        # Calculate win rate safely
        if self.metrics['total_trades'] > 0:
            self.metrics['win_rate'] = (self.metrics['winning_trades'] / self.metrics['total_trades']) * 100
        else:
            self.metrics['win_rate'] = 0.0

        # Calculate max drawdown if method exists
        self.metrics['max_drawdown'] = self._calculate_max_drawdown() if hasattr(self, '_calculate_max_drawdown') else 0.0

        logger.info(f"[{self.terminal_id}] Performance Updated: WinRate={self.metrics['win_rate']:.2f}%, Profit={self.metrics['total_profit']:.2f}")

    def _calculate_max_drawdown(self) -> float:
        """Calculate the maximum drawdown based on trade results."""
        # Ensure trade_executor and trade_history exist and are populated correctly
        if not hasattr(self.trade_executor, 'trade_history') or not self.trade_executor.trade_history:
            return 0.0

        # Starting equity is 0 or could be fetched from account balance
        current_equity = 0.0
        peak_equity = 0.0
        max_drawdown = 0.0

        for trade in self.trade_executor.trade_history:
            # Handle both dictionary and object types
            if isinstance(trade, dict):
                profit = trade.get('profit', 0.0)
            else:
                # Handle object type (like TradingSignal or TradeOrder)
                # Initialize profit to 0.0 as a safe default
                profit = 0.0

                # Try to get profit from various attributes
                try:
                    if hasattr(trade, 'profit'):
                        profit = trade.profit
                    elif hasattr(trade, 'profit_loss'):
                        profit = trade.profit_loss

                    # Ensure profit and profit_loss attributes exist
                    if not hasattr(trade, 'profit'):
                        trade.profit = profit
                    if not hasattr(trade, 'profit_loss'):
                        trade.profit_loss = profit
                except Exception as e:
                    logger.warning(f"Could not access or set profit attributes on trade: {e}")

            if profit is None:
                continue

            current_equity += profit
            peak_equity = max(peak_equity, current_equity)
            drawdown = peak_equity - current_equity  # Absolute drawdown
            # drawdown_percent = (drawdown / peak_equity) * 100 if peak_equity > 0 else 0
            max_drawdown = max(max_drawdown, drawdown)

        return max_drawdown # Return absolute drawdown for consistency with metrics example

    # ... (Keep run, stop, _check_system_requirements, _handle_trading_cycle_error) ...
    # ... Ensure terminal_id is used correctly in logging and context ...

    def run(self, interval_seconds: int = 3600):
        """
        Run the trading bot in a loop with improved error handling

        Args:
            interval_seconds: Interval between trading cycles in seconds
        """
        # Initialize error count if not already present
        if not hasattr(self, 'error_count'):
            self.error_count = 0

        def _trading_loop():
            while not self.stop_event.is_set():
                try:
                    # Check circuit breakers before proceeding
                    if not self._check_circuit_breakers():
                        logger.warning(f"[{self.terminal_id}] Circuit breaker triggered. Waiting for reset...")
                        time.sleep(interval_seconds)
                        continue

                    # Check system requirements
                    if not self._check_system_requirements():
                        logger.error(f"[{self.terminal_id}] System requirements not met. Waiting...")
                        time.sleep(interval_seconds)
                        continue

                    # Run a single trading cycle with error handling
                    # Use with_error_handling decorator from EnhancedErrorHandler
                    success = self.error_handler.with_error_handling(
                        self._run_trading_cycle,
                        context={"operation": "trading_cycle", "terminal_id": self.terminal_id},
                        source="TradingBot.run",
                        severity=ErrorSeverity.ERROR,
                        category=ErrorCategory.EXECUTION,
                        circuit_breaker_name="trading_cycle"
                    )

                    if not success:
                        self.error_count += 1
                        logger.error(f"[{self.terminal_id}] Trading cycle failed. Error count: {self.error_count}")
                    else:
                        # Reset error count if successful
                        self.error_count = 0

                    # Wait for the next interval
                    logger.info(f"[{self.terminal_id}] Waiting {interval_seconds} seconds until next trading cycle")
                    for _ in range(interval_seconds):
                        if self.stop_event.is_set():
                            break
                        time.sleep(1)

                except Exception as e:
                    logger.error(f"[{self.terminal_id}] Error in trading loop: {str(e)}")
                    self.error_count += 1
                    time.sleep(interval_seconds)

        # Start the trading loop in a separate thread
        self.running_thread = threading.Thread(target=_trading_loop, daemon=True)
        self.running_thread.start()
        logger.info(f"[{self.terminal_id}] Trading bot started")

    def update(self):
        """
        Update method for the trading bot.
        This method is called periodically by the main loop to perform maintenance tasks.
        """
        try:
            # Update performance metrics
            self.metrics['last_update'] = datetime.now()

            # Check system health
            if not self._check_system_requirements():
                logger.warning(f"[{self.terminal_id}] System requirements check failed during update")

            # Check circuit breaker status
            if not self._check_circuit_breakers():
                logger.warning(f"[{self.terminal_id}] Circuit breakers are open during update")

            # Update model health status if available
            if hasattr(self.model_manager, 'get_model_health_status'):
                model_health = self.model_manager.get_model_health_status()
                logger.debug(f"[{self.terminal_id}] Model health status: {model_health}")

            # Log current status
            logger.debug(f"[{self.terminal_id}] Bot update completed. Total trades: {self.metrics['total_trades']}, "
                        f"Win rate: {self.metrics['win_rate']:.1f}%")

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error during bot update: {str(e)}")
            self.error_handler.handle_error(
                e,
                context={"method": "update", "terminal_id": self.terminal_id},
                severity="ERROR"
            )