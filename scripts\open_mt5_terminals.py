"""
Sc<PERSON><PERSON> to open all MT5 terminals for manual Algo Trading enablement.
"""

import logging
import time
import sys
import os
import subprocess
from pathlib import Path

# Import terminal ID normalization
try:
    from utils.common import normalize_terminal_id
except ImportError:
    # Fallback implementation if utils.common is not available
    def normalize_terminal_id(terminal_id) -> str:
        """Ensure terminal ID is consistently a string."""
        if terminal_id is None:
            return ""
        return str(terminal_id)

# Add project root to path
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
sys.path.append(PROJECT_ROOT)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('open_mt5_terminals')

# Import required modules
try:
    from config import config_manager
except ImportError:
    try:
        from config import ConfigurationManager
        config_manager = ConfigurationManager()
    except ImportError:
        logger.error("Failed to import configuration manager")
        sys.exit(1)

def open_terminal(terminal_path):
    """Open an MT5 terminal."""
    try:
        # Check if path exists
        path = Path(terminal_path)
        if not path.exists():
            logger.error(f"Terminal path not found: {terminal_path}")
            return False

        # Check if path is a directory or a file
        if path.is_dir():
            # Look for terminal64.exe in the directory
            terminal_exe = path / "terminal64.exe"
            if not terminal_exe.exists():
                logger.error(f"terminal64.exe not found in {path}")
                return False
            terminal_path = str(terminal_exe)

        # Open the terminal
        logger.info(f"Opening terminal: {terminal_path}")
        subprocess.Popen(terminal_path)
        return True

    except Exception as e:
        logger.error(f"Error opening terminal: {str(e)}")
        return False

def main():
    """Main function."""
    try:
        # Get MT5 configuration
        mt5_config = config_manager.get_mt5_config()

        # Print terminal configurations
        logger.info(f"MT5 Terminals: {len(mt5_config.terminals)}")

        # Print instructions for enabling Algo Trading
        logger.info("=" * 80)
        logger.info("INSTRUCTIONS FOR ENABLING ALGO TRADING IN MT5")
        logger.info("=" * 80)
        logger.info("")
        logger.info("For each terminal that opens:")
        logger.info("1. Wait for the terminal to fully load")
        logger.info("2. Look for the 'Algo Trading' button in the toolbar:")
        logger.info("   - It's usually located in the top toolbar")
        logger.info("   - It may be labeled as 'AutoTrading' or have a robot icon")
        logger.info("3. Click the 'Algo Trading' button to enable it:")
        logger.info("   - When enabled, the button should be highlighted (usually in green)")
        logger.info("   - When disabled, the button is not highlighted (usually gray)")
        logger.info("4. Verify that 'AutoTrading enabled' appears in the status bar")
        logger.info("")
        logger.info("IMPORTANT: You must manually enable Algo Trading in each terminal")
        logger.info("=" * 80)

        # Open each terminal
        for terminal_id, terminal_config in mt5_config.terminals.items():
            # Normalize terminal ID
            terminal_id_str = normalize_terminal_id(terminal_id)
            logger.info(f"Processing terminal {terminal_id_str}")

            # Get terminal path
            if isinstance(terminal_config, dict):
                terminal_path = terminal_config["path"]
            else:
                terminal_path = terminal_config.path

            # Open the terminal
            if open_terminal(terminal_path):
                logger.info(f"Terminal {terminal_id_str} opened successfully")
            else:
                logger.error(f"Failed to open terminal {terminal_id_str}")

            # Wait a bit between terminals
            time.sleep(5)

        logger.info("All terminals have been opened")
        logger.info("Please enable Algo Trading in each terminal manually")
        logger.info("After enabling Algo Trading in all terminals, run:")
        logger.info("python scripts/check_algo_trading.py")

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == '__main__':
    main()
