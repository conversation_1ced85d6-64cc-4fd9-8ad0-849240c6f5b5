"""
Model validation utility to verify model files, compatibility, and weights.
"""

import os
import sys
import torch
import pickle
import logging
import json
from pathlib import Path
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import ConfigurationManager

logger = logging.getLogger(__name__)

class ModelValidator:
    def __init__(self, terminal_id: str = "1", timeframe: str = "M5"):
        self.config = ConfigurationManager()
        self.terminal_id = terminal_id
        self.timeframe = timeframe
        config = self.config.get_config()
        self.model_paths = {}

        # Get all available model configurations and construct full paths
        for model_name in config.models.keys():
            try:
                # Use the standardized path method to get the actual model path
                model_dir = self.config.get_model_path(model_name, terminal_id, timeframe)

                # Determine the correct file extension based on model type
                if model_name in ['lstm', 'gru', 'transformer', 'tft']:
                    extension = '.pt'
                elif model_name in ['xgboost']:
                    extension = '.json'
                elif model_name in ['lightgbm']:
                    extension = '.txt'
                elif model_name in ['arima', 'lstm_arima', 'tft_arima']:
                    extension = '.pkl'
                else:
                    extension = '.pkl'  # Default fallback

                # Construct the full model file path
                model_file_path = model_dir / f"{model_name}_model{extension}"
                self.model_paths[model_name] = str(model_file_path)

                logger.debug(f"Model {model_name} path: {model_file_path}")

            except Exception as e:
                logger.warning(f"Could not determine path for model {model_name}: {str(e)}")
                # Fallback to config model_path if available
                try:
                    self.model_paths[model_name] = config.models[model_name].model_path
                except AttributeError:
                    logger.warning(f"Model {model_name} configuration missing model_path")

    def validate_model_files(self) -> Dict[str, bool]:
        """Verify that all model files exist in specified paths."""
        results = {}
        for model_name, path in self.model_paths.items():
            try:
                # Ensure path is a string and handle Path objects
                path_str = str(path) if path else ""
                exists = os.path.exists(path_str) and os.path.isfile(path_str)
                results[model_name] = exists
                if not exists:
                    logger.error(f"Model file not found: {model_name} at {path_str}")
                else:
                    logger.debug(f"Model file exists: {model_name} at {path_str}")
            except Exception as e:
                logger.error(f"Error checking model file {model_name}: {str(e)}")
                results[model_name] = False
        return results

    def validate_model_compatibility(self) -> Dict[str, Dict[str, Any]]:
        """Check model compatibility with current system."""
        results = {}

        for model_name, path in self.model_paths.items():
            if not os.path.exists(path):
                results[model_name] = {
                    'compatible': False,
                    'error': f'Model file not found: {path}'
                }
                continue

            try:
                # Determine model type and validate accordingly
                if model_name in ['lstm', 'gru', 'transformer', 'tft'] and (path.endswith('.pt') or path.endswith('.pth')):
                    # PyTorch models
                    model = torch.load(path, map_location='cpu')
                    results[model_name] = {
                        'compatible': True,
                        'type': 'pytorch',
                        'device_compatible': True
                    }
                elif model_name in ['transformer', 'tft'] and (path.endswith('.h5') or path.endswith('.keras')):
                    # Legacy TensorFlow models - no longer supported
                    results[model_name] = {
                        'compatible': False,
                        'error': 'TensorFlow models no longer supported. Please retrain with PyTorch.'
                    }
                elif model_name == 'xgboost' and path.endswith('.json'):
                    # XGBoost models in native format
                    import xgboost as xgb
                    model = xgb.Booster()
                    model.load_model(path)
                    results[model_name] = {
                        'compatible': True,
                        'type': 'xgboost',
                        'n_features': model.num_features()
                    }
                elif model_name == 'lightgbm' and path.endswith('.txt'):
                    # LightGBM models in native format
                    import lightgbm as lgb
                    model = lgb.Booster(model_file=path)
                    results[model_name] = {
                        'compatible': True,
                        'type': 'lightgbm',
                        'n_features': model.num_feature()
                    }
                elif model_name in ['xgboost', 'lightgbm'] and path.endswith('.pkl'):
                    # Legacy pickle format for XGBoost/LightGBM models
                    with open(path, 'rb') as f:
                        model = pickle.load(f)
                    results[model_name] = {
                        'compatible': True,
                        'type': 'sklearn_legacy',
                        'n_features': getattr(model, 'n_features_in_', 'unknown')
                    }
                elif model_name == 'arima':
                    # ARIMA models (usually pickle files)
                    with open(path, 'rb') as f:
                        model = pickle.load(f)
                    results[model_name] = {
                        'compatible': True,
                        'type': 'arima'
                    }
                else:
                    results[model_name] = {
                        'compatible': False,
                        'error': f'Unknown model type or file format for {model_name}: {path}'
                    }

            except Exception as e:
                logger.error(f"Error loading {model_name} model: {str(e)}")
                results[model_name] = {
                    'compatible': False,
                    'error': str(e)
                }

        return results

    def validate_model_weights(self) -> Dict[str, Dict[str, Any]]:
        """Validate model weights and performance."""
        results = {}

        for model_name, path in self.model_paths.items():
            if not os.path.exists(path):
                results[model_name] = {
                    'weights_valid': False,
                    'error': f'Model file not found: {path}'
                }
                continue

            try:
                # Check PyTorch model weights
                if model_name in ['lstm', 'gru', 'transformer', 'tft'] and (path.endswith('.pt') or path.endswith('.pth')):
                    model = torch.load(path, map_location='cpu')
                    if hasattr(model, 'named_parameters'):
                        weights = {name: param.data for name, param in model.named_parameters()}

                        # Check for NaN or Inf values
                        has_nan = any(torch.isnan(w).any() for w in weights.values())
                        has_inf = any(torch.isinf(w).any() for w in weights.values())

                        results[model_name] = {
                            'weights_valid': not (has_nan or has_inf),
                            'has_nan': has_nan,
                            'has_inf': has_inf,
                            'num_parameters': sum(w.numel() for w in weights.values())
                        }
                    else:
                        results[model_name] = {
                            'weights_valid': True,
                            'note': 'Model loaded but no parameters found (might be state dict)'
                        }



                # For other model types, just check if they load
                else:
                    results[model_name] = {
                        'weights_valid': True,
                        'note': f'Basic validation passed for {model_name}'
                    }

            except Exception as e:
                logger.error(f"Error validating {model_name} weights: {str(e)}")
                results[model_name] = {
                    'weights_valid': False,
                    'error': str(e)
                }

        return results

    def generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report."""
        return {
            'files_exist': self.validate_model_files(),
            'compatibility': self.validate_model_compatibility(),
            'weights': self.validate_model_weights()
        }

def main():
    """Run model validation and generate report."""
    # Test with the terminal and timeframe that has trained models
    validator = ModelValidator(terminal_id="1", timeframe="M5")
    report = validator.generate_validation_report()

    # Log validation results
    logger.info("Model Validation Report:")
    logger.info(json.dumps(report, indent=2))

    return report

if __name__ == "__main__":
    main()