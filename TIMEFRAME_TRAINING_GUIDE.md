# Complete Guide: Training Models on M5, M15, M30, H1, and H4 Timeframes

## 🎯 **ANSWER TO YOUR QUESTION**

The system **ALREADY SUPPORTS** training all models on all 5 timeframes. Here's the complete process:

## 📋 **STEP-BY-STEP TRAINING PROCESS**

### **1. Data Preparation (One-Time Setup)**

First, ensure you have data for all timeframes:

```bash
# Prepare training data for all timeframes
python prepare_training_data.py --symbol BTCUSD.a

# Or prepare specific timeframes
python prepare_training_data.py --symbol BTCUSD.a --timeframe M5
python prepare_training_data.py --symbol BTCUSD.a --timeframe M15
python prepare_training_data.py --symbol BTCUSD.a --timeframe M30
python prepare_training_data.py --symbol BTCUSD.a --timeframe H1
python prepare_training_data.py --symbol BTCUSD.a --timeframe H4
```

### **2. Training Individual Models on Specific Timeframes**

#### **🔥 CORE TRAINING COMMANDS:**

```bash
# Train LSTM on M5 timeframe
python train_models.py --models lstm --timeframe M5 --symbol BTCUSD.a

# Train LSTM on M15 timeframe
python train_models.py --models lstm --timeframe M15 --symbol BTCUSD.a

# Train LSTM on M30 timeframe
python train_models.py --models lstm --timeframe M30 --symbol BTCUSD.a

# Train LSTM on H1 timeframe
python train_models.py --models lstm --timeframe H1 --symbol BTCUSD.a

# Train LSTM on H4 timeframe
python train_models.py --models lstm --timeframe H4 --symbol BTCUSD.a
```

#### **🚀 ALL MODELS ON ALL TIMEFRAMES:**

```bash
# Train ALL models on M5
python train_models.py --timeframe M5 --symbol BTCUSD.a

# Train ALL models on M15
python train_models.py --timeframe M15 --symbol BTCUSD.a

# Train ALL models on M30
python train_models.py --timeframe M30 --symbol BTCUSD.a

# Train ALL models on H1
python train_models.py --timeframe H1 --symbol BTCUSD.a

# Train ALL models on H4
python train_models.py --timeframe H4 --symbol BTCUSD.a
```

### **3. Training Specific Model Types**

#### **Neural Network Models:**
```bash
# LSTM Models
python train_models.py --models lstm --timeframe M5 --epochs 150 --batch-size 64
python train_models.py --models lstm --timeframe H1 --epochs 200 --batch-size 32

# GRU Models
python train_models.py --models gru --timeframe M15 --epochs 150 --learning-rate 0.001

# Transformer Models
python train_models.py --models transformer --timeframe M30 --epochs 100 --patience 15

# TFT (Temporal Fusion Transformer)
python train_models.py --models tft --timeframe H4 --epochs 100 --max-samples-per-dataset 10000
```

#### **Tree-Based Models:**
```bash
# XGBoost Models
python train_models.py --models xgboost --timeframe M5 --epochs 300 --learning-rate 0.01
python train_models.py --models xgboost --timeframe H1 --epochs 500

# LightGBM Models
python train_models.py --models lightgbm --timeframe M15 --epochs 250 --learning-rate 0.05
python train_models.py --models lightgbm --timeframe H4 --epochs 400
```

#### **Time Series Models:**
```bash
# ARIMA Models
python train_models.py --models arima --timeframe M5 --symbol BTCUSD.a
python train_models.py --models arima --timeframe H1 --symbol BTCUSD.a
```

#### **Ensemble Models:**
```bash
# LSTM-ARIMA Ensemble
python train_models.py --models lstm_arima --timeframe M30 --epochs 150

# TFT-ARIMA Ensemble
python train_models.py --models tft_arima --timeframe H4 --epochs 100
```

### **4. Batch Training for All Timeframes**

#### **Train One Model on All Timeframes:**
```bash
# Train LSTM on all timeframes
for timeframe in M5 M15 M30 H1 H4; do
    python train_models.py --models lstm --timeframe $timeframe --symbol BTCUSD.a --epochs 150
done

# Train XGBoost on all timeframes
for timeframe in M5 M15 M30 H1 H4; do
    python train_models.py --models xgboost --timeframe $timeframe --symbol BTCUSD.a --epochs 300
done
```

#### **Train All Models on All Timeframes (Complete Training):**
```bash
# Complete training session - all models, all timeframes
for timeframe in M5 M15 M30 H1 H4; do
    echo "Training all models on $timeframe timeframe..."
    python train_models.py --timeframe $timeframe --symbol BTCUSD.a --max-workers 2
done
```

## 🗂️ **MODEL STORAGE STRUCTURE**

Models are automatically saved in timeframe-specific directories:

```
models/
├── terminal_1/
│   ├── M5/
│   │   ├── lstm_model.pt
│   │   ├── gru_model.pt
│   │   ├── xgboost_model.json
│   │   ├── lightgbm_model.txt
│   │   ├── tft_model.pt
│   │   ├── transformer_model.pt
│   │   └── arima_model.pkl
│   ├── M15/
│   │   ├── lstm_model.pt
│   │   ├── gru_model.pt
│   │   └── ...
│   ├── M30/
│   ├── H1/
│   └── H4/
└── saved_models/
    ├── lstm/
    │   ├── BTCUSD.a_M5_lstm.pt
    │   ├── BTCUSD.a_M15_lstm.pt
    │   ├── BTCUSD.a_M30_lstm.pt
    │   ├── BTCUSD.a_H1_lstm.pt
    │   └── BTCUSD.a_H4_lstm.pt
    ├── xgboost/
    ├── lightgbm/
    ├── tft/
    ├── arima/
    └── ensemble/
```

## 📊 **DATA STRUCTURE**

Training data is organized by timeframe:

```
data/
├── processed/
│   ├── train/
│   │   ├── BTCUSD.a_M5.parquet
│   │   ├── BTCUSD.a_M15.parquet
│   │   ├── BTCUSD.a_M30.parquet
│   │   ├── BTCUSD.a_H1.parquet
│   │   └── BTCUSD.a_H4.parquet
│   ├── validation/
│   │   ├── BTCUSD.a_M5.parquet
│   │   ├── BTCUSD.a_M15.parquet
│   │   ├── BTCUSD.a_M30.parquet
│   │   ├── BTCUSD.a_H1.parquet
│   │   └── BTCUSD.a_H4.parquet
│   └── test/
│       ├── BTCUSD.a_M5.parquet
│       ├── BTCUSD.a_M15.parquet
│       ├── BTCUSD.a_M30.parquet
│       ├── BTCUSD.a_H1.parquet
│       └── BTCUSD.a_H4.parquet
└── metadata/
    ├── BTCUSD.a_M5_metadata.json
    ├── BTCUSD.a_M15_metadata.json
    ├── BTCUSD.a_M30_metadata.json
    ├── BTCUSD.a_H1_metadata.json
    └── BTCUSD.a_H4_metadata.json
```

## ⚙️ **ADVANCED TRAINING OPTIONS**

### **Custom Parameters for Different Timeframes:**

```bash
# M5 - High frequency, shorter sequences
python train_models.py --models lstm --timeframe M5 --sequence-length 60 --batch-size 128 --epochs 200

# M15 - Medium frequency
python train_models.py --models lstm --timeframe M15 --sequence-length 100 --batch-size 64 --epochs 150

# M30 - Lower frequency
python train_models.py --models lstm --timeframe M30 --sequence-length 150 --batch-size 32 --epochs 120

# H1 - Hourly data, longer sequences
python train_models.py --models lstm --timeframe H1 --sequence-length 200 --batch-size 16 --epochs 100

# H4 - 4-hour data, very long sequences
python train_models.py --models lstm --timeframe H4 --sequence-length 300 --batch-size 8 --epochs 80
```

### **Parallel Training:**

```bash
# Train multiple models concurrently
python train_models.py --models lstm gru xgboost --timeframe M5 --max-workers 3

# Train with memory optimization for TFT
python train_models.py --models tft --timeframe H1 --enable-memory-optimization true --max-samples-per-dataset 5000
```

## 🎯 **SUMMARY**

**The system ALREADY WORKS for all 5 timeframes!** You can:

1. ✅ Train any model on any timeframe using `--timeframe` parameter
2. ✅ Models are automatically saved in timeframe-specific directories
3. ✅ Data is properly organized by timeframe
4. ✅ All 9 model types support all 5 timeframes
5. ✅ Custom parameters can be set per timeframe

**Start training immediately with:**
```bash
python train_models.py --timeframe H1 --symbol BTCUSD.a
```
