"""
Circuit breaker implementation for preventing cascading failures.

This module provides a circuit breaker pattern implementation to prevent
repeated failures in critical operations. It keeps track of failures and
temporarily disables operations when a threshold is reached.
"""

import time
import logging
import threading
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, Optional, TypeVar, Generic

T = TypeVar('T')  # Return type for circuit protected functions

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """States for the circuit breaker."""
    CLOSED = "CLOSED"     # Normal operation - requests pass through
    OPEN = "OPEN"         # Failed state - requests are blocked
    HALF_OPEN = "HALF_OPEN"  # Testing state - allows limited requests to test recovery


class CircuitOpenError(Exception):
    """Exception raised when a circuit is open and a request is attempted."""
    
    def __init__(self, circuit_name: str, message: Optional[str] = None):
        self.circuit_name = circuit_name
        self.message = message or f"Circuit '{circuit_name}' is open"
        super().__init__(self.message)


@dataclass
class CircuitStats:
    """Statistics for a circuit breaker."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    consecutive_failures: int = 0
    total_open_time: float = 0.0  # Total seconds the circuit has been open
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    last_state_change_time: datetime = field(default_factory=datetime.now)
    open_count: int = 0  # Number of times the circuit has opened


class CircuitBreaker:
    """
    Implementation of the Circuit Breaker pattern.
    
    The circuit breaker monitors operations for failures and prevents operation
    execution when failure thresholds are exceeded. This helps prevent cascading
    failures and allows the system to recover.
    """
    
    # Class-level registry of all circuit breakers
    _registry: Dict[str, 'CircuitBreaker'] = {}
    _registry_lock = threading.RLock()
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        half_open_max_calls: int = 1,
        name: str = "default"
    ):
        """
        Initialize a circuit breaker.
        
        Args:
            failure_threshold: Number of consecutive failures before opening the circuit
            recovery_timeout: Seconds to wait before attempting recovery (half-open state)
            half_open_max_calls: Maximum number of calls allowed in half-open state
            name: Unique name for this circuit breaker
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_max_calls = half_open_max_calls
        
        self._state = CircuitState.CLOSED
        self._state_lock = threading.RLock()
        self._stats = CircuitStats()
        self._half_open_calls = 0
        
        # Register this circuit breaker
        with CircuitBreaker._registry_lock:
            CircuitBreaker._registry[name] = self
        
        logger.info(f"Circuit breaker '{name}' initialized with failure threshold {failure_threshold}")
    
    @property
    def state(self) -> CircuitState:
        """Get the current state of the circuit breaker."""
        with self._state_lock:
            # If in OPEN state, check if recovery timeout has elapsed
            if self._state == CircuitState.OPEN:
                now = datetime.now()
                if self._stats.last_failure_time and (
                    now - self._stats.last_failure_time > timedelta(seconds=self.recovery_timeout)
                ):
                    self._transition_to_half_open()
            return self._state

    @property
    def is_open(self) -> bool:
        """Check if the circuit breaker is in the OPEN state."""
        return self.state == CircuitState.OPEN
    
    def execute(self, func: Callable[..., T], *args: Any, circuit_name: Optional[str] = None, **kwargs: Any) -> T:
        """
        Execute a function with circuit breaker protection.
        
        Args:
            func: The function to execute
            *args: Arguments to pass to the function
            circuit_name: Optional specific name for this execution context
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            The result of the function execution
            
        Raises:
            CircuitOpenError: If the circuit is open
            Exception: Any exception raised by the function
        """
        context_name = circuit_name or self.name
        state = self.state  # This may transition from OPEN to HALF_OPEN if timeout elapsed
        
        with self._state_lock:
            self._stats.total_requests += 1
            
            # Check if circuit is open
            if state == CircuitState.OPEN:
                self._stats.total_open_time += (datetime.now() - self._stats.last_state_change_time).total_seconds()
                logger.warning(f"Circuit '{context_name}' is OPEN, rejecting request")
                raise CircuitOpenError(context_name)
            
            # Check if we've reached the half-open call limit
            if state == CircuitState.HALF_OPEN and self._half_open_calls >= self.half_open_max_calls:
                logger.warning(f"Circuit '{context_name}' is HALF_OPEN but call limit reached, rejecting request")
                raise CircuitOpenError(context_name, "Call limit reached in half-open state")
            
            # Increment half-open call counter if applicable
            if state == CircuitState.HALF_OPEN:
                self._half_open_calls += 1
                logger.info(f"Circuit '{context_name}' is HALF_OPEN, allowing test request ({self._half_open_calls}/{self.half_open_max_calls})")
        
        # Execute the function
        try:
            result = func(*args, **kwargs)
            self._handle_success()
            return result
        except Exception as e:
            self._handle_failure(e, context_name)
            raise
    
    def _handle_success(self) -> None:
        """Handle a successful execution."""
        with self._state_lock:
            self._stats.successful_requests += 1
            self._stats.consecutive_failures = 0
            self._stats.last_success_time = datetime.now()
            
            # If in half-open state, transition to closed
            if self._state == CircuitState.HALF_OPEN:
                logger.info(f"Circuit '{self.name}' recovery successful, closing circuit")
                self._transition_to_closed()
    
    def _handle_failure(self, exception: Exception, context_name: str) -> None:
        """
        Handle a failed execution.
        
        Args:
            exception: The exception that occurred
            context_name: Name of the execution context
        """
        with self._state_lock:
            self._stats.failed_requests += 1
            self._stats.consecutive_failures += 1
            self._stats.last_failure_time = datetime.now()
            
            # If in half-open state, transition back to open
            if self._state == CircuitState.HALF_OPEN:
                logger.warning(f"Circuit '{context_name}' recovery failed, reopening circuit")
                self._transition_to_open()
            
            # If in closed state and threshold reached, transition to open
            elif self._state == CircuitState.CLOSED and self._stats.consecutive_failures >= self.failure_threshold:
                logger.warning(
                    f"Circuit '{context_name}' failure threshold reached "
                    f"({self._stats.consecutive_failures}/{self.failure_threshold}), opening circuit"
                )
                self._transition_to_open()
    
    def _transition_to_open(self) -> None:
        """Transition to the open state."""
        if self._state != CircuitState.OPEN:
            self._state = CircuitState.OPEN
            self._stats.last_state_change_time = datetime.now()
            self._stats.open_count += 1
            logger.warning(f"Circuit '{self.name}' state changed to OPEN")
    
    def _transition_to_half_open(self) -> None:
        """Transition to the half-open state."""
        if self._state != CircuitState.HALF_OPEN:
            self._state = CircuitState.HALF_OPEN
            self._half_open_calls = 0
            self._stats.last_state_change_time = datetime.now()
            logger.info(f"Circuit '{self.name}' state changed to HALF_OPEN")
    
    def _transition_to_closed(self) -> None:
        """Transition to the closed state."""
        if self._state != CircuitState.CLOSED:
            self._state = CircuitState.CLOSED
            self._stats.last_state_change_time = datetime.now()
            logger.info(f"Circuit '{self.name}' state changed to CLOSED")
    
    def reset(self) -> None:
        """Reset the circuit breaker to its initial closed state."""
        with self._state_lock:
            self._state = CircuitState.CLOSED
            self._stats = CircuitStats()
            self._half_open_calls = 0
            logger.info(f"Circuit '{self.name}' has been reset")
    
    def get_stats(self) -> CircuitStats:
        """Get current statistics for this circuit breaker."""
        with self._state_lock:
            return self._stats
    
    @classmethod
    def get(cls, name: str) -> Optional['CircuitBreaker']:
        """
        Get a circuit breaker by name.
        
        Args:
            name: Name of the circuit breaker to retrieve
            
        Returns:
            The circuit breaker instance or None if not found
        """
        with cls._registry_lock:
            return cls._registry.get(name)
    
    @classmethod
    def get_all(cls) -> Dict[str, 'CircuitBreaker']:
        """
        Get all registered circuit breakers.
        
        Returns:
            Dictionary of all circuit breakers, keyed by name
        """
        with cls._registry_lock:
            return cls._registry.copy() 