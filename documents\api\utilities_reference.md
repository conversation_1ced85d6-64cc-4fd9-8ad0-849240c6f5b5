# Enhanced Utilities Reference

## Overview

The utilities package provides essential support functions for the multi-terminal trading bot system, including advanced memory management, sophisticated error handling, circuit breaker patterns, and performance optimization specifically designed for 5-terminal operations with GPU acceleration.

## Enhanced Memory Management (`utils/enhanced_memory_manager.py`)

### EnhancedMemoryManager Class
Advanced memory monitoring and management with adaptive thresholds and GPU awareness:

```python
class EnhancedMemoryManager:
    def __init__(
        self,
        base_warning_threshold: float = 70.0,
        base_high_threshold: float = 80.0,
        base_critical_threshold: float = 90.0,
        enable_tracemalloc: bool = False,
        history_size: int = 60,
        adaptive_thresholds: bool = True
    ):
        # Adaptive thresholds based on system capabilities
        self.base_thresholds = {
            "WARNING": base_warning_threshold,
            "HIGH": base_high_threshold,
            "CRITICAL": base_critical_threshold
        }
        self.components = {}
        self.cleanup_handlers = {}
        self.monitor_interval = 60.0
        self.adaptive_thresholds = adaptive_thresholds
        self.memory_history = deque(maxlen=history_size)

    # Core Methods
    def register_component(self, name: str, cleanup_handlers: Dict[str, Callable])
    def cleanup(self, level: str = "moderate") -> int
    def get_memory_usage() -> float
    def start_monitoring()
    def stop_monitoring()
    def get_memory_status() -> str
    def get_component_memory_usage(component_name: str) -> float
    def optimize_memory() -> Dict[str, Any]
```

### Memory Cleanup Levels
- **LIGHT**: Basic cleanup (temporary variables, small caches)
- **MODERATE**: Standard cleanup (dataframes, model caches)
- **AGGRESSIVE**: Deep cleanup (force garbage collection, clear all caches)

### Global Memory Manager
```python
from utils.enhanced_memory_manager import enhanced_memory_manager

# Register components with cleanup handlers
enhanced_memory_manager.register_component(
    "dataframes",
    cleanup_handlers={
        CleanupLevel.LIGHT: cleanup_dataframes,
        CleanupLevel.MODERATE: cleanup_dataframes,
        CleanupLevel.AGGRESSIVE: cleanup_dataframes
    }
)

# Monitor memory usage
memory_usage = enhanced_memory_manager.get_memory_usage()
memory_status = enhanced_memory_manager.get_memory_status()

# Manual cleanup
cleaned_mb = enhanced_memory_manager.cleanup("moderate")
```

### Memory Cleanup Levels
- **Light**: Clear expired caches and temporary data
- **Moderate**: Clear memory caches and unused objects
- **Aggressive**: Force garbage collection and clear all caches

### Component Registration
```python
# Register component with cleanup handlers
memory_manager.register_component(
    "model_cache",
    cleanup_handlers={
        "light": lambda _: cache.clear_expired(),
        "moderate": lambda _: cache.clear_memory(),
        "aggressive": lambda _: cache.clear_all()
    }
)
```

## Thread Management (`utils/thread_manager.py`)

### ThreadManager Class
Manages thread pools and concurrent operations:

```python
class ThreadManager:
    def __init__(
        self,
        max_workers: int = 32,
        thread_name_prefix: str = "Worker"
    ):
        self.max_workers = max_workers
        self.thread_pools = {}
        self.active_futures = {}

    def submit_task(self, func: Callable, *args, **kwargs) -> Future
    def shutdown(self, wait: bool = True)
    def get_active_task_count() -> int
```

### Thread Pool Types
- **CPU-bound tasks**: Data processing, model training
- **I/O-bound tasks**: File operations, network requests
- **Background tasks**: Monitoring, cleanup operations

## Data Processing (`utils/data_preprocessor.py`)

### DataPreprocessor Class
Handles data cleaning and feature engineering:

```python
class DataPreprocessor:
    def __init__(self, config: Dict):
        self.config = config
        self.scalers = {}
        self.feature_columns = []

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame
    def create_sequences(self, data: np.ndarray, sequence_length: int)
    def split_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, ...]
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame
```

### Technical Indicators
- **RSI**: Relative Strength Index
- **MACD**: Moving Average Convergence Divergence
- **Bollinger Bands**: Statistical price bands
- **ATR**: Average True Range
- **Moving Averages**: SMA, EMA

### Data Validation
```python
def validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
    validation_results = {
        "missing_values": df.isnull().sum().sum(),
        "duplicate_rows": df.duplicated().sum(),
        "data_range": {
            "start_date": df.index.min(),
            "end_date": df.index.max(),
            "total_records": len(df)
        },
        "outliers": self._detect_outliers(df),
        "data_quality_score": self._calculate_quality_score(df)
    }
    return validation_results
```

## Intelligent Caching (`utils/intelligent_cache.py`)

### IntelligentCache Class
Provides multi-tier caching system:

```python
class IntelligentCache:
    def __init__(
        self,
        memory_limit: int = 1024,  # MB
        disk_limit: int = 10240,   # MB
        ttl: int = 3600           # seconds
    ):
        self.memory_cache = {}
        self.disk_cache_dir = Path("cache")
        self.memory_limit = memory_limit
        self.disk_limit = disk_limit
        self.ttl = ttl

    def get(self, key: str) -> Any
    def set(self, key: str, value: Any, ttl: int = None)
    def clear(self, tier: str = "all")
    def cleanup() -> int
```

### Cache Tiers
1. **Memory cache**: Fast access for frequently used data
2. **Disk cache**: Persistent storage for larger datasets
3. **Automatic eviction**: LRU-based cache eviction

## Model Management (`utils/model_manager.py`)

### ModelManager Class
Manages model lifecycle and operations with health checking:

```python
class ModelManager:
    def __init__(
        self,
        terminal_id: str,
        timeframe: str = "M5",
        check_model_health: bool = True
    ):
        self.terminal_id = terminal_id
        self.timeframe = timeframe
        self.check_model_health = check_model_health
        self.loaded_models = {}
        self.model_metadata = {}
        self.model_classes = {
            'lstm': LSTMModel,
            'gru': GRUModel,
            'transformer': TransformerModel,
            'tft': TFTModel,
            'xgboost': XGBoostModel,
            'lightgbm': LightGBMModel,
            'arima': ARIMAModel,
            'lstm_arima': LSTMARIMAEnsemble,
            'tft_arima': TFTARIMAEnsemble
        }

    def load_model(self, model_type: str) -> BaseModel
    def get_available_models() -> List[str]
    def validate_model_health(self, model: BaseModel) -> bool
    def clear_gpu_cache()
```

### Model Operations
- **Dynamic loading**: Load any of 9 model types on demand
- **Health checking**: Validate model integrity and performance
- **GPU memory management**: Automatic GPU cache clearing
- **Error handling**: Comprehensive error handling with fallbacks
- **Model validation**: Ensure models are properly trained and functional

## Metrics Calculation (`utils/metrics.py`)

### Metrics Functions
Comprehensive metrics calculation utilities:

```python
def calculate_metrics(
    y_true: np.ndarray,
    y_pred: np.ndarray,
    metrics: List[str] = None
) -> Dict[str, float]:

    if metrics is None:
        metrics = ['mse', 'rmse', 'mae', 'mape', 'r2']

    results = {}

    for metric in metrics:
        if metric.lower() == 'mse':
            results['mse'] = np.mean((y_true - y_pred) ** 2)
        elif metric.lower() == 'rmse':
            results['rmse'] = np.sqrt(np.mean((y_true - y_pred) ** 2))
        elif metric.lower() == 'mae':
            results['mae'] = np.mean(np.abs(y_true - y_pred))
        elif metric.lower() == 'r2':
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            ss_res = np.sum((y_true - y_pred) ** 2)
            results['r2'] = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

    return results
```

### Trading Metrics
```python
def calculate_trading_metrics(trades: List[Dict]) -> Dict[str, float]:
    total_trades = len(trades)
    winning_trades = sum(1 for trade in trades if trade['pnl'] > 0)

    return {
        'total_trades': total_trades,
        'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
        'total_pnl': sum(trade['pnl'] for trade in trades),
        'average_pnl': np.mean([trade['pnl'] for trade in trades]),
        'max_drawdown': calculate_max_drawdown(trades),
        'sharpe_ratio': calculate_sharpe_ratio(trades)
    }
```

## Configuration Validation (`utils/config_validator.py`)

### ConfigValidator Class
Validates configuration files and settings:

```python
class ConfigValidator:
    def validate_config(self, config: Dict) -> Tuple[bool, List[str]]:
        errors = []

        # Validate MT5 configuration
        if not self._validate_mt5_config(config.get('mt5', {})):
            errors.append("Invalid MT5 configuration")

        # Validate model configuration
        if not self._validate_models_config(config.get('models', {})):
            errors.append("Invalid models configuration")

        # Validate trading strategy
        if not self._validate_strategy_config(config.get('strategy', {})):
            errors.append("Invalid strategy configuration")

        return len(errors) == 0, errors
```

### Validation Rules
- **Required fields**: Check for mandatory configuration fields
- **Data types**: Validate data type consistency
- **Value ranges**: Ensure values are within acceptable ranges
- **Path validation**: Verify file and directory paths exist
- **Dependency checks**: Validate inter-dependent settings

## Enhanced Error Handling (`utils/enhanced_error_handler.py`)

### EnhancedErrorHandler Class
Comprehensive error handling with tracking and recovery:

```python
class EnhancedErrorHandler:
    def __init__(self):
        self.error_history = deque(maxlen=1000)
        self.error_counts = defaultdict(int)
        self.recovery_handlers = {}
        self.circuit_breakers = {}

    def handle_error(
        self,
        error: Exception,
        context: Dict[str, Any],
        category: ErrorCategory,
        severity: ErrorSeverity,
        recovery_strategy: Optional[str] = None
    ) -> bool

    def register_recovery_handler(
        self,
        error_type: Type[Exception],
        handler: Callable
    )

    def get_error_statistics() -> Dict[str, Any]
    def clear_error_history()
```

### Error Categories and Severity
```python
class ErrorCategory(Enum):
    SYSTEM = "system"
    NETWORK = "network"
    DATA = "data"
    MODEL = "model"
    TRADING = "trading"
    MEMORY = "memory"
    CONFIGURATION = "configuration"

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
```

## Enhanced Circuit Breaker (`utils/enhanced_circuit_breaker.py`)

### EnhancedCircuitBreaker Class
Advanced circuit breaker with multiple failure strategies:

```python
class EnhancedCircuitBreaker:
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        half_open_max_calls: int = 1,
        failure_strategy: FailureStrategy = FailureStrategy.COUNT_BASED,
        retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
        max_retries: int = 3,
        fallback_function: Optional[Callable] = None
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.half_open_calls = 0

    def call(self, func: Callable, *args, **kwargs) -> Any
    def reset()
    def get_state() -> CircuitState
    def get_metrics() -> Dict[str, Any]
```

### Circuit States
- **CLOSED**: Normal operation, calls pass through
- **OPEN**: Circuit is open, calls fail immediately
- **HALF_OPEN**: Testing if service has recovered

### Failure Strategies
- **COUNT_BASED**: Fail after N consecutive failures
- **PERCENTAGE_BASED**: Fail when failure rate exceeds threshold
- **TIME_BASED**: Fail when failures occur within time window

## Graceful Degradation (`utils/graceful_degradation.py`)

### GracefulDegradation Class
System-wide degradation and feature toggle management:

```python
class GracefulDegradation:
    def __init__(self):
        self.feature_toggles = {}
        self.health_checks = {}
        self.degradation_levels = {}

    def register_feature(self, feature: FeatureToggle)
    def disable_feature(self, feature_name: str, reason: str)
    def enable_feature(self, feature_name: str)
    def check_system_health() -> bool
    def get_system_status() -> Dict[str, Any]
```

### Feature Toggles
```python
@dataclass
class FeatureToggle:
    name: str
    enabled: bool = True
    fallback_function: Optional[Callable] = None
    health_check: Optional[Callable] = None
    priority: int = 1
    dependencies: List[str] = field(default_factory=list)
```

## Resource Tracking (`utils/resource_tracker.py`)

### ResourceTracker Class
Monitors system resource usage for multi-terminal operations:

```python
class ResourceTracker:
    def __init__(self):
        self.cpu_usage_history = []
        self.memory_usage_history = []
        self.gpu_usage_history = []
        self.disk_usage_history = []
        self.network_usage_history = []

    def get_current_usage() -> Dict[str, float]
    def log_usage()
    def get_usage_trends() -> Dict[str, List[float]]
    def detect_resource_pressure() -> List[str]
    def get_gpu_usage() -> Dict[str, float]  # RTX 4070 monitoring
```

### Monitoring Metrics
- **CPU usage**: Per-core and overall CPU utilization across 5 terminals
- **Memory usage**: RAM usage and available memory with adaptive thresholds
- **GPU usage**: NVIDIA RTX 4070 utilization and VRAM usage (12.88GB)
- **Disk I/O**: Read/write operations and disk space
- **Network I/O**: MT5 connection traffic and latency
- **Terminal-specific**: Individual terminal resource consumption
