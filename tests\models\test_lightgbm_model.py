"""
Test script for LightGBM model with comprehensive evaluation.
"""
import os
import sys
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, <PERSON>ple
import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from bayes_opt import BayesianOptimization
from sklearn.model_selection import TimeSeriesSplit

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from models.lightgbm_model import LightGBMModel
from config import config_manager

# Get model configuration
model_config = config_manager.get_model_config('lightgbm')
from tests.models.base_test import BaseModelTest

class LightGBMTest(BaseModelTest):
    """Test class for LightGBM model with comprehensive evaluation."""

    def __init__(self):
        """Initialize LightGBM test class."""
        super().__init__('lightgbm')
        self.model = None

    def optimize_hyperparameters(self, X_train: np.ndarray, y_train: np.ndar<PERSON>,
                               X_val: np.ndarray, y_val: np.ndarray) -> Dict:
        """Optimize model hyperparameters using Bayesian optimization."""
        try:
            def objective(n_estimators, learning_rate, num_leaves, max_depth,
                         min_child_samples, subsample, colsample_bytree):
                # Create model with current parameters
                params = {
                    'n_estimators': int(n_estimators),
                    'learning_rate': learning_rate,
                    'num_leaves': int(num_leaves),
                    'max_depth': int(max_depth),
                    'min_child_samples': int(min_child_samples),
                    'subsample': subsample,
                    'colsample_bytree': colsample_bytree
                }

                model = LightGBMModel(model_config)
                model.build()
                model.set_model_params(params)

                # Train and evaluate
                metrics = model.train(X_train, y_train, X_val, y_val)
                return -metrics['rmse']  # Minimize RMSE

            # Define parameter bounds
            pbounds = {
                'n_estimators': (100, 1000),
                'learning_rate': (0.01, 0.3),
                'num_leaves': (20, 100),
                'max_depth': (3, 10),
                'min_child_samples': (10, 100),
                'subsample': (0.5, 1.0),
                'colsample_bytree': (0.5, 1.0)
            }

            # Run optimization
            optimizer = BayesianOptimization(
                f=objective,
                pbounds=pbounds,
                random_state=42
            )

            optimizer.maximize(
                init_points=5,
                n_iter=20
            )

            # Get best parameters
            best_params = optimizer.max['params']
            best_params['n_estimators'] = int(best_params['n_estimators'])
            best_params['num_leaves'] = int(best_params['num_leaves'])
            best_params['max_depth'] = int(best_params['max_depth'])
            best_params['min_child_samples'] = int(best_params['min_child_samples'])

            self.logger.info(f"Best hyperparameters: {best_params}")
            return best_params

        except Exception as e:
            self.logger.error(f"Error optimizing hyperparameters: {str(e)}")
            raise

    def test_model(self):
        """Test the LightGBM model with comprehensive evaluation."""
        try:
            # Load and preprocess data
            data_path = project_root / "data" / "terminal_1" / "M5_data.csv"
            X, y = self.load_and_preprocess_data(data_path)

            # Initialize cross-validation
            tscv = TimeSeriesSplit(n_splits=5)

            # Store metrics across folds
            self.all_metrics = []

            for fold, (train_idx, test_idx) in enumerate(tscv.split(X)):
                self.logger.info(f"\nProcessing fold {fold + 1}/5")

                # Split data
                X_train, X_test = X[train_idx], X[test_idx]
                y_train, y_test = y[train_idx], y[test_idx]

                # Further split for validation
                val_size = int(len(X_train) * 0.2)
                X_train, X_val = X_train[:-val_size], X_train[-val_size:]
                y_train, y_val = y_train[:-val_size], y_train[-val_size:]

                # Optimize hyperparameters
                best_params = self.optimize_hyperparameters(X_train, y_train, X_val, y_val)

                # Initialize and train model
                self.model = LightGBMModel(model_config)
                self.model.build()
                self.model.set_model_params(best_params)

                # Monitor resources
                self.monitor_gpu_usage()
                self.monitor_memory_usage()

                # Train model
                train_metrics = self.model.train(X_train, y_train, X_val, y_val)
                self.logger.info(f"Training metrics: {train_metrics}")

                # Make predictions
                y_pred = self.model.predict(X_test)

                # Calculate metrics
                metrics = self.calculate_metrics(y_test, y_pred)
                self.all_metrics.append(metrics)
                self.logger.info(f"Test metrics: {metrics}")

                # Plot results
                self.plot_predictions(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Actual vs Predicted Values",
                    save_path=self.output_dir / f"fold_{fold}_predictions.png"
                )

                self.plot_residuals(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Prediction Residuals",
                    save_path=self.output_dir / f"fold_{fold}_residuals.png"
                )

                self.plot_error_distribution(
                    y_test, y_pred,
                    title=f"Fold {fold + 1} - Error Distribution",
                    save_path=self.output_dir / f"fold_{fold}_error_dist.png"
                )

                # Plot feature importance
                feature_importance = self.model.get_feature_importance()
                self.plot_feature_importance(
                    feature_importance,
                    title=f"Fold {fold + 1} - Feature Importance",
                    save_path=self.output_dir / f"fold_{fold}_feature_importance.png"
                )

            # Calculate and plot average metrics
            avg_metrics = pd.DataFrame(self.all_metrics).mean().to_dict()
            self.plot_metrics(
                avg_metrics,
                title="Average Model Evaluation Metrics",
                save_path=self.output_dir / "average_metrics.png"
            )

            # Generate comprehensive report
            self.generate_report()

            self.logger.info("LightGBM model test completed successfully")

        except Exception as e:
            self.logger.error(f"Error in LightGBM model test: {str(e)}")
            raise

def test_lightgbm():
    """Test the LightGBM model."""
    test = LightGBMTest()
    test.test_model()

if __name__ == "__main__":
    test = LightGBMTest()
    test.test_model()