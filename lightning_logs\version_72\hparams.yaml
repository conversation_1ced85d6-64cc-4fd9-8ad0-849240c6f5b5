config: !!python/object:config.config.ModelConfig
  BATCH_SIZE: 32
  EARLY_STOPPING_PATIENCE: 10
  FEATURE_COLUMNS:
  - open
  - high
  - low
  - close
  - real_volume
  - rsi
  - macd
  - macd_signal
  - macd_hist
  - bb_upper
  - bb_middle
  - bb_lower
  - atr
  - adx
  - obv
  - returns
  - volatility
  - momentum
  - volume_ma
  - volume_std
  GRU_PARAMS:
    batch_size: 32
    dense_units: 64
    dropout_rate: 0.2
    epochs: 100
    gru_units: 128
    learning_rate: 0.001
    num_layers: 2
    patience: 10
  LIGHTGBM_PARAMS:
    colsample_bytree: 0.8
    learning_rate: 0.01
    max_depth: 6
    min_child_samples: 20
    n_estimators: 1000
    num_leaves: 31
    random_state: 42
    subsample: 0.8
  LSTM_PARAMS:
    batch_size: 32
    dense_units: 64
    dropout_rate: 0.2
    epochs: 100
    learning_rate: 0.001
    lstm_units: 128
    num_layers: 2
    patience: 10
  MAX_EPOCHS: 100
  METRICS:
  - mae
  - mse
  - rmse
  - mape
  - r2
  PREDICTION_LENGTH: 1
  SEQUENCE_LENGTH: 100
  TFT_PARAMS:
    batch_size: 16
    dropout_rate: 0.1
    epochs: 2
    hidden_continuous_size: 16
    hidden_size: 32
    learning_rate: 0.001
    max_encoder_length: 100
    max_prediction_length: 1
    num_heads: 2
    num_layers: 1
    patience: 2
  VALIDATION_SPLIT: 0.2
  XGBOOST_PARAMS:
    colsample_bytree: 0.8
    learning_rate: 0.01
    max_depth: 6
    min_child_weight: 1
    n_estimators: 1000
    random_state: 42
    subsample: 0.8
