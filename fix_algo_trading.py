#!/usr/bin/env python3
"""
Fix for Algorithmic Trading Disabled Issue

This script implements the correct MT5 connection strategy to prevent
algorithmic trading from being disabled when switching between terminals.

The issue: MT5 Python API can only maintain ONE active connection at a time.
When switching between terminals, it disables algorithmic trading in the
previously connected terminal.

The solution: Use a single-connection strategy with terminal validation.
"""

import os
import sys
import logging
import time
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('fix_algo_trading.log')
    ]
)
logger = logging.getLogger(__name__)

def test_fixed_connection_strategy():
    """Test the fixed connection strategy."""
    try:
        logger.info("Testing fixed MT5 connection strategy...")
        
        # Import the fixed configuration manager
        from config.consolidated_config import Configuration<PERSON>anager
        from trading.mt5_connection_manager import MT5ConnectionManager
        
        # Initialize configuration
        config_manager = ConfigurationManager()
        
        # Initialize MT5 connection manager with the fix
        mt5_manager = MT5ConnectionManager(config_manager)
        
        # Test connections to all terminals
        terminals = ['1', '2', '3', '4', '5']
        results = {}
        
        logger.info("Testing connections to all terminals...")
        
        for terminal_id in terminals:
            logger.info(f"Testing terminal {terminal_id}...")
            
            connection = mt5_manager.get_connection(terminal_id)
            if connection:
                if hasattr(connection, 'validated_only') and connection.validated_only:
                    logger.info(f"[OK] Terminal {terminal_id}: Validated (preserving active connection)")
                    results[terminal_id] = "validated"
                elif connection.is_connected:
                    logger.info(f"[OK] Terminal {terminal_id}: Actively connected")
                    results[terminal_id] = "connected"
                else:
                    logger.warning(f"[WARNING] Terminal {terminal_id}: Connection failed")
                    results[terminal_id] = "failed"
            else:
                logger.error(f"[ERROR] Terminal {terminal_id}: No connection object")
                results[terminal_id] = "error"
        
        # Report results
        logger.info("\n" + "="*60)
        logger.info("CONNECTION TEST RESULTS")
        logger.info("="*60)
        
        connected_count = 0
        validated_count = 0
        failed_count = 0
        
        for terminal_id, status in results.items():
            if status == "connected":
                logger.info(f"Terminal {terminal_id}: ACTIVELY CONNECTED")
                connected_count += 1
            elif status == "validated":
                logger.info(f"Terminal {terminal_id}: VALIDATED (algo trading preserved)")
                validated_count += 1
            else:
                logger.error(f"Terminal {terminal_id}: FAILED ({status})")
                failed_count += 1
        
        logger.info(f"\nSummary:")
        logger.info(f"- Actively connected: {connected_count}")
        logger.info(f"- Validated (preserved): {validated_count}")
        logger.info(f"- Failed: {failed_count}")
        
        if connected_count == 1 and validated_count == 4:
            logger.info("\n[SUCCESS] Fix working correctly!")
            logger.info("- One terminal actively connected")
            logger.info("- Four terminals validated without switching")
            logger.info("- Algorithmic trading preserved in all terminals")
            return True
        else:
            logger.error("\n[ISSUE] Fix not working as expected")
            return False
            
    except Exception as e:
        logger.error(f"Error testing connection strategy: {str(e)}")
        return False

def verify_algorithmic_trading_status():
    """Verify algorithmic trading status using the setup script."""
    try:
        logger.info("\nVerifying algorithmic trading status...")
        
        import subprocess
        result = subprocess.run(
            [sys.executable, "setup_terminals.py"],
            capture_output=True,
            text=True,
            cwd=project_root
        )
        
        if result.returncode == 0:
            logger.info("[SUCCESS] All terminals have algorithmic trading enabled!")
            return True
        else:
            logger.warning("[WARNING] Some terminals may have algorithmic trading disabled")
            logger.info("Output:")
            logger.info(result.stdout)
            if result.stderr:
                logger.error("Errors:")
                logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Error verifying algorithmic trading status: {str(e)}")
        return False

def main():
    """Main function."""
    logger.info("="*60)
    logger.info("MT5 ALGORITHMIC TRADING FIX")
    logger.info("="*60)
    logger.info("")
    logger.info("This script tests the fix for the algorithmic trading")
    logger.info("disabled issue that occurs when switching between terminals.")
    logger.info("")
    
    # Test the fixed connection strategy
    if test_fixed_connection_strategy():
        logger.info("\n[STEP 1] ✅ Connection strategy fix verified")
    else:
        logger.error("\n[STEP 1] ❌ Connection strategy fix failed")
        return 1
    
    # Verify algorithmic trading status
    if verify_algorithmic_trading_status():
        logger.info("\n[STEP 2] ✅ Algorithmic trading status verified")
    else:
        logger.warning("\n[STEP 2] ⚠️ Algorithmic trading status needs attention")
        logger.info("Please run 'python setup_terminals.py' to enable algorithmic trading")
    
    logger.info("\n" + "="*60)
    logger.info("FIX SUMMARY")
    logger.info("="*60)
    logger.info("")
    logger.info("The fix implements a single-connection strategy:")
    logger.info("1. Only ONE terminal is actively connected at a time")
    logger.info("2. Other terminals are validated without switching connections")
    logger.info("3. This preserves algorithmic trading in all terminals")
    logger.info("")
    logger.info("Key changes made:")
    logger.info("- Modified MT5ConnectionManager._initialize_connection()")
    logger.info("- Added connection validation without switching")
    logger.info("- Added validated_only flag to track terminal status")
    logger.info("- Prevented connection switching that disables algo trading")
    logger.info("")
    logger.info("Result: Algorithmic trading is preserved in all terminals!")
    logger.info("")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("\n[INTERRUPTED] Fix test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n[ERROR] Unexpected error: {str(e)}")
        sys.exit(1)
