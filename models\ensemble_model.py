"""
Ensemble Model implementations for combining multiple models.
"""

import logging
import numpy as np
from typing import Dict, Any, Optional
from pathlib import Path
import joblib
import warnings
warnings.filterwarnings('ignore')

from .base_model import BaseModel
from .lstm_model import LSTMModel
from .arima_model import ARIMAModel
from .tft_model import TFTModel

logger = logging.getLogger(__name__)

class EnsembleModel(BaseModel):
    """Base class for ensemble models combining multiple individual models."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize ensemble model with configuration."""
        super().__init__(config)

        # Ensemble-specific parameters
        self.component_models = {}
        self.model_weights = config.get('model_weights', {})
        self.ensemble_method = config.get('ensemble_method', 'weighted_average')
        self.component_configs = config.get('component_configs', {})

        logger.info(f"Ensemble model initialized with method: {self.ensemble_method}")

    def build(self) -> None:
        """Build ensemble model by initializing component models."""
        try:
            # Initialize component models based on configuration
            for model_name, model_config in self.component_configs.items():
                if model_name == 'lstm':
                    self.component_models[model_name] = LSTMModel(model_config)
                elif model_name == 'arima':
                    self.component_models[model_name] = ARIMAModel(model_config)
                elif model_name == 'tft':
                    self.component_models[model_name] = TFTModel(model_config)
                else:
                    logger.warning(f"Unknown model type in ensemble: {model_name}")
                    continue

                # Build the component model
                self.component_models[model_name].build()
                logger.info(f"Built component model: {model_name}")

            # Normalize weights if provided
            if self.model_weights:
                total_weight = sum(self.model_weights.values())
                if total_weight > 0:
                    self.model_weights = {k: v/total_weight for k, v in self.model_weights.items()}
            else:
                # Equal weights if not specified
                n_models = len(self.component_models)
                self.model_weights = {name: 1.0/n_models for name in self.component_models.keys()}

            logger.info(f"Ensemble model built with weights: {self.model_weights}")

        except Exception as e:
            logger.error(f"Error building ensemble model: {str(e)}")
            raise

    def train(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[tuple] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Train all component models."""
        try:
            training_results = {}

            for model_name, model in self.component_models.items():
                logger.info(f"Training component model: {model_name}")

                try:
                    # Train individual model
                    result = model.train(X, y, validation_data, **kwargs)
                    training_results[model_name] = result
                    logger.info(f"Successfully trained {model_name}")

                except Exception as e:
                    logger.error(f"Failed to train {model_name}: {str(e)}")
                    training_results[model_name] = {'error': str(e)}

            # Store training history
            self.history = training_results

            return training_results

        except Exception as e:
            logger.error(f"Error training ensemble model: {str(e)}")
            raise

    def train_with_validation(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: np.ndarray,
        y_val: np.ndarray,
        **kwargs
    ) -> Dict[str, Any]:
        """Train ensemble model with validation data."""
        try:
            training_results = {}

            for model_name, model in self.component_models.items():
                logger.info(f"Training component model with validation: {model_name}")

                try:
                    result = model.train_with_validation(X_train, y_train, X_val, y_val, **kwargs)
                    training_results[model_name] = result

                    # Mark component as successfully trained
                    if hasattr(result, 'get') and result.get('success', True):
                        # Set training flag for better state tracking
                        if hasattr(model, '_is_trained'):
                            model._is_trained = True
                        logger.info(f"Successfully trained {model_name} with validation")
                    else:
                        logger.warning(f"Training of {model_name} completed but may have issues")

                except Exception as e:
                    logger.error(f"Failed to train {model_name} with validation: {str(e)}")
                    training_results[model_name] = {'error': str(e), 'success': False}

            self.history = training_results
            return training_results

        except Exception as e:
            logger.error(f"Error in ensemble train_with_validation: {str(e)}")
            raise

    def predict(self, X: np.ndarray) -> np.ndarray:
        """Generate ensemble predictions by combining component model predictions."""
        try:
            predictions = {}

            # Get predictions from each component model
            for model_name, model in self.component_models.items():
                # Check if model is trained using multiple criteria
                is_trained = False
                if hasattr(model, 'fitted_model') and model.fitted_model is not None:
                    is_trained = True
                elif hasattr(model, 'model') and model.model is not None:
                    is_trained = True
                elif hasattr(model, 'trainer') and model.trainer is not None:
                    is_trained = True
                elif hasattr(model, '_is_trained') and model._is_trained:
                    is_trained = True

                if is_trained:
                    try:
                        pred = model.predict(X)
                        predictions[model_name] = pred
                        logger.debug(f"Got predictions from {model_name}")
                    except Exception as e:
                        logger.warning(f"Failed to get predictions from {model_name}: {str(e)}")
                        continue
                else:
                    logger.warning(f"Model {model_name} is not trained, skipping")

            if not predictions:
                raise RuntimeError("No component models available for prediction")

            # Combine predictions based on ensemble method
            if self.ensemble_method == 'weighted_average':
                ensemble_pred = self._weighted_average_prediction(predictions)
            elif self.ensemble_method == 'simple_average':
                ensemble_pred = self._simple_average_prediction(predictions)
            elif self.ensemble_method == 'median':
                ensemble_pred = self._median_prediction(predictions)
            else:
                logger.warning(f"Unknown ensemble method: {self.ensemble_method}, using weighted average")
                ensemble_pred = self._weighted_average_prediction(predictions)

            return ensemble_pred

        except Exception as e:
            logger.error(f"Error making ensemble predictions: {str(e)}")
            raise

    def _weighted_average_prediction(self, predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """Combine predictions using weighted average with shape compatibility."""
        ensemble_pred = None
        total_weight = 0

        # First, ensure all predictions have compatible shapes
        pred_shapes = {name: np.asarray(pred).shape for name, pred in predictions.items()}
        logger.debug(f"Prediction shapes: {pred_shapes}")

        for model_name, pred in predictions.items():
            weight = self.model_weights.get(model_name, 0)
            if weight > 0:
                # Ensure prediction is a numpy array and flatten if needed
                pred_array = np.asarray(pred).flatten()

                if ensemble_pred is None:
                    ensemble_pred = weight * pred_array
                else:
                    # Ensure shapes are compatible before addition
                    if ensemble_pred.shape != pred_array.shape:
                        logger.warning(f"Shape mismatch: ensemble_pred {ensemble_pred.shape} vs {model_name} {pred_array.shape}")
                        # Take minimum length to avoid broadcasting errors
                        min_len = min(len(ensemble_pred), len(pred_array))
                        ensemble_pred = ensemble_pred[:min_len]
                        pred_array = pred_array[:min_len]

                    ensemble_pred += weight * pred_array
                total_weight += weight

        if total_weight > 0 and ensemble_pred is not None:
            ensemble_pred /= total_weight

        return ensemble_pred if ensemble_pred is not None else np.array([])

    def _simple_average_prediction(self, predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """Combine predictions using simple average with shape compatibility."""
        if not predictions:
            return np.array([])

        # Flatten all predictions and find minimum length
        flattened_preds = [np.asarray(pred).flatten() for pred in predictions.values()]
        min_len = min(len(pred) for pred in flattened_preds)

        # Truncate all predictions to minimum length
        truncated_preds = [pred[:min_len] for pred in flattened_preds]

        pred_array = np.array(truncated_preds)
        return np.mean(pred_array, axis=0)

    def _median_prediction(self, predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """Combine predictions using median with shape compatibility."""
        if not predictions:
            return np.array([])

        # Flatten all predictions and find minimum length
        flattened_preds = [np.asarray(pred).flatten() for pred in predictions.values()]
        min_len = min(len(pred) for pred in flattened_preds)

        # Truncate all predictions to minimum length
        truncated_preds = [pred[:min_len] for pred in flattened_preds]

        pred_array = np.array(truncated_preds)
        return np.median(pred_array, axis=0)

    def save_model(self, path: str) -> None:
        """Save ensemble model and all component models."""
        try:
            # Check if ensemble has been trained
            if not hasattr(self, 'history') or not self.history:
                logger.warning("Ensemble model has not been trained yet, cannot save")
                raise RuntimeError("Ensemble model not trained")

            # Ensure directory exists
            Path(path).parent.mkdir(parents=True, exist_ok=True)

            # Save component models
            saved_components = 0
            component_paths = {}

            for model_name, model in self.component_models.items():
                try:
                    # Check if component model is trained
                    is_trained = False
                    if hasattr(model, 'fitted_model') and model.fitted_model is not None:
                        is_trained = True
                    elif hasattr(model, 'model') and model.model is not None:
                        is_trained = True
                    elif hasattr(model, 'trainer') and model.trainer is not None:
                        is_trained = True
                    elif hasattr(model, '_is_trained') and model._is_trained:
                        is_trained = True

                    if is_trained:
                        component_path = f"{path}_{model_name}"
                        model.save_model(component_path)
                        component_paths[model_name] = component_path
                        saved_components += 1
                        logger.info(f"Saved component model: {model_name}")
                    else:
                        logger.warning(f"Component model {model_name} is not trained, skipping save")

                except Exception as e:
                    logger.error(f"Error saving component model {model_name}: {str(e)}")

            # Require at least one component to be saved
            if saved_components == 0:
                logger.error("No component models were saved successfully")
                raise RuntimeError("No trained component models to save")

            # Save ensemble metadata
            ensemble_data = {
                'model_weights': self.model_weights,
                'ensemble_method': self.ensemble_method,
                'component_configs': self.component_configs,
                'component_paths': component_paths,
                'history': self.history,
                'saved_components': saved_components,
                'total_components': len(self.component_models)
            }

            joblib.dump(ensemble_data, f"{path}_ensemble.pkl")
            logger.info(f"Ensemble model saved to {path} with {saved_components}/{len(self.component_models)} components")

        except Exception as e:
            logger.error(f"Error saving ensemble model: {str(e)}")
            raise

    def load_model(self, path: str) -> None:
        """Load ensemble model and all component models."""
        try:
            # Load ensemble metadata
            ensemble_file = f"{path}_ensemble.pkl"
            if not Path(ensemble_file).exists():
                raise FileNotFoundError(f"Ensemble metadata file not found: {ensemble_file}")

            ensemble_data = joblib.load(ensemble_file)
            self.model_weights = ensemble_data['model_weights']
            self.ensemble_method = ensemble_data['ensemble_method']
            self.component_configs = ensemble_data['component_configs']
            self.history = ensemble_data.get('history', {})

            # Load component models
            for model_name in self.component_configs.keys():
                component_path = f"{path}_{model_name}"
                if model_name in self.component_models:
                    self.component_models[model_name].load_model(component_path)
                    logger.info(f"Loaded component model: {model_name}")

            logger.info(f"Ensemble model loaded from {path}")

        except Exception as e:
            logger.error(f"Error loading ensemble model: {str(e)}")
            raise

    def _get_model_extension(self) -> str:
        """Return the appropriate file extension for ensemble models."""
        return ".pkl"

    def is_trained(self) -> bool:
        """Check if all component models are trained."""
        return all(model.is_trained() for model in self.component_models.values())


class LSTMARIMAEnsemble(EnsembleModel):
    """LSTM + ARIMA ensemble model."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize LSTM + ARIMA ensemble."""
        # Set up component configurations
        config['component_configs'] = {
            'lstm': config.get('lstm_config', config),
            'arima': config.get('arima_config', config)
        }

        # Default weights
        if 'model_weights' not in config:
            config['model_weights'] = {'lstm': 0.7, 'arima': 0.3}

        super().__init__(config)
        logger.info("LSTM + ARIMA ensemble model initialized")


class TFTARIMAEnsemble(EnsembleModel):
    """TFT + ARIMA ensemble model."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize TFT + ARIMA ensemble."""
        # Set up component configurations
        config['component_configs'] = {
            'tft': config.get('tft_config', config),
            'arima': config.get('arima_config', config)
        }

        # Default weights
        if 'model_weights' not in config:
            config['model_weights'] = {'tft': 0.8, 'arima': 0.2}

        super().__init__(config)
        logger.info("TFT + ARIMA ensemble model initialized")
