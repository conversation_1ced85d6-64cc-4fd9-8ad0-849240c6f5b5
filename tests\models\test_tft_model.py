import pytest
import pandas as pd
import numpy as np
import torch
from pathlib import Path
import logging
import sys
import os
from typing import Any

# Ensure the project root is in the Python path
project_root = Path(__file__).resolve().parents[2]
sys.path.insert(0, str(project_root))

from config.consolidated_config import ConfigurationManager
# Import the TFT model
try:
    from models.tft_model import TFTModel
    # Try to import PyTorch implementation
    try:
        from pytorch_forecasting import TemporalFusionTransformer
        # Use TemporalFusionTransformer from pytorch_forecasting as the model class
        # TFTModel is our wrapper around it
        PYTORCH_AVAILABLE = True
        TemporalFusionTransformerModel = TemporalFusionTransformer
    except ImportError:
        PYTORCH_AVAILABLE = False
        TemporalFusionTransformerModel = None
        print("Could not import TemporalFusionTransformer from pytorch_forecasting, PyTorch implementation not available")

    # TensorFlow implementation removed - using PyTorch only
    TF_AVAILABLE = False

except ImportError:
    print("Could not import TFTModel from models.tft_model")
    # Fallback or error handling if needed
    TFTModel = None
    TemporalFusionTransformerModel = None
    PYTORCH_AVAILABLE = False
    TF_AVAILABLE = False
# Assuming DataPreprocessor exists
from utils.data_preprocessor import DataPreprocessor
# Assuming metrics utility exists
from utils.metrics import calculate_metrics
# PyTorch Forecasting imports
try:
    from pytorch_forecasting import TimeSeriesDataSet
    from pytorch_forecasting.data import GroupNormalizer
    from pytorch_forecasting.metrics import QuantileLoss # Import directly if used
    from pytorch_lightning import Trainer
    from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint
    from pytorch_lightning.loggers import TensorBoardLogger
except ImportError as e:
     print(f"Error importing PyTorch Forecasting or Lightning: {e}. Please ensure these libraries are installed.")
     TimeSeriesDataSet, GroupNormalizer, QuantileLoss, Trainer, EarlyStopping, ModelCheckpoint, TensorBoardLogger = (None,) * 7 # Placeholders



logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Helper Function --- #
def prepare_tft_dataset(data: pd.DataFrame, config: dict) -> Any:
    """Prepares a TimeSeriesDataSet for the TFT model using dictionary config."""
    if TimeSeriesDataSet is None:
        logger.error("TimeSeriesDataSet is not available due to import error.")
        return None
    try:
        # Ensure 'time_idx' is present and sequential
        time_idx_col = config.get('time_idx', 'time_idx') # Use config key
        if time_idx_col not in data.columns:
            logger.info(f"'{time_idx_col}' not found, attempting creation from 'time' column.")
            if 'time' in data.columns:
                 data = data.sort_values('time').reset_index(drop=True)
                 # Create a simple sequential index if conversion fails or 'time' doesn't exist
                 try:
                     # Example: Hourly index from datetime
                     data[time_idx_col] = (data['time'] - data['time'].min()).dt.total_seconds() // 3600
                     data[time_idx_col] = data[time_idx_col].astype(int)
                 except Exception:
                     logger.warning("Could not create time index from 'time', using simple sequential index.")
                     data[time_idx_col] = data.reset_index().index
            else:
                 logger.warning(f"'{time_idx_col}' and 'time' column not found. Creating simple sequential index.")
                 data[time_idx_col] = data.reset_index().index

        # Ensure 'group_id' is present - use config key
        group_ids_config = config.get('group_ids', ['group_id']) # Expects a list
        if not isinstance(group_ids_config, list):
             logger.warning(f"Config 'group_ids' is not a list ({group_ids_config}). Using default ['group_id']")
             group_ids_config = ['group_id']
        for group_id_col in group_ids_config:
            if group_id_col not in data.columns:
                logger.info(f"Group ID column '{group_id_col}' not found, adding default group 'A'.")
                data[group_id_col] = 'A' # Assign a default group

        # Validate required config keys are present using dictionary access
        required_keys_structure = {
            'max_encoder_length': int, 'max_prediction_length': int,
            'target': str, 'time_idx': str, 'group_ids': list,
            'time_varying_known_reals': list, 'time_varying_unknown_reals': list,
            'static_categoricals': list
        }
        for key, expected_type in required_keys_structure.items():
            value = config.get(key)
            if value is None:
                # Allow empty lists for features, but not None for others
                if key in ['time_varying_known_reals', 'time_varying_unknown_reals', 'static_categoricals'] and isinstance(value, list):
                    continue
                raise ValueError(f"Missing or None value for required config key for TFT dataset: '{key}'")
            if not isinstance(value, expected_type):
                 # Special case: allow None for feature lists if explicitly set, treat as empty list
                 if key in ['time_varying_known_reals', 'time_varying_unknown_reals', 'static_categoricals'] and value is None:
                     logger.warning(f"Config key '{key}' is None. Treating as empty list.")
                     config[key] = [] # Update config in place for consistency
                 else:
                      raise TypeError(f"Config key '{key}' has wrong type. Expected {expected_type}, got {type(value)}")


        # Ensure target is in the dataframe
        target_col = config['target'] # Direct access after validation
        if target_col not in data.columns:
             raise ValueError(f"Target column '{target_col}' not found in DataFrame.")

        # Ensure all specified feature columns exist
        all_feature_cols = (
            config.get('time_varying_known_reals', []) +
            config.get('time_varying_unknown_reals', []) +
            config.get('static_categoricals', [])
        )
        # Filter out potential None values if config explicitly had them (though corrected above)
        all_feature_cols = [col for col in all_feature_cols if col is not None]

        missing_cols = [col for col in all_feature_cols if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing feature columns specified in config: {missing_cols}")

        # Handle potential NaN/inf in target and features BEFORE creating dataset
        cols_to_check = [target_col, time_idx_col] + all_feature_cols + group_ids_config
        cols_to_check = list(set([col for col in cols_to_check if col in data.columns])) # Check only existing cols

        for col in cols_to_check:
             # Convert to numeric if possible, coercing errors, before checking NaN/inf
             # This helps if a column intended as numeric is object type
             if not pd.api.types.is_numeric_dtype(data[col]):
                 data[col] = pd.to_numeric(data[col], errors='coerce')
                 if data[col].isnull().any():
                     logger.warning(f"Coerced non-numeric column '{col}' to numeric, introducing NaNs.")

             if data[col].isnull().any():
                 logger.warning(f"NaN values found in column '{col}'. Filling with forward fill then 0.")
                 data[col] = data[col].ffill().fillna(0) # ffill first, then fill remaining with 0
             if np.isinf(data[col]).any():
                  logger.warning(f"Infinite values found in column '{col}'. Replacing with large finite numbers.")
                  data[col] = data[col].replace([np.inf, -np.inf], [1e9, -1e9]) # Example replacement

        # Final check for NaNs after filling
        if data[cols_to_check].isnull().any().any():
             logger.error(f"NaN values still present in columns {cols_to_check} after attempting fill. Aborting.")
             raise ValueError("Unfillable NaN values detected before TimeSeriesDataSet creation.")


        logger.info(f"Creating TimeSeriesDataSet with target='{config['target']}', time_idx='{config['time_idx']}', group_ids={config['group_ids']}")
        logger.info(f"Encoder length: {config['max_encoder_length']}, Prediction length: {config['max_prediction_length']}")
        logger.info(f"Available columns for dataset: {data.columns.tolist()}")
        logger.info(f"Feature config: time_varying_known={config.get('time_varying_known_reals')}, time_varying_unknown={config.get('time_varying_unknown_reals')}, static_categoricals={config.get('static_categoricals')}")

        # --- TimeSeriesDataSet Creation ---
        # Ensure GroupNormalizer is available
        if GroupNormalizer is None:
            logger.error("GroupNormalizer not available due to import error.")
            return None

        dataset = TimeSeriesDataSet(
            data,
            time_idx=config['time_idx'], # Direct access after validation
            target=config['target'],   # Direct access after validation
            group_ids=config['group_ids'], # Direct access after validation
            max_encoder_length=config['max_encoder_length'], # Direct access
            max_prediction_length=config['max_prediction_length'], # Direct access
            # Use .get() for optional feature lists, defaulting to empty list
            static_categoricals=config.get('static_categoricals', []),
            time_varying_known_reals=config.get('time_varying_known_reals', []),
            time_varying_unknown_reals=config.get('time_varying_unknown_reals', []),
            # Use target normalizer robust to outliers if needed
            target_normalizer=GroupNormalizer(
                groups=config['group_ids'], # Use validated groups
                transformation=config.get("target_transformation", "softplus") # Allow configurable transformation
            ),
            add_relative_time_idx=config.get("add_relative_time_idx", True), # Configurable
            add_target_scales=config.get("add_target_scales", True), # Configurable
            add_encoder_length=config.get("add_encoder_length", True), # Configurable
            allow_missing_timesteps=config.get('allow_missing_timesteps', True) # Configurable
        )
        logger.info("TimeSeriesDataSet created successfully.")
        return dataset

    except KeyError as e:
        logger.error(f"Configuration key error during dataset preparation: Missing key {e}")
        raise
    except ValueError as e:
        logger.error(f"Value error during dataset preparation: {e}")
        raise
    except TypeError as e:
         logger.error(f"Type error during dataset preparation (likely config issue): {e}")
         raise
    except Exception as e:
        logger.error(f"Unexpected error during dataset preparation: {e}", exc_info=True)
        raise


# --- Pytest Fixtures --- #
@pytest.fixture(scope="class")
def configuration_manager():
    """Fixture to provide a ConfigurationManager instance."""
    return ConfigurationManager()

@pytest.fixture(scope="class")
def tft_config(configuration_manager):
    """Fixture to provide TFT model configuration as a dictionary."""
    base_config = configuration_manager.get_model_config('tft')
    if not base_config:
        pytest.fail("Failed to load base TFT configuration using get_model_config('tft').")
    if not isinstance(base_config, dict):
         pytest.fail(f"Expected configuration from get_model_config to be a dict, but got {type(base_config)}")

    # Define test-specific overrides or defaults safely using .get()
    test_overrides = {
        "model_path": str(project_root / "tests" / "test_outputs" / "tft_test_model.pt"),
        "batch_size": 16, # Test-specific batch size
        "max_epochs": 3,   # Fewer epochs for testing
        # Essential TFT structure params (ensure they are present or provide defaults)
        "max_encoder_length": base_config.get('max_encoder_length', 60),
        "max_prediction_length": base_config.get('max_prediction_length', 20),
        "target": base_config.get('target', 'close'),
        "time_idx": base_config.get('time_idx', 'time_idx'),
        "group_ids": base_config.get('group_ids', ['group_id']), # Default group list
        # Feature lists (use base config or provide test defaults)
        "static_categoricals": base_config.get('static_categoricals', []),
        "time_varying_known_reals": base_config.get('time_varying_known_reals', ['time_idx']), # Minimal known real
        "time_varying_unknown_reals": base_config.get('time_varying_unknown_reals', ['close', 'volume']), # Minimal unknown reals
        # TFT hyperparameters (use base config or provide test defaults)
        "hidden_size": base_config.get('hidden_size', 16), # Smaller hidden size for testing
        "lstm_layers": base_config.get('lstm_layers', 1), # Fewer layers for testing
        "num_attention_heads": base_config.get('num_attention_heads', 2), # Fewer heads
        "dropout": base_config.get('dropout', 0.2), # Slightly higher dropout maybe
        "learning_rate": base_config.get('learning_rate', 0.01), # Higher LR for faster convergence in short test
        "gradient_clip_val": base_config.get("gradient_clip_val", 0.5),
        "use_gpu": base_config.get("use_gpu", torch.cuda.is_available()), # Check GPU availability
        "allow_missing_timesteps": base_config.get("allow_missing_timesteps", True),
        "num_workers": base_config.get("num_workers", 0), # For dataloader
         # Loss and optimizer related
        "loss_config": base_config.get("loss_config", {"class": "QuantileLoss"}), # Config for loss
        "optimizer_config": base_config.get("optimizer_config", {"class": "AdamW", "lr": 0.01}), # Config for optimizer
        # Early stopping config
        "early_stopping_monitor": base_config.get("early_stopping_monitor", "train_loss"),
        "early_stopping_min_delta": base_config.get("early_stopping_min_delta", 1e-3),
        "early_stopping_patience": base_config.get("early_stopping_patience", 3),
        "early_stopping_mode": base_config.get("early_stopping_mode", "min"),
        # Target Normalizer Transformation
        "target_transformation": base_config.get("target_transformation", "softplus")
    }

    # Combine base config with overrides. Overrides take precedence.
    final_config = base_config.copy() # Create a copy
    final_config.update(test_overrides) # Apply overrides

    # Create output directory if it doesn't exist
    model_dir = Path(final_config["model_path"]).parent
    model_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Using final TFT config for tests: {final_config}")
    return final_config # Return the dictionary

@pytest.fixture(scope="class")
def sample_data(tft_config):
    """Fixture to provide sample data compatible with the config."""
    data_path = project_root / "tests" / "test_data" / "sample_tft_data.csv"
    required_columns = [
        tft_config['target'],
        # Add feature columns required by the config
    ] + tft_config.get('time_varying_known_reals', []) + \
      tft_config.get('time_varying_unknown_reals', []) + \
      tft_config.get('static_categoricals', []) + \
      tft_config.get('group_ids', ['group_id']) # Include group IDs

    # Remove duplicates and None values
    required_columns = list(set(col for col in required_columns if col is not None))

    if data_path.exists():
        logger.info(f"Loading sample data from {data_path}")
        df = pd.read_csv(data_path, parse_dates=['time'])
        # Check if loaded data has required columns
        missing_loaded_cols = [col for col in required_columns if col not in df.columns]
        if missing_loaded_cols:
            logger.warning(f"Loaded sample data missing required columns: {missing_loaded_cols}. Will attempt synthetic generation.")
            df = None # Force synthetic generation
        else:
            logger.info("Loaded sample data has required columns.")
    else:
        df = None # Force synthetic generation if file doesn't exist

    if df is None:
        logger.warning(f"Sample data file not found at {data_path} or incomplete. Creating synthetic data.")
        num_series = 2
        periods_per_series = 150 # Needs to be >= max_encoder_length + max_prediction_length
        # Calculate total periods (not used directly but kept for reference)
        _ = num_series * periods_per_series

        df_list = []
        for i in range(num_series):
            group = chr(ord('A') + i) # Group 'A', 'B', ...
            dates = pd.to_datetime(pd.date_range(start="2023-01-01", periods=periods_per_series, freq="h"))
            series_df = pd.DataFrame({
                'time': dates,
                'group_id': group # Use the correct group_id column name from config
            })
            # Base value for the target
            base_value = 1000 + np.random.rand() * 100 + i * 50
            series_df[tft_config['target']] = base_value + np.random.randn(periods_per_series).cumsum() * 0.5

            # Generate other required features based on config
            for col in required_columns:
                if col not in series_df.columns:
                    if col == 'volume':
                        series_df[col] = np.random.randint(100, 1000, size=periods_per_series)
                    elif 'real' in col or col == tft_config['target']: # Generate random data for other numeric features
                         series_df[col] = base_value/10 + np.random.rand(periods_per_series) * 10
                    elif 'categorical' in col: # Generate simple categoricals if needed
                         series_df[col] = f"cat_{i % 2}" # Example static categorical
                    # Add specific known real generation if needed (e.g., time features)
                    elif col == 'hour_sin': series_df[col] = np.sin(2 * np.pi * series_df['time'].dt.hour / 24)
                    elif col == 'hour_cos': series_df[col] = np.cos(2 * np.pi * series_df['time'].dt.hour / 24)


            df_list.append(series_df)

        df = pd.concat(df_list, ignore_index=True)

        # Ensure essential columns exist even if not in required_columns initially
        if 'time' not in df.columns: df['time'] = pd.to_datetime(pd.date_range(start="2023-01-01", periods=len(df), freq="h")) # Needs a time column
        if tft_config['target'] not in df.columns: df[tft_config['target']] = np.random.rand(len(df)) * 100 # Ensure target exists
        for gid in tft_config.get('group_ids', ['group_id']): # Ensure group ids exist
             if gid not in df.columns: df[gid] = 'A'


        # Ensure data_path dir exists before saving
        data_path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(data_path, index=False)
        logger.info(f"Saved synthetic data with columns {df.columns.tolist()} to {data_path}")

    # Prepare 'time_idx' required by TimeSeriesDataSet
    time_idx_col = tft_config['time_idx']
    if time_idx_col not in df.columns:
        if 'time' in df.columns:
            df = df.sort_values(['time']).reset_index(drop=True) # Sort before creating index
            df[time_idx_col] = (df['time'] - df['time'].min()).dt.total_seconds() // 3600 # Example: hourly index
            df[time_idx_col] = df[time_idx_col].astype(int)
            logger.info(f"Created time_idx '{time_idx_col}' from 'time' column.")
        else:
            df = df.reset_index(drop=True) # Ensure basic index if no time col
            df[time_idx_col] = df.index
            logger.warning(f"Created simple sequential time_idx '{time_idx_col}' as 'time' column was missing.")
    else:
        # Ensure existing time_idx is integer and sorted
        df[time_idx_col] = pd.to_numeric(df[time_idx_col], errors='coerce').fillna(0).astype(int)
        df = df.sort_values(tft_config.get('group_ids', []) + [time_idx_col]).reset_index(drop=True)


    # Ensure all required columns are present after generation/loading
    final_missing_cols = [col for col in required_columns if col not in df.columns]
    if final_missing_cols:
         pytest.fail(f"Sample data is still missing required columns after processing: {final_missing_cols}. Columns present: {df.columns.tolist()}")

    # Check for NaNs/infs in crucial columns before preprocessing
    check_cols_nan = [tft_config['target'], time_idx_col] + tft_config.get('group_ids', [])
    check_cols_nan = [col for col in check_cols_nan if col in df.columns] # Check only existing
    if df[check_cols_nan].isnull().any().any():
         logger.warning(f"NaNs found in essential columns {check_cols_nan} in raw sample data. Attempting fill.")
         df[check_cols_nan] = df[check_cols_nan].ffill().fillna(0) # Example fill
         if df[check_cols_nan].isnull().any().any():
             pytest.fail(f"NaNs persisted in essential columns {check_cols_nan} after fill.")


    logger.info(f"Sample data shape: {df.shape}")
    logger.info(f"Sample data columns: {df.columns.tolist()}")
    return df


@pytest.fixture(scope="class")
def preprocessed_data(sample_data, tft_config):
    """Fixture to provide preprocessed data using dictionary config."""
    # DataPreprocessor expects a config dictionary
    # Make sure DataPreprocessor handles the dict correctly
    try:
        # Pass the config dict - ensure DataPreprocessor __init__ accepts `config=`
        preprocessor = DataPreprocessor(config=tft_config)
    except TypeError as e:
         if "unexpected keyword argument 'config'" in str(e) or "__init__() got multiple values for argument" in str(e):
              pytest.fail("DataPreprocessor.__init__ does not seem to accept 'config=' keyword argument or has conflicting args. Update DataPreprocessor.")
         else: raise # Re-raise other TypeErrors

    # Ensure sample_data has required raw columns before preprocessing
    # (Checked mostly in sample_data fixture, but double-check core ones)
    core_cols = [tft_config['target'], tft_config['time_idx']] + tft_config['group_ids']
    missing_raw_cols = [col for col in core_cols if col not in sample_data.columns]
    if missing_raw_cols:
        pytest.fail(f"Missing core columns in sample_data before preprocessing: {missing_raw_cols}")

    logger.info("Preprocessing data...")
    # Call preprocess_data - ensure it returns DataFrame, scaler or handles failures
    try:
        # Assuming preprocess_data might return df, scaler or similar tuple
        preprocess_result = preprocessor.preprocess_data(sample_data.copy())
        if isinstance(preprocess_result, tuple) and len(preprocess_result) > 0:
            processed_df = preprocess_result[0]
            # scaler = preprocess_result[1] # if scaler is returned
        elif isinstance(preprocess_result, pd.DataFrame):
             processed_df = preprocess_result
        else:
             pytest.fail(f"Unexpected return type from preprocessor.preprocess_data: {type(preprocess_result)}")

    except Exception as e:
         logger.error(f"Error during DataPreprocessor.preprocess_data: {e}", exc_info=True)
         pytest.fail(f"Data preprocessing failed: {e}")


    if processed_df is None or processed_df.empty:
        pytest.fail("Preprocessing returned empty or None DataFrame.")

    # Post-processing checks: ensure essential columns for TFT are present
    essential_tft_cols = [
        tft_config['time_idx'],
        tft_config['target'],
    ] + tft_config['group_ids'] + \
      tft_config.get('static_categoricals', []) + \
      tft_config.get('time_varying_known_reals', []) + \
      tft_config.get('time_varying_unknown_reals', [])

    essential_tft_cols = list(set([col for col in essential_tft_cols if col is not None])) # Unique, non-None

    missing_post_cols = [col for col in essential_tft_cols if col not in processed_df.columns]
    if missing_post_cols:
         logger.error(f"Essential columns missing after preprocessing: {missing_post_cols}. Columns present: {processed_df.columns.tolist()}")
         pytest.fail(f"Essential columns for TFT missing after preprocessing: {missing_post_cols}. Check preprocessor logic.")


    logger.info(f"Preprocessed data shape: {processed_df.shape}")
    logger.info(f"Preprocessed data columns: {processed_df.columns.tolist()}")
    # Check for NaN/Infs again after preprocessing in essential columns
    numeric_cols = processed_df[essential_tft_cols].select_dtypes(include=np.number).columns
    if not numeric_cols.empty:
        if processed_df[numeric_cols].isnull().any().any():
            logger.error(f"NaN values found after preprocessing in columns: {processed_df[numeric_cols].isnull().sum()[processed_df[numeric_cols].isnull().sum() > 0].index.tolist()}")
            pytest.fail("NaN values found after preprocessing in essential numeric columns.")
        if np.isinf(processed_df[numeric_cols].values).any(): # Check entire array for inf
            logger.error(f"Infinite values found after preprocessing in numeric columns.")
            pytest.fail("Infinite values found after preprocessing in essential numeric columns.")

    return processed_df


@pytest.fixture(scope="class")
def tft_dataset(preprocessed_data, tft_config):
    """Fixture to create TimeSeriesDataSet using dictionary config."""
    # Essential columns should be present after preprocessed_data fixture checks
    logger.info("Preparing TimeSeriesDataSet...")
    dataset = prepare_tft_dataset(preprocessed_data.copy(), tft_config) # Pass a copy and config dict
    if dataset is None:
        pytest.fail("Failed to create TimeSeriesDataSet (returned None). Check logs.")
    return dataset

# --- Utility to instantiate model from config ---
def instantiate_tft_model(dataset: Any, config: dict):
     """Instantiates the TFT model from dataset and config dictionary."""
     if TemporalFusionTransformerModel is None or dataset is None:
         logger.error("TemporalFusionTransformerModel or dataset not available.")
         return None
     try:
         # Extract loss config
         loss_conf = config.get('loss_config', {'class': 'QuantileLoss'})
         loss_class_name = loss_conf.get('class', 'QuantileLoss')
         loss_params = {k: v for k, v in loss_conf.items() if k != 'class'}

         # Import the loss class dynamically (add more as needed)
         if loss_class_name == "QuantileLoss" and QuantileLoss:
             loss_instance = QuantileLoss(**loss_params)
         # Add other losses like MSELoss, etc.
         # elif loss_class_name == "MSELoss":
         #     from torch.nn import MSELoss
         #     loss_instance = MSELoss(**loss_params)
         else:
             logger.warning(f"Loss class '{loss_class_name}' not recognized or imported. Defaulting to QuantileLoss.")
             loss_instance = QuantileLoss() # Default fallback


         # Extract optimizer config (just name for TFT.from_dataset)
         optimizer_conf = config.get('optimizer_config', {'class': 'AdamW'})
         optimizer_name = optimizer_conf.get('class', 'AdamW')

         # Instantiate model using from_dataset
         model = TemporalFusionTransformerModel.from_dataset(
             dataset,
             # Pass hyperparameters from config dictionary using .get() with defaults
             learning_rate=config.get('learning_rate', 0.001),
             hidden_size=config.get('hidden_size', 16),
             attention_head_size=config.get('num_attention_heads', 2),
             dropout=config.get('dropout', 0.1),
             lstm_layers=config.get('lstm_layers', 1),
             # Pass instantiated loss object
             loss=loss_instance,
             # Pass optimizer name string
             optimizer=optimizer_name, # TFT handles optimizer instantiation internally
             # Pass other relevant hyperparameters from config
             reduce_on_plateau_patience = config.get('reduce_on_plateau_patience', 10)
             # Add any other hyperparameters TFT.from_dataset accepts
             # Example: output_size (usually inferred from loss quantiles), log_interval etc.
         )
         logger.info("TFT model instantiated successfully using from_dataset.")
         return model
     except Exception as e:
         logger.error(f"Error instantiating TFT model using from_dataset: {e}", exc_info=True)
         raise # Re-raise the exception to fail the test


# --- Test Class --- #
@pytest.mark.usefixtures("configuration_manager", "tft_config", "sample_data", "preprocessed_data", "tft_dataset")
class TestTFTModel:

    # Inject fixtures via method arguments
    def test_config_loading(self, tft_config):
        """Test if TFT configuration is loaded correctly as a dict."""
        logger.info("Running test_config_loading...")
        assert tft_config is not None, "tft_config fixture is None"
        assert isinstance(tft_config, dict), f"tft_config is not a dict, type is {type(tft_config)}"
        # Check essential keys using dictionary access (.get() for safety)
        assert tft_config.get('model_type') == 'tft', "model_type key missing or incorrect"
        assert 'model_path' in tft_config, "model_path key missing"
        assert isinstance(tft_config.get('max_encoder_length'), int), "max_encoder_length missing or not int"
        assert isinstance(tft_config.get('max_prediction_length'), int), "max_prediction_length missing or not int"
        assert isinstance(tft_config.get('target'), str), "target key missing or not str"
        assert isinstance(tft_config.get('time_idx'), str), "time_idx key missing or not str"
        assert isinstance(tft_config.get('group_ids'), list), "group_ids key missing or not list"
        logger.info("test_config_loading PASSED.")

    def test_data_loading(self, sample_data, tft_config):
        """Test if sample data is loaded/generated correctly and has required columns."""
        logger.info("Running test_data_loading...")
        assert sample_data is not None, "sample_data fixture is None"
        assert not sample_data.empty, "sample_data DataFrame is empty"
        # Check for essential columns based on config dict
        assert tft_config['target'] in sample_data.columns, f"Target column '{tft_config['target']}' missing"
        for group_col in tft_config['group_ids']:
             assert group_col in sample_data.columns, f"Group ID column '{group_col}' missing"
        assert tft_config['time_idx'] in sample_data.columns, f"Time index column '{tft_config['time_idx']}' missing"
        logger.info("test_data_loading PASSED.")

    def test_preprocessing(self, preprocessed_data, tft_config):
        """Test data preprocessing step output using config dictionary."""
        logger.info("Running test_preprocessing...")
        assert preprocessed_data is not None, "preprocessed_data fixture is None"
        assert not preprocessed_data.empty, "preprocessed_data DataFrame is empty"

        # Check if essential columns needed by TimeSeriesDataSet exist after preprocessing
        essential_tft_cols = [
            tft_config['target'], tft_config['time_idx']
        ] + tft_config.get('group_ids', []) + \
          tft_config.get('static_categoricals', []) + \
          tft_config.get('time_varying_known_reals', []) + \
          tft_config.get('time_varying_unknown_reals', [])
        essential_tft_cols = list(set([col for col in essential_tft_cols if col is not None])) # Unique, non-None

        missing_essential = [col for col in essential_tft_cols if col not in preprocessed_data.columns]
        if missing_essential:
              logger.error(f"Essential columns missing after preprocessing: {missing_essential}. Available: {preprocessed_data.columns.tolist()}")
              pytest.fail(f"Essential columns for TFT missing after preprocessing: {missing_essential}")

        # Check for NaN/inf values which should have been handled
        numeric_cols = preprocessed_data[essential_tft_cols].select_dtypes(include=np.number).columns
        if not numeric_cols.empty:
             assert not preprocessed_data[numeric_cols].isnull().any().any(), "NaN values found after preprocessing"
             assert not np.isinf(preprocessed_data[numeric_cols].values).any(), "Infinite values found after preprocessing" # Check underlying numpy array

        logger.info("test_preprocessing PASSED.")


    def test_dataset_creation(self, tft_dataset, tft_config):
        """Test TimeSeriesDataSet creation using config dictionary."""
        logger.info("Running test_dataset_creation...")
        assert tft_dataset is not None, "tft_dataset fixture is None"
        assert isinstance(tft_dataset, TimeSeriesDataSet), "tft_dataset is not a TimeSeriesDataSet object"
        # Check dataset parameters match config (using dictionary access)
        assert tft_dataset.max_encoder_length == tft_config['max_encoder_length']
        assert tft_dataset.max_prediction_length == tft_config['max_prediction_length']
        assert tft_dataset.target == tft_config['target']
        assert tft_dataset.time_idx == tft_config['time_idx']
        assert tft_dataset.group_ids == tft_config['group_ids']
        # Check if data has been loaded into the dataset
        assert len(tft_dataset) > 0, "TimeSeriesDataSet is empty (length is 0)"

        # Check a sample batch can be created from the dataloader
        try:
             dataloader = tft_dataset.to_dataloader(
                 train=False, # Use validation/test mode data for check
                 batch_size=tft_config.get('batch_size', 2), # Small batch size for test
                 num_workers=tft_config.get('num_workers', 0)
                 )
             sample_batch = next(iter(dataloader))
             assert sample_batch is not None, "First batch from dataloader is None"
             assert isinstance(sample_batch, tuple) and len(sample_batch) == 2, "Batch should be a tuple of (input_dict, target_tuple)"
             assert isinstance(sample_batch[0], dict), "First element of batch (inputs) should be a dict"
             assert isinstance(sample_batch[1], tuple), "Second element of batch (targets) should be a tuple"
             logger.info("Successfully created a sample batch from TimeSeriesDataSet dataloader.")
        except StopIteration:
              pytest.fail("Failed to get a batch from the dataloader (dataset might be too small for batch size/lengths).")
        except Exception as e:
             logger.error(f"Failed to create a dataloader batch from TimeSeriesDataSet: {e}", exc_info=True)
             pytest.fail(f"Failed to create a dataloader batch: {e}")

        logger.info("test_dataset_creation PASSED.")

    def test_model_initialization(self, tft_dataset, tft_config):
        """Test if the TFT model initializes correctly using the dataset and config dictionary."""
        logger.info("Running test_model_initialization...")

        # Check if we can run the PyTorch implementation test
        if PYTORCH_AVAILABLE and tft_dataset is not None:
            try:
                # Instantiate model using the utility function
                model = instantiate_tft_model(tft_dataset, tft_config)
                assert model is not None, "instantiate_tft_model returned None"
                assert isinstance(model, TemporalFusionTransformerModel), "Instantiated object is not a TemporalFusionTransformerModel"
                logger.info("PyTorch TFT model initialized successfully from dataset and config dictionary.")
            except Exception as e:
                logger.error(f"Error initializing PyTorch TFT model: {e}", exc_info=True)
                pytest.fail(f"PyTorch TFT model initialization failed: {e}")
        else:
            logger.info("Skipping PyTorch TFT model initialization test: PyTorch or dataset not available.")

        # Check if we can run the TensorFlow implementation test
        if TF_AVAILABLE:
            try:
                # Create a TFT model instance directly
                # Set use_pytorch to False to force TensorFlow implementation
                config_copy = tft_config.copy()
                config_copy['use_pytorch'] = False
                tf_model = TFTModel(config=config_copy)
                assert tf_model is not None, "TensorFlow TFT model initialization returned None"
                logger.info("TensorFlow TFT model initialized successfully from config dictionary.")
            except Exception as e:
                logger.error(f"Error initializing TensorFlow TFT model: {e}", exc_info=True)
                pytest.fail(f"TensorFlow TFT model initialization failed: {e}")
        else:
            logger.info("Skipping TensorFlow TFT model initialization test: TensorFlow not available.")

        # Skip the test entirely if neither implementation is available
        if not PYTORCH_AVAILABLE and not TF_AVAILABLE:
            pytest.skip("Skipping test: Neither PyTorch nor TensorFlow implementation is available.")

        logger.info("test_model_initialization PASSED.")


    def test_model_training(self, tft_dataset, tft_config):
        """Test a short training loop using config dictionary and PyTorch Lightning or TensorFlow."""
        logger.info("Running test_model_training...")

        # Skip the test entirely if neither implementation is available
        if not PYTORCH_AVAILABLE and not TF_AVAILABLE:
            pytest.skip("Skipping test: Neither PyTorch nor TensorFlow implementation is available.")

        # For PyTorch implementation, we need the Trainer and dataset
        if PYTORCH_AVAILABLE and not (Trainer is not None and tft_dataset is not None and TemporalFusionTransformerModel is not None):
            logger.info("Skipping PyTorch TFT model training test: PyTorch Lightning Trainer, TFT Model, or dataset not available.")

        # For TensorFlow implementation, we need TensorFlow
        if TF_AVAILABLE:
            logger.info("TensorFlow is available for TFT model training test.")
        else:
            logger.info("Skipping TensorFlow TFT model training test: TensorFlow not available.")

        try:
            # Create DataLoader using config dict for batch_size and num_workers
            dataloader = tft_dataset.to_dataloader(
                train=True,
                batch_size=tft_config.get('batch_size', 16),
                num_workers=tft_config.get('num_workers', 0),
                shuffle=True # Shuffle for training
                )

            # Instantiate model using config dict via utility function
            model = instantiate_tft_model(tft_dataset, tft_config)
            if model is None:
                pytest.fail("Failed to instantiate model for training.")

            # Configure trainer using config dict
            accelerator = "gpu" if tft_config.get('use_gpu', False) and torch.cuda.is_available() else "cpu"
            devices = [0] if accelerator == "gpu" else 1 # Or auto, or specific devices

            if accelerator == "gpu":
                 # Check actual device count
                 if torch.cuda.device_count() == 0:
                      logger.warning("use_gpu is True in config, but torch.cuda.device_count() is 0. Falling back to CPU.")
                      accelerator = "cpu"
                      devices = 1
                 else:
                      logger.info(f"CUDA available ({torch.cuda.get_device_name(0)}), using GPU.")
            else:
                 logger.info("Using CPU for training.")


            # Setup logger (optional but good practice)
            log_dir = Path(tft_config['model_path']).parent / "lightning_logs"
            tb_logger = TensorBoardLogger(save_dir=str(log_dir), name="tft_test_train")

            # Setup callbacks using config dict
            early_stop_callback = EarlyStopping(
                 monitor=tft_config.get("early_stopping_monitor", "train_loss"), # Monitor train loss for quick test
                 min_delta=tft_config.get("early_stopping_min_delta", 1e-4),
                 patience=tft_config.get("early_stopping_patience", 3),
                 verbose=False,
                 mode=tft_config.get("early_stopping_mode", "min")
            )
            # Checkpoint callback (optional for test, good for real training)
            checkpoint_callback = ModelCheckpoint(
                dirpath=Path(tft_config['model_path']).parent / "checkpoints",
                filename='tft_test_train_best-{epoch:02d}-{train_loss:.2f}', # Example filename
                save_top_k=1,
                verbose=True,
                monitor=tft_config.get("early_stopping_monitor", "train_loss"), # Monitor same as early stopping
                mode=tft_config.get("early_stopping_mode", "min")
            )


            trainer = Trainer(
                max_epochs=tft_config.get('max_epochs', 3), # Use config value
                accelerator=accelerator,
                devices=devices,
                gradient_clip_val=tft_config.get('gradient_clip_val', 0.1), # Use config value
                # Use fast_dev_run for a quick sanity check (runs 1 batch train/val/test)
                # fast_dev_run=True,
                # Or limit batches for a slightly longer test
                limit_train_batches=5 if not os.getenv("CI") else 1.0, # Limit batches unless in CI
                limit_val_batches=1 if not os.getenv("CI") else 1.0, # Need validation dataloader for this
                logger=tb_logger, # Use configured logger
                callbacks=[early_stop_callback, checkpoint_callback], # Add callbacks
                enable_progress_bar=False # Disable progress bar for cleaner logs in tests
            )

            logger.info(f"Starting model training for max {tft_config.get('max_epochs', 3)} epochs using {accelerator}...")
            # Train the model - Requires a validation dataloader for checkpointing/early stopping on val_loss
            # Create a dummy validation loader from the same dataset for testing purposes
            val_dataloader = tft_dataset.to_dataloader(
                 train=False, # Use validation split/data
                 batch_size=tft_config.get('batch_size', 16),
                 num_workers=tft_config.get('num_workers', 0)
                )

            trainer.fit(model, train_dataloaders=dataloader, val_dataloaders=val_dataloader)
            logger.info("Model training completed (short run).")

            # Save the final trained model state_dict using the path from config
            model_save_path = tft_config['model_path'] # This should be the final model path
            trainer.save_checkpoint(model_save_path) # Use trainer's method to save checkpoint
            # torch.save(model.state_dict(), model_save_path) # Alternatively save state_dict directly
            logger.info(f"Model checkpoint saved via trainer to {model_save_path} (or related path in checkpoint callback dir)")
            # Check if checkpoint file exists (path might be slightly different based on callback)
            assert Path(checkpoint_callback.best_model_path).exists(), f"Best model checkpoint not found at {checkpoint_callback.best_model_path}"
            # Copy best model checkpoint to the designated model_path for consistency if needed
            import shutil
            shutil.copy(checkpoint_callback.best_model_path, model_save_path)
            logger.info(f"Copied best checkpoint to final path: {model_save_path}")
            assert Path(model_save_path).exists()


        except Exception as e:
            logger.error(f"Error during model training test: {e}", exc_info=True)
            pytest.fail(f"Model training failed: {e}")
        logger.info("test_model_training PASSED.")

    def test_model_prediction(self, tft_dataset, tft_config):
        """Test model prediction using loaded model and config dictionary."""
        logger.info("Running test_model_prediction...")
        # Use the path where the best model was saved/copied in the training test
        model_path = Path(tft_config.get('model_path'))
        if not model_path or not model_path.exists():
            pytest.skip(f"Skipping prediction test: Model file not found at {model_path}. Run training test first.")
        if TemporalFusionTransformerModel is None or tft_dataset is None:
             pytest.skip("Skipping test: TemporalFusionTransformerModel or tft_dataset not available.")

        try:
             # Create a dataloader for prediction (using train=False)
             pred_dataloader = tft_dataset.to_dataloader(
                 train=False, # Use evaluation mode data
                 batch_size=tft_config.get('batch_size', 16) * 2, # Can use larger batch for inference
                 num_workers=tft_config.get('num_workers', 0)
                )

             # Determine map_location based on availability and config
             device = torch.device("cuda:0" if tft_config.get('use_gpu', False) and torch.cuda.is_available() else "cpu")

             # Load the model from the checkpoint saved by the Trainer
             # Option 1: Load using the class method (requires model class)
             try:
                  # This assumes your model class has a `load_from_checkpoint` method
                  loaded_model = TemporalFusionTransformerModel.load_from_checkpoint(
                       checkpoint_path=str(model_path),
                       map_location=device
                       # If load_from_checkpoint doesn't exist or needs dataset:
                       # you might need to instantiate first, then load state_dict (see below)
                       )
                  logger.info(f"Model loaded successfully from checkpoint {model_path} using load_from_checkpoint.")
             except AttributeError:
                  logger.warning("load_from_checkpoint not found on model class. Instantiating first, then loading state_dict.")
                  # Option 2: Instantiate first, then load state_dict
                  loaded_model = instantiate_tft_model(tft_dataset, tft_config)
                  if loaded_model is None: pytest.fail("Failed to instantiate model before loading state dict.")
                  checkpoint = torch.load(model_path, map_location=device)
                  # Load state_dict (handle potential keys like 'state_dict' from Lightning checkpoints)
                  state_dict = checkpoint.get('state_dict', checkpoint)
                   # Filter out potential unwanted keys if necessary (e.g., optimizer states)
                  # state_dict = {k.replace("model.", ""): v for k, v in state_dict.items() if k.startswith("model.")} # Example if model was wrapped
                  loaded_model.load_state_dict(state_dict)
                  logger.info(f"Model instantiated and state_dict loaded successfully from {model_path}.")


             loaded_model.to(device) # Ensure model is on the correct device
             loaded_model.eval() # Set model to evaluation mode

             logger.info(f"Model ready on {device}. Generating predictions...")

             # Use the Trainer's predict method for convenience
             trainer = Trainer(
                  accelerator=str(device).split(":")[0], # "cpu" or "gpu"
                  devices=1, #[0] if device.type == 'cuda' else 1, # Match device
                  logger=False, # Disable logging for prediction
                  enable_progress_bar=False
                 )

             # raw_predictions is a list of batches
             raw_predictions_list = trainer.predict(loaded_model, dataloaders=pred_dataloader)

             if not raw_predictions_list:
                 pytest.fail("Trainer predict method returned empty list. No predictions generated.")

             # Concatenate predictions from list of batches
             # Assuming each element in raw_predictions_list is the prediction tensor for a batch
             # Shape might be (batch, time, quantiles) or (batch, time)
             try:
                  raw_predictions = torch.cat(raw_predictions_list, dim=0)
             except Exception as cat_err:
                  logger.error(f"Error concatenating prediction tensors: {cat_err}. Prediction list content: {raw_predictions_list}")
                  pytest.fail("Failed to concatenate prediction tensors.")


             # Check prediction format
             assert raw_predictions is not None, "Concatenated predictions are None"
             assert isinstance(raw_predictions, torch.Tensor), "Predictions are not a torch Tensor"
             # Check shape consistency: (num_samples, prediction_length, num_targets or quantiles)
             expected_pred_len = tft_config.get('max_prediction_length')
             assert raw_predictions.shape[1] == expected_pred_len, f"Expected prediction length {expected_pred_len}, got {raw_predictions.shape[1]}"
             # Shape[0] should ideally match number of samples in prediction dataset slice
             # num_samples_in_pred_data = len(pred_dataloader.dataset) # This might not be exact due to batching/dropping last
             # logger.info(f"Number of samples in prediction dataloader dataset: {num_samples_in_pred_data}")
             logger.info(f"Prediction successful. Shape of concatenated prediction tensor: {raw_predictions.shape}")

        except FileNotFoundError:
             pytest.fail(f"Model file not found at {model_path}")
        except Exception as e:
             logger.error(f"Error during model prediction test: {e}", exc_info=True)
             pytest.fail(f"Model prediction failed: {e}")
        logger.info("test_model_prediction PASSED.")

    def test_model_evaluation(self, tft_dataset, tft_config):
        """Test model evaluation using standard metrics, loaded model, and config dictionary."""
        logger.info("Running test_model_evaluation...")
        model_path = Path(tft_config.get('model_path'))
        if not model_path or not model_path.exists():
            pytest.skip(f"Skipping evaluation: Model file not found at {model_path}. Run training test first.")
        if TemporalFusionTransformerModel is None or tft_dataset is None or calculate_metrics is None:
             pytest.skip("Skipping test: Model, dataset, or calculate_metrics function not available.")

        try:
            # Create DataLoader for evaluation
            eval_dataloader = tft_dataset.to_dataloader(
                train=False, # Evaluation mode data
                batch_size=tft_config.get('batch_size', 16),
                num_workers=tft_config.get('num_workers', 0)
                )

            # Load model (similar to prediction test)
            device = torch.device("cuda:0" if tft_config.get('use_gpu', False) and torch.cuda.is_available() else "cpu")
            try:
                model = TemporalFusionTransformerModel.load_from_checkpoint(str(model_path), map_location=device)
                logger.info("Loaded model via load_from_checkpoint for evaluation.")
            except AttributeError:
                logger.warning("load_from_checkpoint not found. Instantiating model first.")
                model = instantiate_tft_model(tft_dataset, tft_config)
                if model is None: pytest.fail("Failed to instantiate model for evaluation.")
                checkpoint = torch.load(model_path, map_location=device)
                state_dict = checkpoint.get('state_dict', checkpoint)
                model.load_state_dict(state_dict)
                logger.info("Instantiated model and loaded state_dict for evaluation.")

            model.to(device)
            model.eval()

            all_predictions_processed = []
            all_actuals_processed = []

            with torch.no_grad():
                for batch in eval_dataloader:
                    x, y = batch # y contains tuple (target, weight)
                    x = {k: v.to(device) for k, v in x.items() if isinstance(v, torch.Tensor)} # Move input dict tensors to device
                    actuals_raw = y[0].to(device) # Shape (batch, pred_len, num_targets=1) - Move targets too

                    # Get model predictions (raw output tensor or dict)
                    predictions_raw = model(x) # Output could be tensor or dict

                    # --- Process raw predictions ---
                    # Handle dict output from model if necessary
                    if isinstance(predictions_raw, dict):
                         # Try common keys, adjust based on your model's actual output
                         pred_tensor = predictions_raw.get('prediction', predictions_raw.get('output'))
                         if pred_tensor is None:
                              logger.error(f"Prediction tensor not found in model output dict keys: {predictions_raw.keys()}")
                              pytest.fail("Cannot find prediction tensor in model output dict.")
                         predictions_raw = pred_tensor

                    # --- Handle Quantiles (if using QuantileLoss) ---
                    # Assuming predictions_raw shape is (batch, time, quantiles)
                    # And actuals_raw shape is (batch, time, 1)
                    if predictions_raw.ndim == 3 and actuals_raw.ndim == 3 and predictions_raw.shape[-1] > 1:
                         # Evaluate median forecast (P50) against actuals
                         median_quantile_index = predictions_raw.shape[-1] // 2
                         predictions_processed = predictions_raw[:, :, median_quantile_index] # Select P50 forecast -> shape (batch, time)
                    elif predictions_raw.ndim == 2 and actuals_raw.ndim == 3: # Handle case where model outputs (batch, time) directly
                         predictions_processed = predictions_raw
                    elif predictions_raw.ndim == 3 and actuals_raw.ndim == 3 and predictions_raw.shape[-1] == 1: # Handles (batch, time, 1) output
                         predictions_processed = predictions_raw.squeeze(-1)
                    else:
                         logger.warning(f"Unexpected prediction/actual shape combination. Pred shape: {predictions_raw.shape}, Actual shape: {actuals_raw.shape}. Skipping metric processing for this batch if shapes mismatch after squeeze.")
                         # Attempt squeeze anyway, check shapes below
                         try:
                            predictions_processed = predictions_raw.squeeze()
                         except Exception:
                            continue # Skip batch if squeeze fails


                    # --- Prepare Actuals ---
                    # Squeeze the target dimension (usually 1) -> shape (batch, time)
                    actuals_processed = actuals_raw.squeeze(-1)

                    # --- Check shapes and append ---
                    if predictions_processed.shape == actuals_processed.shape:
                        all_predictions_processed.append(predictions_processed.cpu().numpy()) # Move to CPU, convert to numpy
                        all_actuals_processed.append(actuals_processed.cpu().numpy())
                    else:
                         logger.warning(f"Shape mismatch after processing: Actuals {actuals_processed.shape}, Predictions {predictions_processed.shape}. Skipping batch.")


            if not all_predictions_processed or not all_actuals_processed:
                pytest.fail("Could not gather valid predictions/actuals for evaluation. Check model output, processing logic, and shapes.")

            # Concatenate results from all batches
            predictions_np = np.concatenate(all_predictions_processed, axis=0)
            actuals_np = np.concatenate(all_actuals_processed, axis=0)

            # Flatten for standard metric calculation (metrics often expect 1D arrays)
            predictions_flat = predictions_np.flatten()
            actuals_flat = actuals_np.flatten()

            # Check for NaNs/Infs before calculating metrics
            if np.isnan(predictions_flat).any() or np.isinf(predictions_flat).any():
                logger.warning(f"NaN or Inf found in final predictions array. Count NaN: {np.isnan(predictions_flat).sum()}, Count Inf: {np.isinf(predictions_flat).sum()}")
                # Option: Impute or fail
                # predictions_flat = np.nan_to_num(predictions_flat, nan=0.0, posinf=1e9, neginf=-1e9) # Example imputation
                pytest.fail("NaN/Inf found in predictions, cannot calculate metrics reliably.")
            if np.isnan(actuals_flat).any() or np.isinf(actuals_flat).any():
                 logger.warning(f"NaN or Inf found in final actuals array. Count NaN: {np.isnan(actuals_flat).sum()}, Count Inf: {np.isinf(actuals_flat).sum()}")
                 pytest.fail("NaN/Inf found in actuals, cannot calculate metrics reliably.")


            # Calculate metrics using the utility function
            logger.info(f"Calculating metrics for {len(actuals_flat)} data points...")
            metrics = calculate_metrics(actuals_flat, predictions_flat)

            assert isinstance(metrics, dict), "calculate_metrics should return a dictionary"
            # Check standard metrics
            for key in ['mae', 'rmse', 'r2']: #'mape' can be problematic if actuals are near zero
                assert key in metrics, f"Metric '{key}' missing from results"
                assert not np.isnan(metrics[key]), f"Metric '{key}' is NaN"
                assert not np.isinf(metrics[key]), f"Metric '{key}' is Infinite"
                logger.info(f"Metric {key}: {metrics[key]:.4f}")


        except FileNotFoundError:
             pytest.fail(f"Model file not found at {model_path}")
        except ImportError as e:
             logger.error(f"ImportError during evaluation: {e}", exc_info=True)
             pytest.fail(f"Missing dependency for evaluation (e.g., scikit-learn, or specific metric function?): {e}")
        except Exception as e:
            logger.error(f"Error during model evaluation test: {e}", exc_info=True)
            pytest.fail(f"Model evaluation failed: {e}")
        logger.info("test_model_evaluation PASSED.")

# --- Optional Cleanup ---
# (Use pytest's tmp_path fixture for better temporary file handling if possible)
# def teardown_class(cls):
#     logger.info("Running teardown for TestTFTModel class...")
#     # Clean up generated files/directories if necessary and safe
#     config_for_cleanup = tft_config(configuration_manager()) # Need config to know paths
#     model_path = Path(config_for_cleanup.get('model_path'))
#     test_output_dir = model_path.parent
#     test_data_file = project_root / "tests" / "test_data" / "sample_tft_data.csv"
#     lightning_logs = test_output_dir / "lightning_logs"
#     checkpoints_dir = test_output_dir / "checkpoints"

#     files_dirs_to_remove = [model_path, test_data_file, lightning_logs, checkpoints_dir, test_output_dir]

#     for item in files_dirs_to_remove:
#         try:
#             if item.is_file():
#                 item.unlink()
#                 logger.info(f"Removed file: {item}")
#             elif item.is_dir():
#                 shutil.rmtree(item)
#                 logger.info(f"Removed directory: {item}")
#         except FileNotFoundError:
#              logger.warning(f"Cleanup item not found: {item}")
#         except Exception as e:
#              logger.error(f"Error removing {item} during cleanup: {e}")