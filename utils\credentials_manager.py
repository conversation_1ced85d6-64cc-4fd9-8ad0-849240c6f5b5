"""
Secure credentials manager for MT5 terminals.
Handles credential storage, validation, and access control.
"""

import os
import json
import hashlib
import base64
from pathlib import Path
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class CredentialsManager:
    def __init__(self, config_path: str = "config/credentials.py"):
        self.config_path = Path(config_path)
        self.encryption_key = None
        self._load_encryption_key()

    def _load_encryption_key(self) -> None:
        """Load or create encryption key."""
        key_path = Path("config/.encryption_key")
        if key_path.exists():
            with open(key_path, "rb") as f:
                self.encryption_key = f.read()
        else:
            self.encryption_key = Fernet.generate_key()
            with open(key_path, "wb") as f:
                f.write(self.encryption_key)
            # Add to .gitignore
            with open('.gitignore', 'a+') as f:
                f.seek(0)
                content = f.read()
                if 'config/.encryption_key' not in content:
                    f.write('\n# Encryption key\nconfig/.encryption_key\n')

    def _encrypt_value(self, value: str) -> str:
        """Encrypt a value using Fernet."""
        f = Fernet(self.encryption_key)
        return f.encrypt(value.encode()).decode()

    def _decrypt_value(self, encrypted_value: str) -> str:
        """Decrypt a value using Fernet."""
        f = Fernet(self.encryption_key)
        return f.decrypt(encrypted_value.encode()).decode()

    def validate_credentials(self, credentials: Dict[str, Any]) -> bool:
        """Validate credential format and security requirements."""
        required_fields = ["login", "password", "server", "path"]

        for field in required_fields:
            if field not in credentials or not credentials[field]:
                return False

        # Validate password strength
        if len(credentials["password"]) < 8:
            return False

        return True

    def save_credentials(self, credentials: Dict[int, Dict[str, Any]]) -> bool:
        """Save encrypted credentials to file."""
        try:
            # Encrypt sensitive data
            encrypted_credentials = {}
            for terminal_id, config in credentials.items():
                encrypted_credentials[terminal_id] = {
                    "login": self._encrypt_value(config["login"]),
                    "password": self._encrypt_value(config["password"]),
                    "server": self._encrypt_value(config["server"]),
                    "path": config["path"]  # Path doesn't need encryption
                }

            # Save to file
            with open(self.config_path, "w") as f:
                json.dump(encrypted_credentials, f, indent=4)

            return True
        except Exception as e:
            print(f"Error saving credentials: {str(e)}")
            return False

    def load_credentials(self) -> Optional[Dict[str, Dict[str, Any]]]:
        """Load and decrypt credentials from file."""
        try:
            if not self.config_path.exists():
                return None

            with open(self.config_path, "r") as f:
                encrypted_credentials = json.load(f)

            # Decrypt credentials
            credentials = {}
            for terminal_id, config in encrypted_credentials.items():
                # Use string terminal_id for consistency
                credentials[str(terminal_id)] = {
                    "login": self._decrypt_value(config["login"]),
                    "password": self._decrypt_value(config["password"]),
                    "server": self._decrypt_value(config["server"]),
                    "path": config["path"]
                }

            return credentials
        except Exception as e:
            print(f"Error loading credentials: {str(e)}")
            return None

    def backup_credentials(self) -> bool:
        """Create a backup of credentials."""
        try:
            backup_path = self.config_path.with_suffix('.bak')
            if self.config_path.exists():
                with open(self.config_path, 'r') as src, open(backup_path, 'w') as dst:
                    dst.write(src.read())
                return True
            return False
        except Exception as e:
            print(f"Error creating backup: {str(e)}")
            return False