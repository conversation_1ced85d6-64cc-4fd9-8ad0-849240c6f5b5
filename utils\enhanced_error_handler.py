"""
Enhanced <PERSON>rro<PERSON> - Improved error handling and recovery system for the trading bot.
Provides comprehensive error logging, tracking, recovery mechanisms, and circuit breaker integration.
"""

import logging
import traceback
import sys
import json
import uuid
import psutil
import platform
from typing import Dict, List, Callable, Any, Optional, TypeVar, Type, Union, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
import threading
from collections import defaultdict, Counter, deque
from enum import Enum
import time
import random

from utils.circuit_breaker import CircuitState, CircuitOpenError, CircuitBreaker

logger = logging.getLogger(__name__)

# Type definitions
ErrorCallback = Callable[[Exception, Dict[str, Any]], Any]
RecoveryHandler = Callable[[Exception, Dict[str, Any]], bool]
T = TypeVar('T')  # Generic type for error context

class ErrorCategory(Enum):
    """Categories of errors for better handling and statistics."""
    NETWORK = "network"  # Network-related errors
    AUTHENTICATION = "auth"  # Authentication failures
    DATA = "data"  # Data integrity/parsing issues
    EXECUTION = "execution"  # Trade execution problems
    RESOURCE = "resource"  # Resource exhaustion (memory, CPU)
    MT5_API = "mt5_api"  # MT5-specific API errors
    MODEL = "model"  # ML model errors
    CONFIG = "config"  # Configuration errors
    SYSTEM = "system"  # System-level errors
    DATABASE = "database"  # Database-related errors
    TIMEOUT = "timeout"  # Timeout errors
    VALIDATION = "validation"  # Validation errors
    UNKNOWN = "unknown"  # Uncategorized errors

class ErrorSeverity(Enum):
    """Severity levels for errors."""
    INFO = "INFO"  # Informational, not critical
    WARNING = "WARNING"  # Warning, might need attention
    ERROR = "ERROR"  # Error, needs attention
    CRITICAL = "CRITICAL"  # Critical, needs immediate attention
    FATAL = "FATAL"  # Fatal, system cannot continue

@dataclass
class SystemState:
    """System state information for error context."""
    memory_percent: float = 0.0
    cpu_percent: float = 0.0
    disk_usage_percent: float = 0.0
    open_file_descriptors: int = 0
    thread_count: int = 0
    process_count: int = 0
    uptime_seconds: float = 0.0
    python_version: str = ""
    platform_info: str = ""

    @classmethod
    def capture_current(cls) -> 'SystemState':
        """Capture the current system state."""
        try:
            process = psutil.Process()
            return cls(
                memory_percent=psutil.virtual_memory().percent,
                cpu_percent=psutil.cpu_percent(interval=0.1),
                disk_usage_percent=psutil.disk_usage('/').percent,
                open_file_descriptors=len(process.open_files()),
                thread_count=process.num_threads(),
                process_count=len(psutil.pids()),
                uptime_seconds=time.time() - psutil.boot_time(),
                python_version=sys.version,
                platform_info=platform.platform()
            )
        except Exception as e:
            logger.warning(f"Failed to capture system state: {str(e)}")
            return cls()

@dataclass
class ErrorInfo:
    """Enhanced information about an error occurrence."""
    error_type: str  # Type of the error/exception
    error_message: str  # Error message
    timestamp: datetime = field(default_factory=datetime.now)  # When the error occurred
    traceback: str = None  # Full traceback of the error
    context: Dict[str, Any] = field(default_factory=dict)  # Additional context
    recovery_attempted: bool = False  # Whether recovery was attempted
    recovery_succeeded: bool = False  # Whether recovery succeeded
    error_id: str = field(default_factory=lambda: str(uuid.uuid4()))  # Unique ID for this error instance
    source: str = None  # Source of the error (module, function, etc.)
    severity: ErrorSeverity = ErrorSeverity.ERROR  # Severity level
    category: ErrorCategory = ErrorCategory.UNKNOWN  # Error category for better classification
    system_state: SystemState = field(default_factory=SystemState.capture_current)  # System state when error occurred
    related_errors: List[str] = field(default_factory=list)  # IDs of related errors
    retry_count: int = 0  # Number of retries attempted
    max_retries: int = 3  # Maximum number of retries allowed
    circuit_breaker_name: str = None  # Name of the circuit breaker if applicable

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = asdict(self)
        # Convert enums to strings
        result['severity'] = self.severity.value
        result['category'] = self.category.value
        # Convert datetime to ISO format
        result['timestamp'] = self.timestamp.isoformat()
        return result

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=2)

    @classmethod
    def from_exception(cls, exception: Exception, context: Dict[str, Any] = None,
                      source: str = None, severity: ErrorSeverity = ErrorSeverity.ERROR,
                      category: ErrorCategory = None) -> 'ErrorInfo':
        """Create an ErrorInfo instance from an exception."""
        error_type = type(exception).__name__
        error_message = str(exception)
        tb = traceback.format_exc()

        # Determine error category if not provided
        if category is None:
            category = cls._categorize_error(exception)

        return cls(
            error_type=error_type,
            error_message=error_message,
            traceback=tb,
            context=context or {},
            source=source,
            severity=severity,
            category=category
        )

    @staticmethod
    def _categorize_error(exception: Exception) -> ErrorCategory:
        """Categorize an error based on its type."""
        error_type = type(exception).__name__
        error_message = str(exception).lower()

        # Network errors
        if any(term in error_type.lower() for term in ['timeout', 'connection', 'network', 'socket']):
            return ErrorCategory.NETWORK
        if any(term in error_message for term in ['timeout', 'connection refused', 'network', 'unreachable']):
            return ErrorCategory.NETWORK

        # Authentication errors
        if any(term in error_type.lower() for term in ['auth', 'login', 'credential']):
            return ErrorCategory.AUTHENTICATION
        if any(term in error_message for term in ['authentication', 'login', 'password', 'credential']):
            return ErrorCategory.AUTHENTICATION

        # Data errors
        if any(term in error_type.lower() for term in ['value', 'type', 'key', 'index', 'attribute']):
            return ErrorCategory.DATA
        if any(term in error_message for term in ['invalid', 'missing', 'not found', 'null', 'undefined']):
            return ErrorCategory.DATA

        # Resource errors
        if any(term in error_type.lower() for term in ['memory', 'resource', 'overflow']):
            return ErrorCategory.RESOURCE
        if any(term in error_message for term in ['memory', 'resource', 'disk space', 'cpu']):
            return ErrorCategory.RESOURCE

        # MT5 API errors
        if 'mt5' in error_type.lower() or 'metatrader' in error_type.lower():
            return ErrorCategory.MT5_API
        if 'mt5' in error_message or 'metatrader' in error_message:
            return ErrorCategory.MT5_API

        # Model errors
        if any(term in error_type.lower() for term in ['model', 'predict', 'train']):
            return ErrorCategory.MODEL
        if any(term in error_message for term in ['model', 'prediction', 'training', 'inference']):
            return ErrorCategory.MODEL

        # Config errors
        if 'config' in error_type.lower():
            return ErrorCategory.CONFIG
        if 'configuration' in error_message or 'config' in error_message:
            return ErrorCategory.CONFIG

        # Timeout errors
        if 'timeout' in error_type.lower():
            return ErrorCategory.TIMEOUT
        if 'timeout' in error_message or 'timed out' in error_message:
            return ErrorCategory.TIMEOUT

        # Default to unknown
        return ErrorCategory.UNKNOWN

class EnhancedErrorHandler:
    """
    Enhanced error handler with improved recovery strategies and circuit breaker integration.
    Provides centralized error handling, tracking, and recovery mechanisms.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(EnhancedErrorHandler, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the error handler."""
        if self._initialized:
            return

        # Error tracking
        self._errors: Dict[str, ErrorInfo] = {}
        self._error_counts: Counter = Counter()
        self._category_counts: Counter = Counter()
        self._recent_errors: deque = deque(maxlen=100)

        # Recovery handlers
        self._recovery_handlers: Dict[Type[Exception], List[RecoveryHandler]] = defaultdict(list)
        self._category_recovery_handlers: Dict[ErrorCategory, List[RecoveryHandler]] = defaultdict(list)
        self._global_recovery_handlers: List[RecoveryHandler] = []

        # Error callbacks
        self._error_callbacks: Dict[Type[Exception], List[ErrorCallback]] = defaultdict(list)
        self._category_callbacks: Dict[ErrorCategory, List[ErrorCallback]] = defaultdict(list)
        self._global_callbacks: List[ErrorCallback] = []

        # Circuit breaker registry
        self._circuit_breakers = {}

        # Thread safety
        self._lock = threading.RLock()

        # Register default recovery handlers
        self._register_default_recovery_handlers()

        self._initialized = True
        logger.info("Enhanced error handler initialized")

    def _register_default_recovery_handlers(self):
        """Register default recovery handlers for common error types."""
        # Network errors
        self.register_category_recovery_handler(
            ErrorCategory.NETWORK,
            self._network_error_recovery_handler
        )

        # Authentication errors
        self.register_category_recovery_handler(
            ErrorCategory.AUTHENTICATION,
            self._auth_error_recovery_handler
        )

        # MT5 API errors
        self.register_category_recovery_handler(
            ErrorCategory.MT5_API,
            self._mt5_api_error_recovery_handler
        )

        # Resource errors
        self.register_category_recovery_handler(
            ErrorCategory.RESOURCE,
            self._resource_error_recovery_handler
        )

        # Timeout errors
        self.register_category_recovery_handler(
            ErrorCategory.TIMEOUT,
            self._timeout_error_recovery_handler
        )

    def _network_error_recovery_handler(self, exception: Exception, context: Dict[str, Any]) -> bool:
        """Recovery handler for network errors."""
        logger.info(f"Attempting to recover from network error: {str(exception)}")

        # Implement exponential backoff
        retry_count = context.get('retry_count', 0)
        max_retries = context.get('max_retries', 3)

        if retry_count >= max_retries:
            logger.warning(f"Max retries ({max_retries}) exceeded for network error")
            return False

        # Calculate backoff time with jitter
        backoff_seconds = min(30, (2 ** retry_count)) + random.uniform(0, 1)
        logger.info(f"Backing off for {backoff_seconds:.2f} seconds before retry {retry_count + 1}/{max_retries}")

        # Sleep for backoff time
        time.sleep(backoff_seconds)

        # Update retry count in context
        context['retry_count'] = retry_count + 1

        return True

    def _auth_error_recovery_handler(self, exception: Exception, context: Dict[str, Any]) -> bool:
        """Recovery handler for authentication errors."""
        logger.info(f"Attempting to recover from authentication error: {str(exception)}")

        # Check if we have retry credentials
        if 'retry_credentials' in context:
            logger.info("Using alternative credentials for authentication")
            # The actual credential switching would be handled by the caller
            return True

        # No recovery possible
        logger.warning("No alternative credentials available for authentication recovery")
        return False

    def _mt5_api_error_recovery_handler(self, exception: Exception, context: Dict[str, Any]) -> bool:
        """Recovery handler for MT5 API errors."""
        logger.info(f"Attempting to recover from MT5 API error: {str(exception)}")

        # Implement retry logic for MT5 API errors
        retry_count = context.get('retry_count', 0)
        max_retries = context.get('max_retries', 3)

        if retry_count >= max_retries:
            logger.warning(f"Max retries ({max_retries}) exceeded for MT5 API error")
            return False

        # Add backoff delay
        backoff_seconds = min(10, (1.5 ** retry_count)) + random.uniform(0, 0.5)
        logger.info(f"Backing off for {backoff_seconds:.2f} seconds before retry {retry_count + 1}/{max_retries}")
        time.sleep(backoff_seconds)

        # Update retry count in context
        context['retry_count'] = retry_count + 1

        # Check if we need to reconnect
        if 'mt5_manager' in context and 'terminal_id' in context:
            mt5_manager = context['mt5_manager']
            terminal_id = context['terminal_id']

            # Normalize terminal ID for consistency
            try:
                from utils.common import normalize_terminal_id
                terminal_id = normalize_terminal_id(terminal_id)
            except ImportError:
                # Fallback to simple string conversion if common utils not available
                terminal_id = str(terminal_id)

            logger.info(f"Attempting to reconnect to MT5 terminal {terminal_id}")
            try:
                # Attempt reconnection using standardized method
                success = mt5_manager.reconnect(terminal_id)
                if success:
                    logger.info(f"Successfully reconnected to MT5 terminal {terminal_id}")
                    return True
                else:
                    logger.warning(f"Failed to reconnect to MT5 terminal {terminal_id}")
            except Exception as e:
                logger.error(f"Error during MT5 reconnection: {str(e)}")
                # Continue with other recovery methods

        # Try to use MT5ConnectionManager if available
        terminal_id = context.get('terminal_id')
        if terminal_id is not None:
            try:
                # Normalize terminal ID for consistency
                try:
                    from utils.common import normalize_terminal_id
                    terminal_id = normalize_terminal_id(terminal_id)
                except ImportError:
                    # Fallback to simple string conversion if common utils not available
                    terminal_id = str(terminal_id)

                # Import MT5ConnectionManager
                from trading.mt5_connection_manager import MT5ConnectionManager

                # Try to get the global instance from main.py
                try:
                    import main
                    if hasattr(main, 'mt5_manager'):
                        manager = main.mt5_manager
                        logger.info(f"Using global MT5ConnectionManager to reconnect to terminal {terminal_id}")
                        success = manager.reconnect(terminal_id)
                        if success:
                            logger.info(f"Successfully reconnected to MT5 terminal {terminal_id}")
                            return True
                        else:
                            logger.warning(f"Failed to reconnect to MT5 terminal {terminal_id} using global manager")
                except (ImportError, AttributeError):
                    logger.warning("Global MT5ConnectionManager not available")

                # Try to use circuit breaker for MT5 connection
                try:
                    from utils.enhanced_circuit_breaker import get_circuit_breaker
                    circuit_breaker = get_circuit_breaker('mt5_connection')
                    if circuit_breaker:
                        logger.info(f"Using circuit breaker for MT5 connection recovery")
                        # Reset the circuit breaker to allow reconnection attempt
                        circuit_breaker.reset()
                except (ImportError, AttributeError):
                    logger.warning("Circuit breaker not available for MT5 connection recovery")

                # Try to use MT5 module directly as a last resort
                try:
                    import MetaTrader5 as mt5
                    if not mt5.initialize():
                        logger.warning(f"Failed to initialize MT5: {mt5.last_error()}")
                        return False
                    logger.info("Successfully initialized MT5")
                    return True
                except ImportError:
                    logger.warning("MetaTrader5 module not available for direct initialization")
                except Exception as e:
                    logger.error(f"Error initializing MT5 directly: {str(e)}")

            except ImportError:
                logger.warning("MT5ConnectionManager not available for reconnection")
            except Exception as e:
                logger.error(f"Error using MT5ConnectionManager: {str(e)}")

        # No recovery possible
        logger.warning("No MT5 manager or terminal ID available for MT5 API error recovery")
        return False

    def _resource_error_recovery_handler(self, exception: Exception, context: Dict[str, Any]) -> bool:
        """Recovery handler for resource errors."""
        logger.info(f"Attempting to recover from resource error: {str(exception)}")

        # Check if we need to free memory
        if 'memory_manager' in context:
            memory_manager = context['memory_manager']

            logger.info("Attempting to free memory using provided memory manager")
            try:
                # Attempt to free memory using the provided memory manager
                if hasattr(memory_manager, 'cleanup_memory'):
                    memory_manager.cleanup_memory("AGGRESSIVE")
                    return True
                elif hasattr(memory_manager, 'free_memory'):
                    memory_manager.free_memory()
                    return True
                else:
                    logger.warning("Provided memory manager has no cleanup_memory or free_memory method")
            except Exception as e:
                logger.error(f"Error during memory cleanup with provided manager: {str(e)}")
                # Continue with other recovery methods

        # Try to use the global enhanced memory manager
        try:
            from utils.enhanced_memory_manager import enhanced_memory_manager
            logger.info("Attempting to free memory using global enhanced memory manager")
            enhanced_memory_manager.cleanup_memory("AGGRESSIVE")
            return True
        except (ImportError, AttributeError):
            logger.warning("Enhanced memory manager not available")
        except Exception as e:
            logger.error(f"Error during memory cleanup with enhanced memory manager: {str(e)}")

        # Try to use the memory manager from main.py
        try:
            import main
            if hasattr(main, 'memory_manager'):
                logger.info("Attempting to free memory using memory manager from main")
                main.memory_manager.cleanup_memory("AGGRESSIVE")
                return True
        except (ImportError, AttributeError):
            logger.warning("Memory manager from main not available")
        except Exception as e:
            logger.error(f"Error during memory cleanup with main memory manager: {str(e)}")

        # Last resort: force garbage collection
        try:
            import gc
            logger.info("Forcing garbage collection")
            gc.collect()
            return True
        except Exception as e:
            logger.error(f"Error during forced garbage collection: {str(e)}")

        # No recovery possible
        logger.warning("No memory manager available for resource error recovery")
        return False

    def _timeout_error_recovery_handler(self, exception: Exception, context: Dict[str, Any]) -> bool:
        """Recovery handler for timeout errors."""
        logger.info(f"Attempting to recover from timeout error: {str(exception)}")

        # Implement exponential backoff with longer delays for timeouts
        retry_count = context.get('retry_count', 0)
        max_retries = context.get('max_retries', 5)  # Increased max retries for timeouts

        if retry_count >= max_retries:
            logger.warning(f"Max retries ({max_retries}) exceeded for timeout error")
            return False

        # Calculate backoff time with jitter (longer for timeouts)
        backoff_seconds = min(60, (4 ** retry_count)) + random.uniform(0, 2)
        logger.info(f"Backing off for {backoff_seconds:.2f} seconds before retry {retry_count + 1}/{max_retries}")

        # Sleep for backoff time
        time.sleep(backoff_seconds)

        # Update retry count in context
        context['retry_count'] = retry_count + 1

        # Check if this is a network timeout
        error_message = str(exception).lower()
        if any(term in error_message for term in ['socket timeout', 'connection timeout', 'read timeout']):
            logger.info("Detected network timeout, checking connectivity")

            # Try to check internet connectivity
            try:
                import socket
                socket.create_connection(("8.8.8.8", 53), timeout=5)
                logger.info("Internet connectivity check passed")
            except Exception as e:
                logger.warning(f"Internet connectivity check failed: {str(e)}")
                # Wait longer if connectivity issues detected
                time.sleep(5)

        # Check if this is an MT5 timeout
        if 'mt5' in error_message or 'metatrader' in error_message:
            logger.info("Detected MT5 timeout, attempting to refresh connection")

            # Try to refresh MT5 connection if terminal_id is available
            terminal_id = context.get('terminal_id')
            if terminal_id is not None:
                try:
                    from trading.mt5_connection_manager import MT5ConnectionManager
                    if hasattr(MT5ConnectionManager, 'get_instance'):
                        manager = MT5ConnectionManager.get_instance()
                        if manager:
                            logger.info(f"Refreshing MT5 connection for terminal {terminal_id}")
                            # Use reconnect method instead of refresh_connection which doesn't exist
                            manager.reconnect(terminal_id)
                except Exception as e:
                    logger.error(f"Error refreshing MT5 connection: {str(e)}")

        return True

    def handle_error(self, exception: Exception, context: Dict[str, Any] = None,
                    source: str = None, severity: ErrorSeverity = ErrorSeverity.ERROR,
                    category: ErrorCategory = None, circuit_breaker_name: str = None) -> ErrorInfo:
        """
        Handle an error with proper logging, tracking, and recovery.

        Args:
            exception: The exception to handle
            context: Additional context information
            source: Source of the error (module, function, etc.)
            severity: Severity level of the error
            category: Category of the error
            circuit_breaker_name: Name of the circuit breaker to use

        Returns:
            ErrorInfo: Information about the error
        """
        # Create error info
        context = context or {}
        error_info = ErrorInfo.from_exception(
            exception=exception,
            context=context,
            source=source,
            severity=severity,
            category=category
        )
        error_info.circuit_breaker_name = circuit_breaker_name

        # Update circuit breaker if provided
        if circuit_breaker_name:
            try:
                circuit_breaker = self._get_or_create_circuit_breaker(circuit_breaker_name)
                # Use the appropriate method based on the CircuitBreaker implementation
                if hasattr(circuit_breaker, 'record_failure'):
                    circuit_breaker.record_failure()
                elif hasattr(circuit_breaker, 'on_failure'):
                    circuit_breaker.on_failure()
            except Exception as cb_error:
                logger.warning(f"Error updating circuit breaker: {str(cb_error)}")

        # Log the error
        log_method = logger.error
        if severity == ErrorSeverity.CRITICAL:
            log_method = logger.critical
        elif severity == ErrorSeverity.WARNING:
            log_method = logger.warning
        elif severity == ErrorSeverity.INFO:
            log_method = logger.info

        log_method(f"Error [{error_info.error_id}] {error_info.error_type}: {error_info.error_message} "
                  f"(source: {source}, category: {error_info.category.value})")

        if error_info.traceback:
            logger.debug(f"Traceback for error [{error_info.error_id}]:\n{error_info.traceback}")

        # Track the error
        with self._lock:
            self._errors[error_info.error_id] = error_info
            self._error_counts[error_info.error_type] += 1
            self._category_counts[error_info.category.value] += 1
            self._recent_errors.append(error_info)

        # Execute callbacks
        self._execute_callbacks(exception, context, error_info)

        # Attempt recovery
        recovery_succeeded = self._attempt_recovery(exception, context, error_info)
        error_info.recovery_attempted = True
        error_info.recovery_succeeded = recovery_succeeded

        return error_info

    def _execute_callbacks(self, exception: Exception, context: Dict[str, Any], error_info: ErrorInfo) -> None:
        """
        Execute callbacks for an error.

        Args:
            exception: The exception that occurred
            context: Additional context information
            error_info: Information about the error
        """
        # Get exception type
        exception_type = type(exception)

        # Execute type-specific callbacks
        for callback in self._error_callbacks.get(exception_type, []):
            try:
                callback(exception, context)
            except Exception as e:
                logger.error(f"Error in error callback: {str(e)}")

        # Execute category-specific callbacks
        for callback in self._category_callbacks.get(error_info.category, []):
            try:
                callback(exception, context)
            except Exception as e:
                logger.error(f"Error in category callback: {str(e)}")

        # Execute global callbacks
        for callback in self._global_callbacks:
            try:
                callback(exception, context)
            except Exception as e:
                logger.error(f"Error in global callback: {str(e)}")

    def _attempt_recovery(self, exception: Exception, context: Dict[str, Any], error_info: ErrorInfo) -> bool:
        """
        Attempt to recover from an error.

        Args:
            exception: The exception that occurred
            context: Additional context information
            error_info: Information about the error

        Returns:
            bool: True if recovery succeeded, False otherwise
        """
        # Get exception type
        exception_type = type(exception)

        # Try type-specific recovery handlers
        for handler in self._recovery_handlers.get(exception_type, []):
            try:
                if handler(exception, context):
                    logger.info(f"Recovery succeeded for error [{error_info.error_id}] using type-specific handler")
                    return True
            except Exception as e:
                logger.error(f"Error in recovery handler: {str(e)}")

        # Try category-specific recovery handlers
        for handler in self._category_recovery_handlers.get(error_info.category, []):
            try:
                if handler(exception, context):
                    logger.info(f"Recovery succeeded for error [{error_info.error_id}] using category-specific handler")
                    return True
            except Exception as e:
                logger.error(f"Error in category recovery handler: {str(e)}")

        # Try global recovery handlers
        for handler in self._global_recovery_handlers:
            try:
                if handler(exception, context):
                    logger.info(f"Recovery succeeded for error [{error_info.error_id}] using global handler")
                    return True
            except Exception as e:
                logger.error(f"Error in global recovery handler: {str(e)}")

        logger.warning(f"Recovery failed for error [{error_info.error_id}]")
        return False

    def register_recovery_handler(self, exception_type: Type[Exception], handler: RecoveryHandler) -> None:
        """
        Register a recovery handler for a specific exception type.

        Args:
            exception_type: Type of exception to handle
            handler: Recovery handler function
        """
        with self._lock:
            self._recovery_handlers[exception_type].append(handler)

    def register_category_recovery_handler(self, category: ErrorCategory, handler: RecoveryHandler) -> None:
        """
        Register a recovery handler for a specific error category.

        Args:
            category: Error category to handle
            handler: Recovery handler function
        """
        with self._lock:
            self._category_recovery_handlers[category].append(handler)

    def register_global_recovery_handler(self, handler: RecoveryHandler) -> None:
        """
        Register a global recovery handler.

        Args:
            handler: Recovery handler function
        """
        with self._lock:
            self._global_recovery_handlers.append(handler)

    def register_error_callback(self, exception_type: Type[Exception], callback: ErrorCallback) -> None:
        """
        Register a callback for a specific exception type.

        Args:
            exception_type: Type of exception to handle
            callback: Callback function
        """
        with self._lock:
            self._error_callbacks[exception_type].append(callback)

    def register_category_callback(self, category: ErrorCategory, callback: ErrorCallback) -> None:
        """
        Register a callback for a specific error category.

        Args:
            category: Error category to handle
            callback: Callback function
        """
        with self._lock:
            self._category_callbacks[category].append(callback)

    def register_global_callback(self, callback: ErrorCallback) -> None:
        """
        Register a global callback.

        Args:
            callback: Callback function
        """
        with self._lock:
            self._global_callbacks.append(callback)

    def get_error(self, error_id: str) -> Optional[ErrorInfo]:
        """
        Get information about a specific error.

        Args:
            error_id: ID of the error

        Returns:
            Optional[ErrorInfo]: Error information if found, None otherwise
        """
        with self._lock:
            return self._errors.get(error_id)

    def get_recent_errors(self, limit: int = 10) -> List[ErrorInfo]:
        """
        Get recent errors.

        Args:
            limit: Maximum number of errors to return

        Returns:
            List[ErrorInfo]: List of recent errors
        """
        with self._lock:
            return list(self._recent_errors)[-limit:]

    def get_error_stats(self) -> Dict[str, Any]:
        """
        Get error statistics.

        Returns:
            Dict[str, Any]: Error statistics
        """
        with self._lock:
            return {
                'total_errors': sum(self._error_counts.values()),
                'error_counts': dict(self._error_counts),
                'category_counts': dict(self._category_counts),
                'recent_errors': len(self._recent_errors)
            }

    def clear_errors(self) -> None:
        """Clear all tracked errors."""
        with self._lock:
            self._errors.clear()
            self._error_counts.clear()
            self._category_counts.clear()
            self._recent_errors.clear()

    def with_error_handling(self, func=None, **error_kwargs):
        """
        Decorator for executing a function with error handling.

        Can be used as a decorator or as a function wrapper.

        Args:
            func: Function to execute (if None, returns a decorator)
            **error_kwargs: Error handling parameters

        Returns:
            Callable: Decorated function or decorator
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # Extract error handling parameters
                    context = error_kwargs.get('context', {})
                    source = error_kwargs.get('source', func.__name__)
                    severity = error_kwargs.get('severity', ErrorSeverity.ERROR)
                    category = error_kwargs.get('category', None)
                    circuit_breaker_name = error_kwargs.get('circuit_breaker_name', None)

                    # Handle the error
                    error_info = self.handle_error(
                        exception=e,
                        context=context,
                        source=source,
                        severity=severity,
                        category=category,
                        circuit_breaker_name=circuit_breaker_name
                    )

                    # Re-raise if recovery failed
                    if not error_info.recovery_succeeded:
                        raise

                    # Recovery succeeded, retry the function
                    return func(*args, **kwargs)
            return wrapper

        # If func is None, return the decorator
        if func is None:
            return decorator

        # Otherwise, return the decorated function
        return decorator(func)

    def _get_or_create_circuit_breaker(self, name: str, **kwargs) -> CircuitBreaker:
        """
        Get or create a circuit breaker.

        Args:
            name: Name of the circuit breaker
            **kwargs: Configuration parameters for the circuit breaker

        Returns:
            CircuitBreaker: The circuit breaker instance
        """
        with self._lock:
            if name not in self._circuit_breakers:
                self._circuit_breakers[name] = CircuitBreaker(name, **kwargs)
            return self._circuit_breakers[name]

    def with_circuit_breaker(self, circuit_breaker_name: str, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Execute a function with circuit breaker protection.

        Args:
            circuit_breaker_name: Name of the circuit breaker to use
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            T: Result of the function

        Raises:
            Exception: If the function raises an exception and the circuit is not open
        """
        # Get or create circuit breaker
        circuit_breaker = self._get_or_create_circuit_breaker(circuit_breaker_name)

        # Check if circuit is open
        if circuit_breaker.state == CircuitState.OPEN:
            raise CircuitOpenError(circuit_breaker_name)

        try:
            # Execute the function
            result = func(*args, **kwargs)
            try:
                # Use the appropriate method based on the CircuitBreaker implementation
                if hasattr(circuit_breaker, 'record_success'):
                    circuit_breaker.record_success()
                elif hasattr(circuit_breaker, 'on_success'):
                    circuit_breaker.on_success()
            except Exception as cb_error:
                logger.warning(f"Error recording circuit breaker success: {str(cb_error)}")
            return result
        except Exception as e:
            # Record failure
            try:
                # Use the appropriate method based on the CircuitBreaker implementation
                if hasattr(circuit_breaker, 'record_failure'):
                    circuit_breaker.record_failure()
                elif hasattr(circuit_breaker, 'on_failure'):
                    circuit_breaker.on_failure()
            except Exception as cb_error:
                logger.warning(f"Error recording circuit breaker failure: {str(cb_error)}")

            # Extract error handling parameters from kwargs
            context = kwargs.pop('error_context', {})
            source = kwargs.pop('error_source', None)
            severity = kwargs.pop('error_severity', ErrorSeverity.ERROR)
            category = kwargs.pop('error_category', None)

            # Add circuit breaker name to context
            context['circuit_breaker_name'] = circuit_breaker_name

            # Handle the error
            self.handle_error(
                exception=e,
                context=context,
                source=source,
                severity=severity,
                category=category,
                circuit_breaker_name=circuit_breaker_name
            )

            # Re-raise the exception
            raise

    def with_retry(self, max_retries: int = 3, retry_delay: float = 1.0,
                  backoff_factor: float = 2.0, jitter: float = 0.1,
                  retry_on: List[Type[Exception]] = None) -> Callable:
        """
        Decorator for retrying a function on failure.

        Args:
            max_retries: Maximum number of retries
            retry_delay: Initial delay between retries in seconds
            backoff_factor: Factor to increase delay between retries
            jitter: Random jitter factor to add to delay
            retry_on: List of exception types to retry on (None for all)

        Returns:
            Callable: Decorated function
        """
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            def wrapper(*args, **kwargs) -> T:
                last_exception = None

                for retry in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        # Check if we should retry on this exception
                        if retry_on and not any(isinstance(e, exc_type) for exc_type in retry_on):
                            raise

                        # Check if we've reached max retries
                        if retry >= max_retries:
                            raise

                        # Calculate delay with exponential backoff and jitter
                        delay = retry_delay * (backoff_factor ** retry)
                        delay += random.uniform(0, jitter * delay)

                        # Log retry attempt
                        logger.warning(f"Retry {retry + 1}/{max_retries} for {func.__name__} after {delay:.2f}s due to: {str(e)}")

                        # Sleep before retry
                        time.sleep(delay)

                        # Store last exception
                        last_exception = e

                # This should never be reached, but just in case
                if last_exception:
                    raise last_exception
                raise Exception(f"Retry failed for {func.__name__}")

            return wrapper

        return decorator

# Create global instance
enhanced_error_handler = EnhancedErrorHandler()

# Export convenience functions
handle_error = enhanced_error_handler.handle_error
with_error_handling = enhanced_error_handler.with_error_handling
with_circuit_breaker = enhanced_error_handler.with_circuit_breaker
with_retry = enhanced_error_handler.with_retry
