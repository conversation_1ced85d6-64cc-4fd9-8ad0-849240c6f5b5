@echo off
REM MT5 Trading Bot Monitoring Startup Script
REM This script provides multiple monitoring options for the trading bot

echo ========================================
echo MT5 Trading Bot Monitoring System
echo ========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists and activate it
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

echo.
echo Available Monitoring Options:
echo.
echo 1. Real-time Bot Performance Monitor
echo 2. Training Progress Visualizer
echo 3. Static Performance Dashboard
echo 4. Monitor Specific Terminal
echo 5. Start All Monitoring (Recommended)
echo 6. Exit
echo.

set /p choice="Please select an option (1-6): "

if "%choice%"=="1" goto :realtime_monitor
if "%choice%"=="2" goto :training_monitor
if "%choice%"=="3" goto :static_dashboard
if "%choice%"=="4" goto :specific_terminal
if "%choice%"=="5" goto :all_monitoring
if "%choice%"=="6" goto :exit
goto :invalid_choice

:realtime_monitor
echo.
echo Starting Real-time Bot Performance Monitor...
echo This will update every 30 seconds
echo Press Ctrl+C to stop
echo.
python monitor_bot_performance.py --realtime --interval 30
goto :end

:training_monitor
echo.
echo Starting Training Progress Visualizer...
echo This will monitor model training progress
echo Press Ctrl+C to stop
echo.
python visualize_training.py --log_file model_training.log --monitor --interval 30
goto :end

:static_dashboard
echo.
echo Generating Static Performance Dashboard...
python monitor_bot_performance.py
echo.
echo Dashboard generated! Check the monitoring/dashboard directory
echo Opening dashboard in default browser...
start monitoring\dashboard\performance_dashboard.html
goto :end

:specific_terminal
echo.
set /p terminal_id="Enter terminal ID to monitor: "
echo.
echo Starting monitor for terminal %terminal_id%...
python monitor_bot_performance.py --realtime --terminal %terminal_id% --interval 30
goto :end

:all_monitoring
echo.
echo Starting Comprehensive Monitoring System...
echo This will start multiple monitoring processes
echo.

REM Start real-time performance monitor in background
echo Starting real-time performance monitor...
start "Performance Monitor" cmd /c "python monitor_bot_performance.py --realtime --interval 30"

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start training visualizer if log file exists
if exist "model_training.log" (
    echo Starting training progress monitor...
    start "Training Monitor" cmd /c "python visualize_training.py --log_file model_training.log --monitor --interval 60"
)

REM Generate initial static dashboard
echo Generating initial dashboard...
python monitor_bot_performance.py

echo.
echo All monitoring systems started!
echo.
echo Active Monitors:
echo - Real-time Performance Monitor (updates every 30s)
if exist "model_training.log" echo - Training Progress Monitor (updates every 60s)
echo - Static Dashboard Generated
echo.
echo Dashboard files are saved in: monitoring\dashboard\
echo.
echo Press any key to open the main dashboard...
pause >nul
start monitoring\dashboard\performance_dashboard.html
goto :end

:invalid_choice
echo.
echo Invalid choice. Please select 1-6.
echo.
goto :start

:exit
echo.
echo Exiting monitoring system...
goto :end

:end
echo.
echo Monitoring session ended.
pause
