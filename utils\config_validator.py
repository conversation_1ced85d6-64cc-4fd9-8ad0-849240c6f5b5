"""
Configuration validator for the trading bot.
Ensures all configuration parameters are valid and consistent.
"""
import logging
import json
import os
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path
import re
import importlib
import inspect
from dataclasses import is_dataclass, fields

logger = logging.getLogger(__name__)

class ConfigError(Exception):
    """Exception raised for configuration validation errors."""
    pass

class ConfigValidator:
    """
    Validates trading bot configurations against predefined schemas.
    Includes type checking, range validation, required fields validation, and cross-reference validation.
    Provides detailed error messages for invalid configurations.
    """
    
    def __init__(self, 
                config_schemas_dir: Union[str, Path] = "config/schemas",
                custom_validators: Dict[str, callable] = None):
        """
        Initialize the configuration validator.
        
        Args:
            config_schemas_dir: Directory containing JSON schemas for validation
            custom_validators: Dictionary of custom validator functions keyed by config section
        """
        self.config_schemas_dir = Path(config_schemas_dir)
        self.custom_validators = custom_validators or {}
        
        # Create schemas directory if it doesn't exist
        os.makedirs(self.config_schemas_dir, exist_ok=True)
        
        # Create default schemas if they don't exist
        self._create_default_schemas()
        
        logger.info(f"ConfigValidator initialized with schemas directory: {self.config_schemas_dir}")
    
    def _create_default_schemas(self) -> None:
        """Create default validation schemas if they don't exist."""
        
        # Define default schemas
        default_schemas = {
            "main_config": {
                "type": "object",
                "required": ["mt5", "strategy", "models"],
                "properties": {
                    "mt5": {
                        "oneOf": [
                            {"$ref": "#/definitions/mt5_single_config"},
                            {"$ref": "#/definitions/mt5_multi_config"}
                        ]
                    },
                    "strategy": {"$ref": "#/definitions/strategy_config"},
                    "models": {"$ref": "#/definitions/models_config"},
                    "update_interval": {"type": "integer", "minimum": 1},
                    "max_memory_usage": {"type": "number", "minimum": 50, "maximum": 95},
                    "log_level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]},
                    "debug_mode": {"type": "boolean"}
                },
                "definitions": {
                    "mt5_single_config": {
                        "type": "object",
                        "required": ["path", "login", "password", "server"],
                        "properties": {
                            "path": {"type": "string"},
                            "login": {"type": "integer"},
                            "password": {"type": "string"},
                            "server": {"type": "string"},
                            "timeout": {"type": "integer", "minimum": 100, "maximum": 60000},
                            "terminal_id": {"type": "integer", "minimum": 1}
                        }
                    },
                    "mt5_multi_config": {
                        "type": "object",
                        "patternProperties": {
                            "^terminal[0-9]+$|^default$": {"$ref": "#/definitions/mt5_single_config"}
                        }
                    },
                    "strategy_config": {
                        "type": "object",
                        "required": ["symbol", "timeframes", "lot_size"],
                        "properties": {
                            "symbol": {"type": "string"},
                            "timeframes": {
                                "type": "array",
                                "items": {"type": "string"},
                                "minItems": 1
                            },
                            "sequence_length": {"type": "integer", "minimum": 1, "maximum": 500},
                            "lot_size": {"type": "number", "minimum": 0.01, "maximum": 10.0},
                            "max_positions": {"type": "integer", "minimum": 1, "maximum": 100},
                            "stop_loss_pips": {"type": "integer", "minimum": 5, "maximum": 1000},
                            "take_profit_pips": {"type": "integer", "minimum": 5, "maximum": 2000},
                            "max_spread_pips": {"type": "integer", "minimum": 1, "maximum": 100},
                            "risk_per_trade": {"type": "number", "minimum": 0.01, "maximum": 5.0},
                            "max_daily_loss": {"type": "number", "minimum": 0.1, "maximum": 100.0},
                            "max_daily_trades": {"type": "integer", "minimum": 1, "maximum": 1000},
                            "cooldown_period": {"type": "integer", "minimum": 0, "maximum": 86400}
                        }
                    },
                    "models_config": {
                        "type": "object",
                        "minProperties": 1,
                        "patternProperties": {
                            "^lstm$|^gru$": {"$ref": "#/definitions/nn_model"},
                            "^xgboost$|^lightgbm$": {"$ref": "#/definitions/tree_model"}
                        }
                    },
                    "nn_model": {
                        "type": "object",
                        "required": ["model_path", "input_dim", "output_dim", "weight", "hidden_dim", "num_layers", "dropout"],
                        "properties": {
                            "model_path": {"type": "string"},
                            "input_dim": {"type": "integer", "minimum": 1},
                            "output_dim": {"type": "integer", "minimum": 1},
                            "weight": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                            "hidden_dim": {"type": "integer", "minimum": 1},
                            "num_layers": {"type": "integer", "minimum": 1, "maximum": 10},
                            "dropout": {"type": "number", "minimum": 0.0, "maximum": 0.9}
                        }
                    },
                    "tree_model": {
                        "type": "object",
                        "required": ["model_path", "input_dim", "output_dim", "weight", "max_depth", "learning_rate", "n_estimators"],
                        "properties": {
                            "model_path": {"type": "string"},
                            "input_dim": {"type": "integer", "minimum": 1},
                            "output_dim": {"type": "integer", "minimum": 1},
                            "weight": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                            "max_depth": {"type": "integer", "minimum": 1, "maximum": 20},
                            "learning_rate": {"type": "number", "minimum": 0.0001, "maximum": 1.0},
                            "n_estimators": {"type": "integer", "minimum": 10, "maximum": 10000}
                        }
                    }
                }
            }
        }
        
        # Write schemas to files
        for schema_name, schema in default_schemas.items():
            schema_file = self.config_schemas_dir / f"{schema_name}.json"
            try:
                with open(schema_file, 'w') as f:
                    json.dump(schema, f, indent=2)
                logger.info(f"Created default schema file: {schema_file}")
            except Exception as e:
                logger.error(f"Error creating default schema file {schema_file}: {str(e)}")
    
    def validate_config(self, config: Dict, schema_name: str = "main_config") -> Tuple[bool, List[str]]:
        """
        Validate a configuration dictionary against a schema.
        
        Args:
            config: Configuration dictionary to validate
            schema_name: Name of the schema to validate against
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, list_of_errors)
        """
        errors = []
        
        # Load schema
        schema_file = self.config_schemas_dir / f"{schema_name}.json"
        try:
            if os.path.exists(schema_file):
                with open(schema_file, 'r') as f:
                    schema = json.load(f)
            else:
                logger.error(f"Schema file not found: {schema_file}")
                return False, [f"Schema file not found: {schema_file}"]
        except Exception as e:
            logger.error(f"Error loading schema file {schema_file}: {str(e)}")
            return False, [f"Error loading schema file: {str(e)}"]
        
        # Validate against schema
        validation_errors = self._validate_against_schema(config, schema)
        if validation_errors:
            errors.extend(validation_errors)
        
        # Perform additional cross-module validations
        consistency_errors = self._validate_config_consistency(config)
        if consistency_errors:
            errors.extend(consistency_errors)
        
        # Validate file paths
        path_errors = self._validate_file_paths(config)
        if path_errors:
            errors.extend(path_errors)
        
        return len(errors) == 0, errors
    
    def _validate_config_consistency(self, config: Dict) -> List[str]:
        """
        Validate consistency between different parts of the configuration.
        
        Args:
            config: Complete configuration dictionary
            
        Returns:
            List[str]: List of validation errors
        """
        errors = []
        
        # Validate model weight sum is 1.0
        if 'models' in config:
            models = config['models']
            weight_sum = sum(model.get('weight', 0) for model in models.values())
            
            # Tolerate small floating point differences
            if not (0.99 <= weight_sum <= 1.01):
                errors.append(f"Sum of model weights should be 1.0, got {weight_sum:.2f}")
        
        # Validate risk parameters are consistent
        if 'strategy' in config:
            strategy = config['strategy']
            if 'risk_per_trade' in strategy and 'max_daily_loss' in strategy:
                risk_per_trade = strategy['risk_per_trade']
                max_daily_loss = strategy['max_daily_loss']
                max_trades = strategy.get('max_daily_trades', float('inf'))
                
                # If trading max trades with max risk exceeds max loss
                if risk_per_trade * max_trades > max_daily_loss:
                    errors.append(
                        f"Risk configuration inconsistency: max_daily_trades ({max_trades}) * "
                        f"risk_per_trade ({risk_per_trade}) = {risk_per_trade * max_trades} "
                        f"exceeds max_daily_loss ({max_daily_loss})"
                    )
        
        # Validate sequence_length is compatible with input dimensions
        if 'strategy' in config and 'models' in config:
            sequence_length = config['strategy'].get('sequence_length')
            if sequence_length:
                for model_name, model_config in config['models'].items():
                    input_dim = model_config.get('input_dim')
                    if input_dim and input_dim % sequence_length != 0:
                        errors.append(
                            f"Model {model_name} input_dim ({input_dim}) is not a multiple of "
                            f"sequence_length ({sequence_length})"
                        )
        
        # Execute custom validators
        for section, validator in self.custom_validators.items():
            if section in config:
                try:
                    section_errors = validator(config[section], config)
                    if section_errors:
                        errors.extend([f"{section}: {error}" for error in section_errors])
                except Exception as e:
                    errors.append(f"Error in custom validator for {section}: {str(e)}")
        
        return errors
    
    def _validate_file_paths(self, config: Dict) -> List[str]:
        """
        Validate that all file paths in the configuration exist.
        
        Args:
            config: Complete configuration dictionary
            
        Returns:
            List[str]: List of validation errors
        """
        errors = []
        
        # Validate MT5 path exists
        if 'mt5' in config:
            mt5_config = config['mt5']
            
            # Handle single and multi-terminal configurations
            if isinstance(mt5_config, dict):
                # Check if it's a multi-terminal config
                if any(key.startswith('terminal') for key in mt5_config):
                    # Multi-terminal config
                    for terminal_id, terminal_config in mt5_config.items():
                        if isinstance(terminal_config, dict) and 'path' in terminal_config:
                            mt5_path = terminal_config['path']
                            if not os.path.exists(mt5_path):
                                errors.append(f"MT5 path for {terminal_id} does not exist: {mt5_path}")
                elif 'path' in mt5_config:
                    # Single terminal config
                    mt5_path = mt5_config['path']
                    if not os.path.exists(mt5_path):
                        errors.append(f"MT5 path does not exist: {mt5_path}")
        
        # Validate model paths exist
        if 'models' in config:
            for model_name, model_config in config['models'].items():
                if 'model_path' in model_config:
                    # Check model weights
                    if not 0 <= model_config.get('weight', 0) <= 1:
                        errors.append(f"Model {model_name} weight must be between 0 and 1")

                    # Validate model-specific parameters if needed
                    # (e.g., check dimensions, layer sizes)

        return errors
    
    def _validate_against_schema(self, config: Dict, schema: Dict) -> List[str]:
        """
        Validate a configuration dictionary against a JSON schema.
        
        Args:
            config: Configuration dictionary
            schema: JSON schema
            
        Returns:
            List[str]: List of validation errors
        """
        errors = []
        
        # Check required fields
        for field in schema.get('required', []):
            if field not in config:
                errors.append(f"Missing required field: {field}")
        
        # Validate properties
        for field, value in config.items():
            if field not in schema.get('properties', {}):
                continue
            
            field_schema = schema['properties'][field]
            
            # Handle schema references
            if '$ref' in field_schema:
                ref_path = field_schema['$ref']
                if ref_path.startswith('#/definitions/'):
                    definition_name = ref_path.split('/')[-1]
                    if 'definitions' in schema and definition_name in schema['definitions']:
                        field_schema = schema['definitions'][definition_name]
                    else:
                        errors.append(f"Schema definition not found: {definition_name}")
                        continue
            
            # Type validation
            self._validate_type(field, value, field_schema, errors)
            
            # If field is an object, recursively validate properties
            if 'properties' in field_schema and isinstance(value, dict):
                nested_errors = []
                for nested_field, nested_schema in field_schema['properties'].items():
                    if nested_field in value:
                        nested_value = value[nested_field]
                        
                        # Handle schema references for nested properties
                        if '$ref' in nested_schema:
                            ref_path = nested_schema['$ref']
                            if ref_path.startswith('#/definitions/'):
                                definition_name = ref_path.split('/')[-1]
                                if 'definitions' in schema and definition_name in schema['definitions']:
                                    nested_schema = schema['definitions'][definition_name]
                                else:
                                    nested_errors.append(f"Schema definition not found: {definition_name}")
                                    continue
                        
                        # Recursively validate nested objects
                        if 'properties' in nested_schema and isinstance(nested_value, dict):
                            sub_errors = self._validate_against_schema(nested_value, nested_schema)
                            if sub_errors:
                                nested_errors.extend([f"{nested_field}.{error}" for error in sub_errors])
                        else:
                            self._validate_type(f"{field}.{nested_field}", nested_value, nested_schema, nested_errors)
                
                if nested_errors:
                    errors.extend(nested_errors)
        
        return errors
    
    def _validate_type(self, field_path: str, value: Any, schema: Dict, errors: List[str]) -> None:
        """
        Validate a value against a schema type definition.
        
        Args:
            field_path: Path to the field (for error messages)
            value: Value to validate
            schema: Schema for the field
            errors: List to append errors to
        """
        field_type = schema.get('type')
        if not field_type:
            return
        
        # Handle multiple allowed types
        if isinstance(field_type, list):
            valid_type = False
            for t in field_type:
                if self._check_type(value, t):
                    valid_type = True
                    break
            
            if not valid_type:
                errors.append(f"Field {field_path} should be one of {field_type}, got {type(value).__name__}")
            return
        
        # Type validation
        if not self._check_type(value, field_type):
            errors.append(f"Field {field_path} should be {field_type}, got {type(value).__name__}")
            return
        
        # Range validation
        if field_type in ['number', 'integer'] and isinstance(value, (int, float)):
            minimum = schema.get('minimum')
            maximum = schema.get('maximum')
            
            if minimum is not None and value < minimum:
                errors.append(f"Field {field_path} should be at least {minimum}, got {value}")
            if maximum is not None and value > maximum:
                errors.append(f"Field {field_path} should be at most {maximum}, got {value}")
        
        # Enum validation
        if 'enum' in schema and value not in schema['enum']:
            errors.append(f"Field {field_path} must be one of {schema['enum']}, got {value}")
        
        # Pattern validation
        if field_type == 'string' and 'pattern' in schema:
            pattern = schema['pattern']
            if not re.match(pattern, value):
                errors.append(f"Field {field_path} must match pattern {pattern}, got {value}")
        
        # Array validation
        if field_type == 'array' and isinstance(value, list):
            min_items = schema.get('minItems')
            max_items = schema.get('maxItems')
            
            if min_items is not None and len(value) < min_items:
                errors.append(f"Field {field_path} should have at least {min_items} items, got {len(value)}")
            if max_items is not None and len(value) > max_items:
                errors.append(f"Field {field_path} should have at most {max_items} items, got {len(value)}")
            
            # Validate array items
            if 'items' in schema and value:
                item_schema = schema['items']
                for i, item in enumerate(value):
                    self._validate_type(f"{field_path}[{i}]", item, item_schema, errors)
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """
        Check if a value matches the expected type.
        
        Args:
            value: Value to check
            expected_type: Expected type
            
        Returns:
            bool: Whether the value matches the expected type
        """
        if expected_type == 'string':
            return isinstance(value, str)
        elif expected_type == 'number':
            return isinstance(value, (int, float))
        elif expected_type == 'integer':
            return isinstance(value, int)
        elif expected_type == 'boolean':
            return isinstance(value, bool)
        elif expected_type == 'array':
            return isinstance(value, list)
        elif expected_type == 'object':
            return isinstance(value, dict)
        elif expected_type == 'null':
            return value is None
        return False
    
    def validate_config_from_file(self, config_file: str) -> Tuple[bool, List[str]]:
        """
        Validate configuration from a file.
        
        Args:
            config_file: Path to configuration file
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, list_of_errors)
        """
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            return self.validate_config(config)
        except json.JSONDecodeError as e:
            return False, [f"Invalid JSON in configuration file: {str(e)}"]
        except Exception as e:
            return False, [f"Error reading configuration file: {str(e)}"]
    
    def register_custom_validator(self, section: str, validator_func: callable) -> None:
        """
        Register a custom validator function for a config section.
        
        Args:
            section: Configuration section to validate
            validator_func: Function that takes (section_config, full_config) and returns list of errors
        """
        self.custom_validators[section] = validator_func
        logger.info(f"Registered custom validator for section: {section}")
    
    def generate_config_template(self, output_file: str = "config/template.json") -> bool:
        """
        Generate a template configuration file based on schemas.
        
        Args:
            output_file: Path to output template file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Load main schema
            schema_file = self.config_schemas_dir / "main_config.json"
            with open(schema_file, 'r') as f:
                schema = json.load(f)
            
            # Generate template from schema
            template = self._generate_template_from_schema(schema)
            
            # Write template to file
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w') as f:
                json.dump(template, f, indent=2)
            
            logger.info(f"Generated config template at {output_file}")
            return True
        except Exception as e:
            logger.error(f"Error generating config template: {str(e)}")
            return False
    
    def _generate_template_from_schema(self, schema: Dict, definitions: Dict = None) -> Dict:
        """
        Generate a template dictionary from a JSON schema.
        
        Args:
            schema: JSON schema
            definitions: Schema definitions for resolving references
            
        Returns:
            Dict: Template dictionary
        """
        if definitions is None and 'definitions' in schema:
            definitions = schema['definitions']
        
        if not definitions:
            definitions = {}
        
        template = {}
        
        # Handle schema type
        schema_type = schema.get('type')
        if not schema_type:
            return template
        
        # Handle oneOf
        if 'oneOf' in schema:
            # Use the first option
            return self._generate_template_from_schema(schema['oneOf'][0], definitions)
        
        # Handle references
        if '$ref' in schema:
            ref_path = schema['$ref']
            if ref_path.startswith('#/definitions/'):
                definition_name = ref_path.split('/')[-1]
                if definition_name in definitions:
                    return self._generate_template_from_schema(definitions[definition_name], definitions)
            return {}
        
        # Handle different types
        if schema_type == 'object':
            for prop_name, prop_schema in schema.get('properties', {}).items():
                # Skip if not required and not generating full template
                if prop_name not in schema.get('required', []):
                    continue
                
                template[prop_name] = self._generate_template_from_schema(prop_schema, definitions)
                
        elif schema_type == 'array':
            if 'items' in schema:
                # Add a single example item
                template = [self._generate_template_from_schema(schema['items'], definitions)]
        
        # Add default values for primitives
        elif schema_type == 'string':
            if 'enum' in schema and schema['enum']:
                template = schema['enum'][0]
            else:
                template = ""
        elif schema_type == 'number':
            if 'minimum' in schema:
                template = schema['minimum']
            else:
                template = 0.0
        elif schema_type == 'integer':
            if 'minimum' in schema:
                template = schema['minimum']
            else:
                template = 0
        elif schema_type == 'boolean':
            template = False
        
        return template 