"""
Thread manager for implementing concurrent operations in the trading bot.
Provides efficient multi-threading with thread pooling and task scheduling.
"""
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from typing import Dict, List, Any, Callable, Optional, Union, Tuple
from queue import Queue, PriorityQueue
import time
from dataclasses import dataclass, field
from datetime import datetime
import traceback
from functools import wraps

logger = logging.getLogger(__name__)

@dataclass(order=True)
class PrioritizedTask:
    """A task with priority for scheduling."""
    priority: int
    task_id: str = field(compare=False)
    func: Callable = field(compare=False)
    args: tuple = field(default_factory=tuple, compare=False)
    kwargs: Dict = field(default_factory=dict, compare=False)
    created_at: datetime = field(default_factory=datetime.now, compare=False)

@dataclass
class TaskResult:
    """Result of a task execution."""
    task_id: str
    success: bool
    result: Any = None
    error: Optional[Exception] = None
    execution_time: float = 0.0
    completed_at: datetime = field(default_factory=datetime.now)

class TaskStatus:
    """Enum-like class for task statuses."""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELED = "CANCELED"

class ThreadManager:
    """
    Manages thread pools and concurrent task execution.
    
    Features:
    - Thread pool management
    - Priority-based task scheduling
    - Task cancellation and timeout handling
    - Monitoring of thread utilization
    - Resource management to prevent thread starvation
    """
    
    def __init__(
        self,
        max_workers: int = None,
        thread_name_prefix: str = "TradingBot",
        task_timeout: int = 60
    ):
        """
        Initialize the thread manager.
        
        Args:
            max_workers: Maximum number of worker threads (defaults to CPU count * 5)
            thread_name_prefix: Prefix for thread names
            task_timeout: Default timeout for tasks in seconds
        """
        # Use CPU count * 5 as default (typical I/O bound tasks in trading)
        self._max_workers = max_workers or (threading.cpu_count() * 5)
        self._thread_name_prefix = thread_name_prefix
        self._default_timeout = task_timeout
        
        # Initialize thread pool
        self._pool = ThreadPoolExecutor(
            max_workers=self._max_workers,
            thread_name_prefix=self._thread_name_prefix
        )
        
        # Task tracking
        self._tasks: Dict[str, Tuple[Future, PrioritizedTask]] = {}
        self._task_queue = PriorityQueue()
        self._results: Dict[str, TaskResult] = {}
        self._lock = threading.RLock()
        
        # Monitoring
        self._active_threads = 0
        self._total_tasks_submitted = 0
        self._total_tasks_completed = 0
        self._total_tasks_failed = 0
        
        # Control flag
        self._running = True
        
        # Start scheduler thread
        self._scheduler_thread = threading.Thread(
            target=self._task_scheduler,
            name=f"{self._thread_name_prefix}-Scheduler",
            daemon=True
        )
        self._scheduler_thread.start()
        
        logger.info(f"Thread manager initialized with {self._max_workers} workers")
    
    def submit_task(
        self,
        func: Callable,
        *args,
        task_id: Optional[str] = None,
        priority: int = 10,
        timeout: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Submit a task for execution.
        
        Args:
            func: Function to execute
            *args: Positional arguments for the function
            task_id: Unique identifier for the task (generated if not provided)
            priority: Task priority (lower value means higher priority)
            timeout: Timeout in seconds (uses default if not provided)
            **kwargs: Keyword arguments for the function
            
        Returns:
            str: Task ID
        """
        with self._lock:
            if not self._running:
                raise RuntimeError("Thread manager is shutting down")
            
            # Generate task ID if not provided
            if task_id is None:
                task_id = f"task-{int(time.time() * 1000)}-{self._total_tasks_submitted}"
            
            # Create task
            task = PrioritizedTask(
                priority=priority,
                task_id=task_id,
                func=func,
                args=args,
                kwargs=kwargs,
                created_at=datetime.now()
            )
            
            # Add to queue
            self._task_queue.put(task)
            
            # Update stats
            self._total_tasks_submitted += 1
            
            logger.debug(f"Task {task_id} submitted with priority {priority}")
            
            return task_id
    
    def _task_scheduler(self):
        """Task scheduler thread function."""
        while self._running:
            try:
                # Get next task
                try:
                    task = self._task_queue.get(timeout=1.0)
                except Exception:
                    # No task available, continue
                    continue
                
                # Check if we have capacity
                with self._lock:
                    if self._active_threads >= self._max_workers:
                        # Put task back and wait
                        self._task_queue.put(task)
                        time.sleep(0.1)
                        continue
                    
                    # Submit task to thread pool
                    future = self._pool.submit(self._execute_task, task)
                    self._tasks[task.task_id] = (future, task)
                    self._active_threads += 1
                
                # Mark task as processed
                self._task_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in task scheduler: {str(e)}")
                time.sleep(1.0)  # Avoid tight loop on error
        
        logger.info("Task scheduler thread stopped")
    
    def _execute_task(self, task: PrioritizedTask) -> TaskResult:
        """
        Execute a task and return the result.
        
        Args:
            task: Task to execute
            
        Returns:
            TaskResult: Task execution result
        """
        start_time = time.time()
        try:
            # Execute task
            result = task.func(*task.args, **task.kwargs)
            
            # Create result
            task_result = TaskResult(
                task_id=task.task_id,
                success=True,
                result=result,
                execution_time=time.time() - start_time,
                completed_at=datetime.now()
            )
            
            logger.debug(f"Task {task.task_id} completed successfully in {task_result.execution_time:.2f}s")
            
            return task_result
            
        except Exception as e:
            # Log error
            logger.error(f"Task {task.task_id} failed: {str(e)}\n{traceback.format_exc()}")
            
            # Create result
            task_result = TaskResult(
                task_id=task.task_id,
                success=False,
                error=e,
                execution_time=time.time() - start_time,
                completed_at=datetime.now()
            )
            
            return task_result
        finally:
            # Update task tracking
            with self._lock:
                self._results[task.task_id] = task_result
                if task.task_id in self._tasks:
                    del self._tasks[task.task_id]
                self._active_threads -= 1
                
                if task_result.success:
                    self._total_tasks_completed += 1
                else:
                    self._total_tasks_failed += 1
    
    def get_task_result(
        self,
        task_id: str,
        timeout: Optional[int] = None,
        block: bool = True
    ) -> Optional[TaskResult]:
        """
        Get the result of a task.
        
        Args:
            task_id: Task ID
            timeout: Timeout in seconds (None for no timeout)
            block: Whether to block until the task completes
            
        Returns:
            Optional[TaskResult]: Task result or None if not available
        """
        # Check if result is already available
        if task_id in self._results:
            return self._results[task_id]
        
        # Check if task is still running
        if task_id not in self._tasks:
            return None
        
        # Wait for task to complete if blocking
        if block:
            future, _ = self._tasks[task_id]
            try:
                # Wait for future to complete
                result = future.result(timeout=timeout or self._default_timeout)
                return result
            except Exception:
                # Timeout or error, return None
                return None
        
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            bool: True if task was canceled, False otherwise
        """
        with self._lock:
            if task_id in self._tasks:
                future, task = self._tasks[task_id]
                cancelled = future.cancel()
                
                if cancelled:
                    logger.info(f"Task {task_id} canceled")
                    
                    # Update stats
                    self._active_threads -= 1
                    
                    # Create result
                    task_result = TaskResult(
                        task_id=task_id,
                        success=False,
                        error=Exception("Task canceled"),
                        completed_at=datetime.now()
                    )
                    self._results[task_id] = task_result
                    
                    # Remove from tasks
                    del self._tasks[task_id]
                    
                    return True
            
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get thread manager statistics.
        
        Returns:
            Dict[str, Any]: Statistics dictionary
        """
        with self._lock:
            return {
                "max_workers": self._max_workers,
                "active_threads": self._active_threads,
                "pending_tasks": self._task_queue.qsize(),
                "total_tasks_submitted": self._total_tasks_submitted,
                "total_tasks_completed": self._total_tasks_completed,
                "total_tasks_failed": self._total_tasks_failed,
                "task_success_rate": (
                    self._total_tasks_completed / 
                    (self._total_tasks_completed + self._total_tasks_failed)
                    if (self._total_tasks_completed + self._total_tasks_failed) > 0 else 0
                )
            }
    
    def shutdown(self, wait: bool = True, cancel_pending: bool = False):
        """
        Shutdown the thread manager.
        
        Args:
            wait: Whether to wait for tasks to complete
            cancel_pending: Whether to cancel pending tasks
        """
        logger.info("Shutting down thread manager...")
        
        # Stop scheduler
        self._running = False
        
        # Cancel pending tasks if requested
        if cancel_pending:
            with self._lock:
                # Cancel all tasks in queue
                while not self._task_queue.empty():
                    try:
                        task = self._task_queue.get_nowait()
                        logger.info(f"Canceling pending task {task.task_id}")
                        self._task_queue.task_done()
                    except Exception:
                        break
                
                # Cancel running tasks
                for task_id, (future, _) in list(self._tasks.items()):
                    future.cancel()
                    logger.info(f"Canceling running task {task_id}")
        
        # Shutdown thread pool
        self._pool.shutdown(wait=wait)
        
        # Wait for scheduler thread to complete
        if self._scheduler_thread.is_alive():
            self._scheduler_thread.join(timeout=5.0)
        
        logger.info("Thread manager shutdown complete")

def async_task(func=None, *, priority=50):
    """
    Decorator for async task execution.
    
    Args:
        func: Function to decorate
        priority: Task priority
        
    Returns:
        Callable: Decorated function
    """
    def decorator(f):
        @wraps(f)
        def wrapper(self, *args, **kwargs):
            # Get thread manager from instance
            if not hasattr(self, 'thread_manager'):
                raise AttributeError("Class must have a thread_manager attribute")
            
            thread_manager = self.thread_manager
            
            # Submit task
            task_id = thread_manager.submit_task(
                f, self, *args,
                priority=priority,
                **kwargs
            )
            
            # Return task ID
            return task_id
        
        return wrapper
    
    if func is None:
        return decorator
    return decorator(func) 