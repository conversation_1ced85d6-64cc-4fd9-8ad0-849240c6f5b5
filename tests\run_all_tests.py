#!/usr/bin/env python
"""
Unified test runner for all model evaluations and system tests.

This script orchestrates the execution of all model tests and system validations:

1. Individual model testing with comprehensive evaluation
2. Cross-validation and hyperparameter optimization
3. Model comparison and ranking
4. System integration tests
5. Performance benchmarking
6. Automated reporting and visualization

Usage:
    python tests/run_all_tests.py [--models MODEL_NAMES] [--quick] [--output OUTPUT_DIR]
"""

import sys
import logging
import argparse
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
# Removed unused imports

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_runner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_runner')

# Import project modules with error handling
try:
    from config import ConfigurationManager
    from tests.model_comparison import ModelComparator
except ImportError as e:
    logger.error(f"Failed to import required modules: {str(e)}")
    sys.exit(1)

class TestRunner:
    """Main class for running all tests and evaluations."""

    def __init__(self, output_dir: Optional[str] = None, quick_mode: bool = False):
        """Initialize test runner.

        Args:
            output_dir: Optional output directory for results
            quick_mode: Whether to run in quick mode (reduced testing)
        """
        self.config_manager = ConfigurationManager()
        self.config = self.config_manager.get_config()
        self.quick_mode = quick_mode

        # Create output directory
        self.output_dir = Path(output_dir) if output_dir else Path("test_results") / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.test_results = {}
        self.failed_tests = []

        logger.info(f"Test runner initialized with output directory: {self.output_dir}")
        logger.info(f"Quick mode: {self.quick_mode}")

    def run_individual_model_tests(self, model_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run individual model tests.

        Args:
            model_names: Optional list of specific models to test

        Returns:
            Dictionary containing test results
        """
        try:
            models_to_test = model_names if model_names else list(self.config.models.keys())
            logger.info(f"Running individual tests for models: {models_to_test}")

            test_results = {}

            # Available model test scripts
            test_scripts = {
                'lstm': 'tests/models/test_lstm_model.py',
                'gru': 'tests/models/test_gru_model.py',
                'transformer': 'tests/models/test_transformer_model.py',
                'tft': 'tests/models/test_tft_model.py',
                'xgboost': 'tests/models/test_xgboost_model.py',
                'lightgbm': 'tests/models/test_lightgbm_model.py',
                'arima': 'tests/models/test_arima_model.py',
                'lstm_arima': 'tests/models/test_ensemble_model.py',
                'tft_arima': 'tests/models/test_ensemble_model.py'
            }

            for model_name in models_to_test:
                if model_name not in self.config.models:
                    logger.warning(f"Model {model_name} not found in configuration")
                    continue

                model_config = self.config.models[model_name]
                model_type = model_config.model_type.lower()

                # Find appropriate test script
                test_script = None
                for script_type, script_path in test_scripts.items():
                    if script_type in model_type:
                        test_script = script_path
                        break

                if not test_script or not Path(test_script).exists():
                    logger.warning(f"No test script found for {model_name} (type: {model_type})")
                    continue

                try:
                    logger.info(f"Running test for {model_name}")
                    result = self._run_model_test(model_name, test_script)
                    test_results[model_name] = result

                    if result['success']:
                        logger.info(f"Test passed for {model_name}")
                    else:
                        logger.error(f"Test failed for {model_name}: {result.get('error', 'Unknown error')}")
                        self.failed_tests.append(model_name)

                except Exception as e:
                    logger.error(f"Exception during test of {model_name}: {str(e)}")
                    self.failed_tests.append(model_name)
                    test_results[model_name] = {
                        'success': False,
                        'model_name': model_name,
                        'error': str(e)
                    }

            return test_results

        except Exception as e:
            logger.error(f"Error running individual model tests: {str(e)}")
            return {}

    def _run_model_test(self, model_name: str, test_script: str) -> Dict[str, Any]:
        """Run a single model test script.

        Args:
            model_name: Name of the model
            test_script: Path to test script

        Returns:
            Test result dictionary
        """
        try:
            start_time = datetime.now()

            # Prepare command
            cmd = [sys.executable, test_script]
            if self.quick_mode:
                cmd.extend(['--quick'])

            # Run test
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600 if not self.quick_mode else 600  # 1 hour normal, 10 min quick
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # Parse results
            success = result.returncode == 0

            return {
                'success': success,
                'model_name': model_name,
                'test_script': test_script,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }

        except subprocess.TimeoutExpired:
            logger.error(f"Test timeout for {model_name}")
            return {
                'success': False,
                'model_name': model_name,
                'error': 'Test timeout',
                'duration': 3600 if not self.quick_mode else 600
            }
        except Exception as e:
            logger.error(f"Error running test for {model_name}: {str(e)}")
            return {
                'success': False,
                'model_name': model_name,
                'error': str(e)
            }

    def run_model_comparison(self, model_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run model comparison analysis.

        Args:
            model_names: Optional list of specific models to compare

        Returns:
            Comparison results
        """
        try:
            logger.info("Running model comparison analysis")

            # Create comparison output directory
            comparison_dir = self.output_dir / "model_comparison"

            # Initialize comparator
            comparator = ModelComparator(str(comparison_dir))

            # Load model results
            results = comparator.load_model_results(model_names)

            if not results:
                logger.warning("No model results available for comparison")
                return {'success': False, 'error': 'No model results available'}

            # Perform comparison
            comparison_results = comparator.compare_models()

            if comparison_results:
                logger.info("Model comparison completed successfully")
                return {
                    'success': True,
                    'comparison_results': comparison_results,
                    'output_dir': str(comparison_dir)
                }
            else:
                logger.error("Model comparison failed")
                return {'success': False, 'error': 'Comparison analysis failed'}

        except Exception as e:
            logger.error(f"Error running model comparison: {str(e)}")
            return {'success': False, 'error': str(e)}

    def run_system_integration_tests(self) -> Dict[str, Any]:
        """Run system integration tests.

        Returns:
            Integration test results
        """
        try:
            logger.info("Running system integration tests")

            integration_results = {}

            # Test configuration loading
            config_test = self._test_configuration_loading()
            integration_results['configuration'] = config_test

            # Test data pipeline
            data_test = self._test_data_pipeline()
            integration_results['data_pipeline'] = data_test

            # Test model loading
            model_test = self._test_model_loading()
            integration_results['model_loading'] = model_test

            # Test adapter functionality
            adapter_test = self._test_model_adapter()
            integration_results['model_adapter'] = adapter_test

            # Calculate overall success
            all_passed = all(result.get('success', False) for result in integration_results.values())

            return {
                'success': all_passed,
                'tests': integration_results,
                'summary': {
                    'total_tests': len(integration_results),
                    'passed': sum(1 for r in integration_results.values() if r.get('success', False)),
                    'failed': sum(1 for r in integration_results.values() if not r.get('success', False))
                }
            }

        except Exception as e:
            logger.error(f"Error running integration tests: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _test_configuration_loading(self) -> Dict[str, Any]:
        """Test configuration loading."""
        try:
            config = self.config_manager.get_config()

            # Basic validation
            assert hasattr(config, 'models'), "Configuration missing models"
            assert hasattr(config, 'data_base_path'), "Configuration missing data_base_path"
            assert len(config.models) > 0, "No models configured"

            return {
                'success': True,
                'message': f"Configuration loaded successfully with {len(config.models)} models"
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _test_data_pipeline(self) -> Dict[str, Any]:
        """Test data pipeline functionality."""
        try:
            # Check if processed data exists
            data_dir = Path(self.config.data_base_path) / "processed"

            required_dirs = ['train', 'validation', 'test']
            for dir_name in required_dirs:
                dir_path = data_dir / dir_name
                if not dir_path.exists():
                    return {'success': False, 'error': f"Missing data directory: {dir_path}"}

            # Check for data files
            test_file = data_dir / "test" / "BTCUSD.a_M5.parquet"
            if not test_file.exists():
                return {'success': False, 'error': f"Missing test data file: {test_file}"}

            return {
                'success': True,
                'message': "Data pipeline validation passed"
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _test_model_loading(self) -> Dict[str, Any]:
        """Test model loading functionality."""
        try:
            # Test model path construction and basic validation
            for model_name, model_config in self.config.models.items():
                try:
                    # Validate model configuration
                    if not hasattr(model_config, 'model_type'):
                        return {'success': False, 'error': f"Model {model_name} missing model_type"}

                    if not hasattr(model_config, 'FEATURE_COLUMNS'):
                        return {'success': False, 'error': f"Model {model_name} missing FEATURE_COLUMNS"}

                    # Test with first model only for basic validation
                    break

                except Exception as e:
                    return {'success': False, 'error': f"Model loading test failed for {model_name}: {str(e)}"}

            return {
                'success': True,
                'message': "Model loading validation passed"
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _test_model_adapter(self) -> Dict[str, Any]:
        """Test model adapter functionality."""
        try:
            from model_adapter import ModelInputAdapter
            import numpy as np

            # Test input adaptation
            test_input = np.random.rand(100, 5)
            target_shape = (60, 5)

            # Test different model types
            model_types = ['lstm', 'xgboost', 'tft']

            for model_type in model_types:
                adapted = ModelInputAdapter.adapt_input(test_input, target_shape, model_type)
                if adapted is None:
                    return {'success': False, 'error': f"Adapter failed for {model_type}"}

            return {
                'success': True,
                'message': "Model adapter validation passed"
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def run_all_tests(self, model_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run all tests and evaluations.

        Args:
            model_names: Optional list of specific models to test

        Returns:
            Complete test results
        """
        try:
            logger.info("Starting comprehensive test suite")
            start_time = datetime.now()

            all_results = {
                'test_session': {
                    'start_time': start_time.isoformat(),
                    'quick_mode': self.quick_mode,
                    'models_tested': model_names if model_names else 'all'
                }
            }

            # Run system integration tests first
            logger.info("Phase 1: System Integration Tests")
            integration_results = self.run_system_integration_tests()
            all_results['integration_tests'] = integration_results

            if not integration_results['success']:
                logger.error("Integration tests failed, skipping model tests")
                return all_results

            # Run individual model tests
            logger.info("Phase 2: Individual Model Tests")
            model_test_results = self.run_individual_model_tests(model_names)
            all_results['model_tests'] = model_test_results

            # Run model comparison if we have successful tests
            successful_models = [name for name, result in model_test_results.items() if result.get('success', False)]

            if successful_models:
                logger.info("Phase 3: Model Comparison Analysis")
                comparison_results = self.run_model_comparison(successful_models)
                all_results['model_comparison'] = comparison_results
            else:
                logger.warning("No successful model tests, skipping comparison")
                all_results['model_comparison'] = {'success': False, 'error': 'No successful model tests'}

            # Generate final report
            end_time = datetime.now()
            all_results['test_session']['end_time'] = end_time.isoformat()
            all_results['test_session']['duration'] = (end_time - start_time).total_seconds()

            self._generate_final_report(all_results)

            return all_results

        except Exception as e:
            logger.error(f"Error running all tests: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _generate_final_report(self, results: Dict[str, Any]):
        """Generate final comprehensive report."""
        try:
            # Save detailed results
            report_path = self.output_dir / "comprehensive_test_report.json"
            with open(report_path, 'w') as f:
                json.dump(results, f, indent=4, default=str)

            logger.info(f"Comprehensive test report saved to {report_path}")

            # Print summary
            self._print_test_summary(results)

        except Exception as e:
            logger.error(f"Error generating final report: {str(e)}")

    def _print_test_summary(self, results: Dict[str, Any]):
        """Print test summary to console."""
        try:
            print("\n" + "="*80)
            print("COMPREHENSIVE TEST SUMMARY")
            print("="*80)

            # Integration tests
            if 'integration_tests' in results:
                integration = results['integration_tests']
                print(f"Integration Tests: {'PASSED' if integration['success'] else 'FAILED'}")
                if 'summary' in integration:
                    summary = integration['summary']
                    print(f"  {summary['passed']}/{summary['total_tests']} tests passed")

            # Model tests
            if 'model_tests' in results:
                model_tests = results['model_tests']
                total_models = len(model_tests)
                successful_models = sum(1 for r in model_tests.values() if r.get('success', False))
                print(f"Model Tests: {successful_models}/{total_models} models passed")

                for model_name, result in model_tests.items():
                    status = "PASSED" if result.get('success', False) else "FAILED"
                    duration = result.get('duration', 0)
                    print(f"  {model_name}: {status} ({duration:.1f}s)")

            # Model comparison
            if 'model_comparison' in results:
                comparison = results['model_comparison']
                print(f"Model Comparison: {'COMPLETED' if comparison['success'] else 'FAILED'}")

                if comparison['success'] and 'comparison_results' in comparison:
                    comp_results = comparison['comparison_results']
                    if 'recommendations' in comp_results and 'best_overall' in comp_results['recommendations']:
                        best = comp_results['recommendations']['best_overall']
                        print(f"  Best Model: {best['model']} (score: {best['score']:.4f})")

            # Session summary
            if 'test_session' in results:
                session = results['test_session']
                duration = session.get('duration', 0)
                print(f"\nTotal Duration: {duration:.1f} seconds")
                print(f"Quick Mode: {session.get('quick_mode', False)}")

            print("="*80)

        except Exception as e:
            logger.error(f"Error printing test summary: {str(e)}")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run comprehensive model tests and evaluations')
    parser.add_argument('--models', nargs='+', help='Specific models to test')
    parser.add_argument('--quick', action='store_true', help='Run in quick mode (reduced testing)')
    parser.add_argument('--output', type=str, help='Output directory for results')
    parser.add_argument('--integration-only', action='store_true', help='Run only integration tests')
    parser.add_argument('--comparison-only', action='store_true', help='Run only model comparison')
    return parser.parse_args()


def main():
    """Main function."""
    try:
        args = parse_arguments()

        logger.info("Starting comprehensive test runner")

        # Initialize test runner
        runner = TestRunner(args.output, args.quick)

        if args.integration_only:
            # Run only integration tests
            results = runner.run_system_integration_tests()
            logger.info(f"Integration tests: {'PASSED' if results['success'] else 'FAILED'}")
        elif args.comparison_only:
            # Run only model comparison
            results = runner.run_model_comparison(args.models)
            logger.info(f"Model comparison: {'COMPLETED' if results['success'] else 'FAILED'}")
        else:
            # Run all tests
            results = runner.run_all_tests(args.models)

            if 'test_session' in results:
                duration = results['test_session'].get('duration', 0)
                logger.info(f"All tests completed in {duration:.1f} seconds")

    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
