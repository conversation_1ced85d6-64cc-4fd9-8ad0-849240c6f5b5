"""
Trading package for the trading bot system.

This package contains all trading-related modules including:
- TradingBot: Main trading bot implementation
- SignalGenerator: Trading signal generation
- TradeExecutor: Trade execution logic
- TradingStrategy: Trading strategy implementation
- MT5ConnectionManager: MT5 connection management
- DataCollector: Real-time data collection
"""

from .bot import TradingBot
from .signal_generator import SignalGenerator
from .executor import TradeExecutor
from .strategy import TradingStrategy
from .mt5_connection_manager import MT5ConnectionManager
from .data_collector import DataCollector

__all__ = [
    'TradingBot',
    'SignalGenerator', 
    'TradeExecutor',
    'TradingStrategy',
    'MT5ConnectionManager',
    'DataCollector'
]
