#!/usr/bin/env python3
"""
Batch Training Script for All Timeframes
========================================

This script trains all models on all timeframes (M5, M15, M30, H1, H4) in an organized manner.
It provides options for training specific models, timeframes, or complete batch training.

Usage Examples:
    # Train all models on all timeframes
    python train_all_timeframes.py

    # Train specific models on all timeframes
    python train_all_timeframes.py --models lstm gru xgboost

    # Train all models on specific timeframes
    python train_all_timeframes.py --timeframes M5 H1 H4

    # Train with custom parameters
    python train_all_timeframes.py --models lstm --epochs 200 --batch-size 64
"""

import argparse
import subprocess
import sys
import logging
from datetime import datetime
from pathlib import Path
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'batch_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Available models and timeframes
AVAILABLE_MODELS = [
    'lstm', 'gru', 'transformer', 'tft', 
    'xgboost', 'lightgbm', 'arima', 
    'lstm_arima', 'tft_arima'
]

AVAILABLE_TIMEFRAMES = ['M5', 'M15', 'M30', 'H1', 'H4']

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Batch training script for all timeframes',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Train all models on all timeframes
  %(prog)s --models lstm gru                  # Train LSTM and GRU on all timeframes
  %(prog)s --timeframes M5 H1                 # Train all models on M5 and H1
  %(prog)s --models lstm --timeframes H4      # Train LSTM on H4 only
  %(prog)s --models xgboost --epochs 500     # Train XGBoost with 500 epochs on all timeframes
        """
    )
    
    # Model selection
    parser.add_argument('--models', nargs='+', choices=AVAILABLE_MODELS,
                        help='Specific models to train (default: all models)')
    
    # Timeframe selection
    parser.add_argument('--timeframes', nargs='+', choices=AVAILABLE_TIMEFRAMES,
                        help='Specific timeframes to train on (default: all timeframes)')
    
    # Training parameters
    parser.add_argument('--symbol', type=str, default='BTCUSD.a',
                        help='Trading symbol (default: BTCUSD.a)')
    parser.add_argument('--epochs', type=int,
                        help='Number of training epochs')
    parser.add_argument('--batch-size', type=int,
                        help='Batch size for training')
    parser.add_argument('--learning-rate', type=float,
                        help='Learning rate for training')
    parser.add_argument('--patience', type=int,
                        help='Early stopping patience')
    parser.add_argument('--max-workers', type=int, default=2,
                        help='Maximum concurrent training processes (default: 2)')
    
    # Execution options
    parser.add_argument('--dry-run', action='store_true',
                        help='Show commands that would be executed without running them')
    parser.add_argument('--continue-on-error', action='store_true',
                        help='Continue training other models if one fails')
    parser.add_argument('--force', action='store_true',
                        help='Force retraining even if models exist')
    
    return parser.parse_args()

def build_training_command(models, timeframe, symbol, args):
    """Build the training command for a specific configuration."""
    cmd = ['python', 'train_models.py']
    
    # Add models
    if models:
        cmd.extend(['--models'] + models)
    
    # Add timeframe and symbol
    cmd.extend(['--timeframe', timeframe])
    cmd.extend(['--symbol', symbol])
    
    # Add optional parameters
    if args.epochs:
        cmd.extend(['--epochs', str(args.epochs)])
    if args.batch_size:
        cmd.extend(['--batch-size', str(args.batch_size)])
    if args.learning_rate:
        cmd.extend(['--learning-rate', str(args.learning_rate)])
    if args.patience:
        cmd.extend(['--patience', str(args.patience)])
    if args.max_workers:
        cmd.extend(['--max-workers', str(args.max_workers)])
    if args.force:
        cmd.append('--force')
    
    return cmd

def execute_training_command(cmd, timeframe, models_str, dry_run=False):
    """Execute a training command."""
    cmd_str = ' '.join(cmd)
    logger.info(f"Training {models_str} on {timeframe}: {cmd_str}")
    
    if dry_run:
        logger.info(f"[DRY RUN] Would execute: {cmd_str}")
        return True, "Dry run - not executed"
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
        
        if result.returncode == 0:
            logger.info(f"✅ Successfully trained {models_str} on {timeframe}")
            return True, result.stdout
        else:
            logger.error(f"❌ Failed to train {models_str} on {timeframe}")
            logger.error(f"Error output: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        logger.error(f"⏰ Training {models_str} on {timeframe} timed out after 1 hour")
        return False, "Training timed out"
    except Exception as e:
        logger.error(f"💥 Exception during training {models_str} on {timeframe}: {str(e)}")
        return False, str(e)

def main():
    """Main function."""
    args = parse_arguments()
    
    # Determine models and timeframes to train
    models_to_train = args.models if args.models else AVAILABLE_MODELS
    timeframes_to_train = args.timeframes if args.timeframes else AVAILABLE_TIMEFRAMES
    
    logger.info("🚀 Starting batch training session")
    logger.info(f"📊 Models: {models_to_train}")
    logger.info(f"⏰ Timeframes: {timeframes_to_train}")
    logger.info(f"💰 Symbol: {args.symbol}")
    
    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - Commands will be shown but not executed")
    
    # Training results tracking
    results = {
        'start_time': datetime.now().isoformat(),
        'total_tasks': len(timeframes_to_train),
        'successful_tasks': 0,
        'failed_tasks': 0,
        'task_results': []
    }
    
    # Execute training for each timeframe
    for timeframe in timeframes_to_train:
        logger.info(f"\n{'='*60}")
        logger.info(f"🎯 Training on {timeframe} timeframe")
        logger.info(f"{'='*60}")
        
        models_str = ', '.join(models_to_train) if len(models_to_train) <= 3 else f"{len(models_to_train)} models"
        
        # Build and execute command
        cmd = build_training_command(models_to_train, timeframe, args.symbol, args)
        success, output = execute_training_command(cmd, timeframe, models_str, args.dry_run)
        
        # Track results
        task_result = {
            'timeframe': timeframe,
            'models': models_to_train,
            'success': success,
            'command': ' '.join(cmd),
            'output': output[:500] if output else None,  # Truncate long outputs
            'timestamp': datetime.now().isoformat()
        }
        results['task_results'].append(task_result)
        
        if success:
            results['successful_tasks'] += 1
        else:
            results['failed_tasks'] += 1
            if not args.continue_on_error and not args.dry_run:
                logger.error("❌ Stopping due to error. Use --continue-on-error to continue despite failures.")
                break
    
    # Final summary
    results['end_time'] = datetime.now().isoformat()
    results['total_duration'] = str(datetime.fromisoformat(results['end_time']) - datetime.fromisoformat(results['start_time']))
    
    logger.info(f"\n{'='*60}")
    logger.info("📋 BATCH TRAINING SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"✅ Successful: {results['successful_tasks']}/{results['total_tasks']}")
    logger.info(f"❌ Failed: {results['failed_tasks']}/{results['total_tasks']}")
    logger.info(f"⏱️  Total Duration: {results['total_duration']}")
    
    # Save detailed results
    results_file = f"batch_training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    logger.info(f"📄 Detailed results saved to: {results_file}")
    
    # Exit with appropriate code
    if results['failed_tasks'] > 0 and not args.dry_run:
        logger.error("❌ Some training tasks failed. Check the logs for details.")
        sys.exit(1)
    else:
        logger.info("🎉 Batch training completed successfully!")
        sys.exit(0)

if __name__ == "__main__":
    main()
