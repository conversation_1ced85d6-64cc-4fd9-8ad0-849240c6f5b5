"""
Model Adapter

This module provides adapter classes to handle input shape mismatches between
the data provided by the trading bot and the expected input shapes of the models.

The ModelInputAdapter class is responsible for transforming input data to match
the target shape expected by different model types. It handles various model
architectures including:

- Sequence models (LSTM, GRU)
- Tree-based models (XGBoost, LightGBM)
- Temporal Fusion Transformer (TFT)

Key features:
- Input validation and error handling
- Shape adaptation for different model architectures
- NaN and infinite value handling
- Detailed logging for debugging

Usage:
    from model_adapter import ModelInputAdapter

    # Example: Adapt input for an LSTM model
    adapted_input = ModelInputAdapter.adapt_input(
        X=original_input,
        target_shape=(60, 5),  # (sequence_length, features)
        model_type="lstm"
    )
"""

import logging
import numpy as np
from typing import Dict, Tuple, Union, Optional

logger = logging.getLogger(__name__)

class ModelInputAdapter:
    """
    Adapter class to transform input data to match model expectations.

    This class provides methods to adapt input data to match the expected shape
    of different model architectures. It handles common shape mismatches and
    provides detailed error messages for debugging.

    The adapter supports the following model types:
    - Sequence models (LSTM, GRU): Expects 3D input (batch_size, seq_len, features)
    - Tree-based models (XGBoost, LightGBM): Expects 2D input (batch_size, features)
    - Temporal Fusion Transformer (TFT): Expects a dictionary with encoder inputs

    All methods are static and do not require instantiation of the class.
    """

    @staticmethod
    def adapt_input(X, target_shape, model_type: str = None) -> Optional[Union[np.ndarray, Dict[str, np.ndarray]]]:
        """
        Adapt input data to match the target shape expected by the model.

        Args:
            X: Input data with shape (batch_size, seq_len, features) or other format
            target_shape: Tuple of (seq_len, features) expected by the model
            model_type: Optional model type to handle special cases ('lstm', 'gru', 'tft', 'xgboost', 'lightgbm')

        Returns:
            Adapted input data with shape matching the model's expectations
        """
        try:
            # Validate input data
            if X is None:
                logger.error("Input data is None. Cannot adapt None input.")
                return None

            # Validate target_shape
            if target_shape is None:
                logger.error("Target shape is None. Cannot adapt to unknown target shape.")
                return None

            # Validate model_type if provided
            if model_type is not None and not isinstance(model_type, str):
                logger.warning(f"model_type should be a string, got {type(model_type)}. Converting to string.")
                model_type = str(model_type)

            # Ensure target_shape is a tuple
            if not isinstance(target_shape, tuple):
                logger.warning(f"Target shape must be a tuple, got {type(target_shape)}: {target_shape}")
                try:
                    target_shape = tuple(target_shape)
                    logger.info(f"Converted target_shape to tuple: {target_shape}")
                except Exception as e:
                    logger.error(f"Failed to convert target_shape to tuple: {str(e)}. Please provide a valid tuple.")
                    return None

            # Validate target_shape has correct dimensions
            if len(target_shape) not in [1, 2]:
                logger.error(f"Target shape must have 1 or 2 dimensions, got {len(target_shape)}: {target_shape}")
                return None

            # Convert input to numpy array if needed
            if not isinstance(X, np.ndarray):
                try:
                    X = np.array(X)
                    logger.debug(f"Converted input to numpy array with shape {X.shape}")
                except Exception as e:
                    logger.error(f"Failed to convert input to numpy array: {str(e)}. Input type: {type(X)}")
                    return None

            # Validate array has data
            if X.size == 0:
                logger.error("Input array is empty. Cannot adapt empty array.")
                return None

            # Validate array has correct number of dimensions
            if X.ndim not in [1, 2, 3]:
                logger.error(f"Input array must have 1, 2, or 3 dimensions, got {X.ndim} dimensions with shape {X.shape}")
                return None

            # Check for NaN or infinite values
            nan_count = np.isnan(X).sum()
            inf_count = np.isinf(X).sum()
            if nan_count > 0 or inf_count > 0:
                logger.warning(f"Input contains {nan_count} NaN and {inf_count} infinite values, replacing with zeros")
                X = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)

            # Convert model_type to lowercase for case-insensitive comparison
            if model_type is not None:
                model_type = model_type.lower()

            # Special handling for TFT model which expects a dictionary
            if model_type == 'tft':
                return ModelInputAdapter._adapt_for_tft(X, target_shape)

            # Special handling for tree-based models (XGBoost, LightGBM)
            if model_type in ['xgboost', 'lightgbm']:
                return ModelInputAdapter._adapt_for_tree_model(X, target_shape)

            # Default handling for sequence models (LSTM, GRU, etc.)
            return ModelInputAdapter._adapt_for_sequence_model(X, target_shape)

        except Exception as e:
            logger.error(f"Unexpected error in adapt_input: {str(e)}")
            return None

    @staticmethod
    def _adapt_for_sequence_model(X: np.ndarray, target_shape: Tuple[int, int]) -> Optional[np.ndarray]:
        """
        Adapt input data for sequence models like LSTM, GRU.

        Args:
            X: Input data
            target_shape: Target shape (seq_len, features)

        Returns:
            Adapted input data or None if adaptation fails
        """
        try:
            # Validate target shape has 2 dimensions (seq_len, features)
            if len(target_shape) != 2:
                logger.error(f"Target shape for sequence model must have 2 dimensions (seq_len, features), got {target_shape}")
                return None

            target_seq_len, target_features = target_shape

            # Handle 1D input (single sample, features)
            if len(X.shape) == 1:
                features = X.shape[0]
                logger.debug(f"Reshaping 1D input with shape {X.shape} to (1, 1, {features})")
                X = X.reshape(1, 1, features)

            # Handle 2D input (batch_size, features)
            elif len(X.shape) == 2:
                batch_size, features = X.shape
                logger.debug(f"Reshaping 2D input with shape {X.shape} to ({batch_size}, 1, {features})")
                X = X.reshape(batch_size, 1, features)

            # Handle unexpected dimensions
            elif len(X.shape) != 3:
                logger.error(f"Expected 1D, 2D, or 3D input for sequence model, got shape {X.shape}")
                # Try to reshape if possible
                try:
                    X = X.reshape(1, -1, X.shape[-1])
                    logger.warning(f"Attempted to reshape input to {X.shape}, but this may not be correct")
                except Exception as e:
                    logger.error(f"Failed to reshape input: {str(e)}")
                    return None

            # Now X should be 3D with shape (batch_size, seq_len, features)
            batch_size, seq_len, features = X.shape

            logger.debug(f"Adapting sequence input from shape {X.shape} to (batch_size, {target_seq_len}, {target_features})")

            # Handle sequence length mismatch
            if seq_len > target_seq_len:
                # Truncate sequence (take the most recent data)
                X = X[:, -target_seq_len:, :]
                logger.debug(f"Truncated sequence to {X.shape}")
            elif seq_len < target_seq_len:
                # Pad sequence with zeros at the beginning (older data)
                padding = np.zeros((batch_size, target_seq_len - seq_len, features))
                X = np.concatenate([padding, X], axis=1)
                logger.debug(f"Padded sequence to {X.shape}")

            # After handling sequence length, get the new shape
            batch_size, seq_len, features = X.shape

            # Handle feature dimension mismatch
            if features > target_features:
                # Select subset of features
                X = X[:, :, :target_features]
                logger.debug(f"Selected subset of features to {X.shape}")
            elif features < target_features:
                # Pad features with zeros
                padding = np.zeros((batch_size, seq_len, target_features - features))
                X = np.concatenate([X, padding], axis=2)
                logger.debug(f"Padded features to {X.shape}")

            # Final validation
            final_shape = X.shape
            expected_shape = (batch_size, target_seq_len, target_features)

            if final_shape != expected_shape:
                logger.warning(f"Final shape {final_shape} does not match expected shape {expected_shape}")

                # Try to fix the shape if possible
                try:
                    X = X.reshape(expected_shape)
                    logger.info(f"Reshaped input to match expected shape {expected_shape}")
                except Exception as e:
                    logger.error(f"Failed to reshape to expected shape: {str(e)}")
                    # Continue anyway, as the model might be able to handle it

            return X

        except Exception as e:
            logger.error(f"Error during sequence adaptation: {str(e)}")
            return None

    @staticmethod
    def _adapt_for_tree_model(X: np.ndarray, target_shape: Tuple[int, ...]) -> Optional[np.ndarray]:
        """
        Adapt input data for tree-based models like XGBoost, LightGBM.
        These models typically expect 2D input (batch_size, features).

        Args:
            X: Input data
            target_shape: Target shape (seq_len, features) or (features,)

        Returns:
            Adapted input data or None if adaptation fails
        """
        try:
            # Validate target shape
            if not target_shape or len(target_shape) not in [1, 2]:
                logger.error(f"Invalid target shape for tree model: {target_shape}. Expected (features,) or (seq_len, features)")
                return None

            # Handle different input formats
            if len(X.shape) == 3:  # (batch_size, seq_len, features)
                batch_size, seq_len, features = X.shape

                # For tree models, we need to flatten the sequence dimension
                if len(target_shape) == 2:
                    target_seq_len, target_features = target_shape
                    total_features = target_seq_len * target_features
                elif len(target_shape) == 1:
                    total_features = target_shape[0]
                    # If we need to calculate target_seq_len and target_features
                    if seq_len > 0 and total_features % seq_len == 0:
                        target_seq_len = seq_len  # Keep original sequence length
                        target_features = total_features // seq_len
                    else:
                        # Can't divide evenly, just use the total as features
                        target_seq_len = 1
                        target_features = total_features
                else:
                    logger.error(f"Invalid target shape for tree model: {target_shape}")
                    return None

                logger.debug(f"Flattening 3D input of shape {X.shape} to 2D for tree model")

                # For tree models, we don't need to adapt sequence length first
                # Just flatten the sequence dimension directly
                try:
                    X = X.reshape(batch_size, -1)
                    logger.debug(f"Reshaped to {X.shape}")
                except Exception as e:
                    logger.error(f"Failed to reshape 3D input to 2D: {str(e)}")
                    return None

                # If the flattened feature count doesn't match the target, pad or truncate
                if X.shape[1] > total_features:
                    X = X[:, :total_features]
                    logger.debug(f"Truncated features to {X.shape}")
                elif X.shape[1] < total_features:
                    padding = np.zeros((batch_size, total_features - X.shape[1]))
                    X = np.concatenate([X, padding], axis=1)
                    logger.debug(f"Padded features to {X.shape}")

                return X

            elif len(X.shape) == 2:  # Already 2D (batch_size, features)
                batch_size, features = X.shape

                # If target_shape is a tuple with 2 elements, it's (seq_len, features)
                if len(target_shape) == 2:
                    target_seq_len, target_features = target_shape
                    total_features = target_seq_len * target_features
                else:
                    # If target_shape is a single value, it's just the feature count
                    total_features = target_shape[0]

                logger.debug(f"Adapting 2D input of shape {X.shape} for tree model to feature count {total_features}")

                # Adjust feature count if needed
                if features > total_features:
                    X = X[:, :total_features]
                    logger.debug(f"Truncated features to {X.shape}")
                elif features < total_features:
                    padding = np.zeros((batch_size, total_features - features))
                    X = np.concatenate([X, padding], axis=1)
                    logger.debug(f"Padded features to {X.shape}")

                return X

            elif len(X.shape) == 1:  # 1D input (features)
                features = X.shape[0]

                # Determine total features needed
                if len(target_shape) == 2:
                    target_seq_len, target_features = target_shape
                    total_features = target_seq_len * target_features
                else:
                    total_features = target_shape[0]

                logger.debug(f"Reshaping 1D input with shape {X.shape} to (1, {features})")
                X = X.reshape(1, -1)  # Reshape to 2D (1, features)

                # Adjust feature count if needed
                if features > total_features:
                    X = X[:, :total_features]
                    logger.debug(f"Truncated features to {X.shape}")
                elif features < total_features:
                    padding = np.zeros((1, total_features - features))
                    X = np.concatenate([X, padding], axis=1)
                    logger.debug(f"Padded features to {X.shape}")

                return X
            else:
                logger.error(f"Unexpected input shape for tree model: {X.shape}")
                # Try to reshape to 2D if possible
                try:
                    X = X.reshape(1, -1)
                    logger.warning(f"Reshaped input to {X.shape}")
                    return X
                except Exception as e:
                    logger.error(f"Failed to reshape input: {str(e)}")
                    return None

        except Exception as e:
            logger.error(f"Error adapting input for tree model: {str(e)}")
            return None

    @staticmethod
    def _adapt_for_tft(X: np.ndarray, target_shape: Tuple[int, int]) -> Optional[Dict[str, np.ndarray]]:
        """
        Adapt input data for Temporal Fusion Transformer model.

        This method supports both PyTorch and TensorFlow TFT implementations:
        - PyTorch TFT expects a dictionary with 'encoder_cont' and 'encoder_target' keys
        - TensorFlow TFT expects a dictionary with 'input_1' key

        Args:
            X: Input data
            target_shape: Target shape (seq_len, features)

        Returns:
            Dictionary with adapted input data or None if adaptation fails
        """
        try:
            # Validate target shape has 2 dimensions (seq_len, features)
            if len(target_shape) != 2:
                logger.error(f"Target shape for TFT model must have 2 dimensions (seq_len, features), got {target_shape}")
                return None

            # Ensure there are at least 2 features (one for continuous variables, one for target)
            if target_shape[1] < 2:
                logger.error(f"TFT model requires at least 2 features (one for continuous variables, one for target), got {target_shape[1]}")
                return None

            # First adapt the input as a sequence model
            adapted_X = ModelInputAdapter._adapt_for_sequence_model(X, target_shape)
            if adapted_X is None:
                logger.error("Failed to adapt input for TFT model using sequence adapter")
                return None

            # Get the shape of the adapted input
            batch_size, seq_len, features = adapted_X.shape

            # Validate the adapted input has the expected shape
            if features < 2:
                logger.error(f"TFT model requires at least 2 features, got {features}")
                return None

            # For TFT, we need to create a dictionary with encoder inputs
            # Assume the last feature is the target variable
            try:
                encoder_cont = adapted_X[:, :, :-1]  # All features except the last one
                encoder_target = adapted_X[:, :, -1]  # Last feature as target

                # Ensure encoder_target is 2D (batch_size, seq_len)
                if len(encoder_target.shape) == 1:
                    encoder_target = encoder_target.reshape(-1, 1)

                # Reshape encoder_target if needed
                if len(encoder_target.shape) == 3 and encoder_target.shape[2] == 1:
                    encoder_target = encoder_target.reshape(batch_size, seq_len)

                logger.debug(f"Created TFT input with encoder_cont shape {encoder_cont.shape} and encoder_target shape {encoder_target.shape}")

                # Return a dictionary that supports both PyTorch and TensorFlow implementations
                result = {
                    # For PyTorch TFT
                    'encoder_cont': encoder_cont,
                    'encoder_target': encoder_target,
                    # For TensorFlow TFT
                    'input_1': adapted_X  # Full input tensor for TensorFlow implementation
                }

                # Add additional keys for PyTorch TFT if needed
                result['encoder_cat'] = np.zeros((batch_size, seq_len, 1), dtype=np.int64)  # Empty categorical features
                result['decoder_cont'] = np.zeros((batch_size, 1, encoder_cont.shape[2]), dtype=np.float32)  # Empty decoder inputs
                result['decoder_target'] = np.zeros((batch_size, 1), dtype=np.float32)  # Empty decoder targets

                return result

            except Exception as e:
                logger.error(f"Error creating TFT input dictionary: {str(e)}")

                # Fallback to a simpler dictionary if the above fails
                return {
                    'input_1': adapted_X  # Just return the adapted input
                }

        except Exception as e:
            logger.error(f"Error adapting input for TFT model: {str(e)}")
            return None
