# Trading Operations

## Trading Bot Architecture

The trading system operates 5 independent trading bots, each connected to a specific MT5 terminal and using a dedicated model.

## Trading Bot Class (`trading/bot.py`)

### Core Components
```python
class TradingBot:
    def __init__(
        self,
        config_manager: ConfigurationManager,
        terminal_id: str,
        error_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        thread_manager: <PERSON><PERSON><PERSON>Manager,
        mt5_manager: MT5ConnectionManager,
        visualizer: ProgressVisualizer,
        memory_manager
    ):
        self.terminal_id = terminal_id
        self.config = config_manager.get_config()
        self.model_manager = ModelManager(config_manager, terminal_id)
        self.signal_generator = SignalGenerator(config_manager)
        self.executor = TradeExecutor(mt5_manager, terminal_id)
        self.strategy = TradingStrategy(config_manager)
```

### Bot Lifecycle
1. **Initialization**: Load configuration and initialize components
2. **Model Loading**: Load trained models for the terminal
3. **Data Collection**: Collect real-time market data
4. **Signal Generation**: Generate trading signals from model predictions
5. **Risk Assessment**: Evaluate trade risk and position sizing
6. **Trade Execution**: Execute trades through MT5
7. **Monitoring**: Monitor open positions and performance

## Signal Generation (`trading/signal_generator.py`)

### Signal Generation Process
```python
class SignalGenerator:
    def generate_signal(
        self,
        predictions: np.ndarray,
        current_price: float,
        confidence_threshold: float = 0.65
    ) -> Dict[str, Any]:
        
        # Calculate prediction confidence
        confidence = self._calculate_confidence(predictions)
        
        if confidence < confidence_threshold:
            return {"action": "HOLD", "confidence": confidence}
        
        # Determine trade direction
        predicted_price = predictions[-1]
        price_change = (predicted_price - current_price) / current_price
        
        if price_change > 0.001:  # 0.1% threshold
            return {
                "action": "BUY",
                "confidence": confidence,
                "predicted_price": predicted_price,
                "expected_return": price_change
            }
        elif price_change < -0.001:
            return {
                "action": "SELL",
                "confidence": confidence,
                "predicted_price": predicted_price,
                "expected_return": abs(price_change)
            }
        else:
            return {"action": "HOLD", "confidence": confidence}
```

### Signal Validation
- **Confidence threshold**: Minimum prediction confidence (65%)
- **Price change threshold**: Minimum expected price movement (0.1%)
- **Market conditions**: Check spread, volatility, and liquidity
- **Risk limits**: Verify against position and risk limits

## Trade Execution (`trading/executor.py`)

### Trade Execution Process
```python
class TradeExecutor:
    def execute_trade(
        self,
        terminal_id: str,
        signal: Dict[str, Any]
    ) -> bool:
        
        # Validate trade signal
        if not self._validate_signal(signal):
            return False
        
        # Calculate position size
        position_size = self._calculate_position_size(signal)
        
        # Set stop loss and take profit
        stop_loss = self._calculate_stop_loss(signal)
        take_profit = self._calculate_take_profit(signal)
        
        # Execute trade
        result = self.mt5_manager.place_order(
            terminal_id=terminal_id,
            symbol=self.symbol,
            order_type=signal["action"],
            volume=position_size,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        return result.success
```

### Order Management
- **Market orders**: Immediate execution at current market price
- **Stop loss orders**: Automatic loss limitation
- **Take profit orders**: Automatic profit taking
- **Position monitoring**: Continuous position tracking
- **Order modification**: Dynamic stop loss and take profit adjustment

## Risk Management

### Position Sizing
```python
def calculate_position_size(
    self,
    account_balance: float,
    risk_per_trade: float,
    stop_loss_pips: int,
    pip_value: float
) -> float:
    
    risk_amount = account_balance * risk_per_trade
    position_size = risk_amount / (stop_loss_pips * pip_value)
    
    # Apply maximum position size limits
    max_position = account_balance * 0.1  # 10% max
    return min(position_size, max_position)
```

### Risk Parameters
- **Risk per trade**: 1% of account balance
- **Maximum positions**: 2 concurrent positions
- **Stop loss**: 200 pips
- **Take profit**: 400 pips (2:1 risk-reward ratio)
- **Maximum daily loss**: $50
- **Maximum daily trades**: 5
- **Cooldown period**: 600 seconds between trades

### Risk Monitoring
- **Real-time P&L tracking**: Continuous profit/loss monitoring
- **Drawdown monitoring**: Maximum drawdown alerts
- **Exposure limits**: Total exposure across all positions
- **Correlation checks**: Avoid correlated positions

## Trading Strategy (`trading/strategy.py`)

### Strategy Implementation
```python
class TradingStrategy:
    def __init__(self, config):
        self.symbol = config.strategy.symbol
        self.timeframes = config.strategy.timeframes
        self.risk_per_trade = config.strategy.risk_per_trade
        self.max_positions = config.strategy.max_positions
        self.stop_loss_pips = config.strategy.stop_loss_pips
        self.take_profit_pips = config.strategy.take_profit_pips
    
    def should_enter_trade(self, signal: Dict) -> bool:
        # Check signal strength
        if signal["confidence"] < self.confidence_threshold:
            return False
        
        # Check market conditions
        if not self._check_market_conditions():
            return False
        
        # Check position limits
        if self._get_open_positions() >= self.max_positions:
            return False
        
        # Check daily limits
        if self._check_daily_limits():
            return False
        
        return True
```

### Strategy Rules
1. **Signal-based entry**: Enter trades based on model predictions
2. **Confidence filtering**: Only trade high-confidence signals
3. **Market condition checks**: Verify favorable market conditions
4. **Position limits**: Respect maximum position limits
5. **Time-based filters**: Avoid trading during low-liquidity periods
6. **Correlation filters**: Avoid highly correlated positions

## Performance Monitoring

### Real-time Metrics
- **P&L**: Real-time profit and loss
- **Win rate**: Percentage of winning trades
- **Average trade duration**: Time in market per trade
- **Sharpe ratio**: Risk-adjusted returns
- **Maximum drawdown**: Largest peak-to-trough decline

### Performance Tracking
```python
class PerformanceMonitor:
    def update_metrics(self, trade_result: Dict):
        self.total_trades += 1
        self.total_pnl += trade_result["pnl"]
        
        if trade_result["pnl"] > 0:
            self.winning_trades += 1
        
        self.win_rate = self.winning_trades / self.total_trades
        self.average_pnl = self.total_pnl / self.total_trades
        
        # Update drawdown
        self.current_balance += trade_result["pnl"]
        if self.current_balance > self.peak_balance:
            self.peak_balance = self.current_balance
        
        current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
```

## Terminal-Specific Operations

### Terminal Assignments
- **Terminal 1**: LSTM model trading
- **Terminal 2**: GRU model trading
- **Terminal 3**: TFT model trading
- **Terminal 4**: XGBoost model trading
- **Terminal 5**: LightGBM model trading

### Independent Operation
Each terminal operates independently:
- Separate model predictions
- Independent risk management
- Isolated position tracking
- Individual performance metrics

### Coordination
Limited coordination between terminals:
- Global risk limits
- Market condition sharing
- Performance comparison
- Resource allocation
