#!/usr/bin/env python
"""
PyTorch-specific training utilities for neural network models.

This script provides specialized training functions for PyTorch models
including LSTM, GRU, Transformer, and TFT models. It includes:

1. PyTorch-specific optimizations
2. Advanced monitoring and logging
3. Model checkpointing and early stopping
4. TensorBoard integration for PyTorch
5. Memory optimization for large datasets

Usage:
    python train_with_pytorch.py [--model MODEL_NAME] [--config CONFIG_PATH]
"""

import sys
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Any, Tuple

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pytorch_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('pytorch_training')

def safe_print_summary(model, print_fn=None):
    """Safely print PyTorch model summary, handling Unicode encoding issues."""
    try:
        if print_fn is None:
            print_fn = print

        # For PyTorch models, print basic information
        if hasattr(model, 'parameters'):
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            print_fn(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
            print_fn(f"Model architecture: {model}")
        else:
            print_fn(f"Model: {model}")
    except Exception as e:
        print_fn(f"Could not print model summary: {str(e)}")

# Import PyTorch with error handling
try:
    import torch
    logger.info(f"PyTorch version: {torch.__version__}")
except ImportError as e:
    logger.error(f"PyTorch not available: {str(e)}")
    sys.exit(1)

# Import project modules
try:
    from train_models import ModelTrainer
except ImportError as e:
    logger.error(f"Failed to import required modules: {str(e)}")
    sys.exit(1)

class PyTorchTrainer(ModelTrainer):
    """Specialized trainer for PyTorch models."""

    def __init__(self, config_path: Optional[str] = None):
        """Initialize PyTorch trainer."""
        super().__init__(config_path)

        # Configure PyTorch
        self._configure_pytorch()

        # Create TensorBoard log directory
        self.tensorboard_dir = self.output_dir / "tensorboard"
        self.tensorboard_dir.mkdir(parents=True, exist_ok=True)

        logger.info("PyTorch trainer initialized")

    def _configure_pytorch(self):
        """Configure PyTorch settings for optimal performance."""
        try:
            # Enhanced GPU detection and configuration
            logger.info("Configuring PyTorch for optimal performance...")

            # Check CUDA availability
            cuda_available = torch.cuda.is_available()
            device_count = torch.cuda.device_count() if cuda_available else 0

            logger.info("PyTorch GPU Detection:")
            logger.info(f"  - CUDA available: {cuda_available}")
            logger.info(f"  - GPU devices: {device_count}")

            if cuda_available:
                for i in range(device_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    logger.info(f"  - GPU {i}: {gpu_name}")

                try:
                    # Test GPU functionality
                    device = torch.device('cuda')
                    test_tensor = torch.tensor([1.0, 2.0, 3.0]).to(device)
                    result = torch.sum(test_tensor)
                    logger.info(f"GPU test successful: {result.item()}")

                    # Set device for training
                    self.device = device
                    logger.info("Using GPU for training")

                except Exception as gpu_error:
                    logger.error(f"Error configuring GPU: {gpu_error}")
                    logger.warning("Falling back to CPU")
                    self.device = torch.device('cpu')
            else:
                logger.info("No GPUs found, using CPU")
                logger.info("PyTorch GPU troubleshooting:")
                logger.info("  - Try: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
                logger.info("  - Check CUDA installation")
                logger.info("  - Check GPU drivers")
                self.device = torch.device('cpu')

            # Configure automatic mixed precision if GPU available
            try:
                if cuda_available:
                    self.use_amp = True
                    logger.info("Enabled automatic mixed precision training")
                else:
                    self.use_amp = False
                    logger.info("Skipping mixed precision (CPU mode)")
            except Exception as e:
                logger.warning(f"Could not enable mixed precision: {str(e)}")
                self.use_amp = False

        except Exception as e:
            logger.warning(f"Error configuring PyTorch: {str(e)}")
            self.device = torch.device('cpu')
            self.use_amp = False

    def create_pytorch_training_config(self, model_name: str, model_config: Any) -> Dict[str, Any]:
        """Create PyTorch training configuration.

        Args:
            model_name: Name of the model
            model_config: Model configuration

        Returns:
            Dictionary with training configuration
        """
        try:
            # Create checkpoint directory
            checkpoint_dir = self.output_dir / "checkpoints" / model_name
            checkpoint_dir.mkdir(parents=True, exist_ok=True)

            # Create TensorBoard log directory
            tensorboard_dir = self.tensorboard_dir / model_name
            tensorboard_dir.mkdir(parents=True, exist_ok=True)

            training_config = {
                'checkpoint_dir': checkpoint_dir,
                'tensorboard_dir': tensorboard_dir,
                'monitor_metric': getattr(model_config, 'monitor_metric', 'val_loss'),
                'save_best_only': getattr(model_config, 'save_best_only', True),
                'early_stopping': getattr(model_config, 'early_stopping', True),
                'patience': getattr(model_config, 'patience', 10),
                'learning_rate': getattr(model_config, 'learning_rate', 0.001),
                'use_amp': self.use_amp,
                'device': self.device,
                'csv_log_file': str(self.output_dir / f"{model_name}_training_log.csv")
            }

            logger.info(f"Created PyTorch training configuration for {model_name}")
            return training_config

        except Exception as e:
            logger.error(f"Error creating training configuration: {str(e)}")
            return {}

    def prepare_pytorch_data(self, data: Dict[str, Any], batch_size: int) -> Tuple[Any, Any, Any]:
        """Prepare PyTorch data loaders for training.

        Args:
            data: Prepared model data
            batch_size: Batch size for training

        Returns:
            Tuple of (train_loader, val_loader, test_loader)
        """
        try:
            from torch.utils.data import DataLoader, TensorDataset

            # Convert to PyTorch tensors
            X_train_tensor = torch.FloatTensor(data['X_train']).to(self.device)
            y_train_tensor = torch.FloatTensor(data['y_train']).to(self.device)
            X_val_tensor = torch.FloatTensor(data['X_val']).to(self.device)
            y_val_tensor = torch.FloatTensor(data['y_val']).to(self.device)
            X_test_tensor = torch.FloatTensor(data['X_test']).to(self.device)
            y_test_tensor = torch.FloatTensor(data['y_test']).to(self.device)

            # Create datasets
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

            # Create data loaders
            train_loader = DataLoader(
                train_dataset,
                batch_size=batch_size,
                shuffle=True,
                num_workers=0,  # Set to 0 for Windows compatibility
                pin_memory=torch.cuda.is_available()
            )

            val_loader = DataLoader(
                val_dataset,
                batch_size=batch_size,
                shuffle=False,
                num_workers=0,
                pin_memory=torch.cuda.is_available()
            )

            test_loader = DataLoader(
                test_dataset,
                batch_size=batch_size,
                shuffle=False,
                num_workers=0,
                pin_memory=torch.cuda.is_available()
            )

            logger.info(f"Created PyTorch data loaders with batch size {batch_size}")
            return train_loader, val_loader, test_loader

        except Exception as e:
            logger.error(f"Error preparing PyTorch data: {str(e)}")
            return None, None, None

    def train_pytorch_model(self, model_name: str, model_config: Any,
                           training_data: Dict[str, Any]) -> Dict[str, Any]:
        """Train a PyTorch model with advanced features.

        Args:
            model_name: Name of the model
            model_config: Model configuration
            training_data: Training data

        Returns:
            Training results dictionary
        """
        try:
            logger.info(f"Starting PyTorch training for {model_name}")
            start_time = datetime.now()

            # Prepare model-specific data
            model_data = self.prepare_model_data(training_data, model_config)
            if model_data is None:
                return {'success': False, 'error': 'Failed to prepare model data'}

            # Note: Data loaders not needed for current model training approach

            # Import and initialize model
            model_class = self._get_model_class(model_config.model_type)
            if model_class is None:
                return {'success': False, 'error': f'Unknown model type: {model_config.model_type}'}

            # Create model configuration
            config_dict = {
                'model_name': model_name,
                'model_type': model_config.model_type,
                'timeframe': training_data['timeframe'],
                'terminal_id': '1',
                'models_base_path': self.config.models_base_path,
                'model_filename': f"{model_name}_model",
                'input_dim': model_data['X_train'].shape[-1] if len(model_data['X_train'].shape) > 1 else 1,
                'output_dim': 1,
                'FEATURE_COLUMNS': model_data['feature_columns'],
                **{k: v for k, v in model_config.__dict__.items() if not k.startswith('_')}
            }

            # Initialize and build model
            model = model_class(config_dict)
            model.build()

            # Train model using PyTorch
            if hasattr(model, 'train_with_validation'):
                # Use model's custom training method with validation
                training_result = model.train_with_validation(
                    X_train=model_data['X_train'],
                    y_train=model_data['y_train'],
                    X_val=model_data['X_val'],
                    y_val=model_data['y_val'],
                    epochs=model_config.epochs,
                    batch_size=model_config.batch_size,
                    verbose=1
                )
            else:
                # Use standard training method
                training_result = model.train(
                    X=model_data['X_train'],
                    y=model_data['y_train'],
                    validation_data=(model_data['X_val'], model_data['y_val']),
                    epochs=model_config.epochs,
                    batch_size=model_config.batch_size,
                    verbose=1
                )

            # Evaluate on test data
            test_predictions = model.predict(model_data['X_test'])
            test_targets = model_data['y_test']
            test_loss = None

            # Calculate test metrics
            test_metrics = self._calculate_test_metrics(test_targets, test_predictions.flatten())
            if test_loss is not None:
                test_metrics['test_loss'] = float(test_loss) if isinstance(test_loss, (int, float)) else float(test_loss[0])

            # Save model
            save_success = model.save()

            end_time = datetime.now()
            training_duration = (end_time - start_time).total_seconds()

            result = {
                'success': True,
                'model_name': model_name,
                'training_metrics': training_result,
                'test_metrics': test_metrics,
                'training_duration': training_duration,
                'model_saved': save_success,
                'model_path': str(model.model_path) if hasattr(model, 'model_path') else None,
                'tensorboard_dir': str(self.tensorboard_dir / model_name),
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }

            logger.info(f"PyTorch training completed for {model_name} in {training_duration:.2f} seconds")
            return result

        except Exception as e:
            logger.error(f"Error in PyTorch training for {model_name}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'model_name': model_name,
                'error': str(e),
                'end_time': datetime.now().isoformat()
            }


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train PyTorch models with advanced features')
    parser.add_argument('--model', type=str, help='Specific model to train')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--tensorboard', action='store_true', help='Launch TensorBoard after training')

    # Custom training parameters
    parser.add_argument('--epochs', type=int, help='Override epochs for training')
    parser.add_argument('--batch-size', type=int, help='Override batch size for training')
    parser.add_argument('--learning-rate', type=float, help='Override learning rate for training')
    parser.add_argument('--patience', type=int, help='Override patience for early stopping')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Trading symbol for training data')
    parser.add_argument('--timeframe', type=str, default='M5', help='Timeframe for training data')

    return parser.parse_args()


def main():
    """Main function."""
    try:
        args = parse_arguments()

        logger.info("Starting PyTorch training session")

        # Prepare custom parameters from command line arguments
        custom_params = {}
        if args.epochs is not None:
            custom_params['epochs'] = args.epochs
        if args.batch_size is not None:
            custom_params['batch_size'] = args.batch_size
        if args.learning_rate is not None:
            custom_params['learning_rate'] = args.learning_rate
        if args.patience is not None:
            custom_params['patience'] = args.patience

        # Initialize trainer with custom parameters
        trainer = PyTorchTrainer(args.config)
        trainer.custom_params = custom_params

        # Load training data with custom symbol and timeframe
        training_data = trainer.load_training_data(args.symbol, args.timeframe)
        if training_data is None:
            logger.error("Failed to load training data")
            sys.exit(1)

        if args.model:
            # Train specific model
            if args.model not in trainer.config.models:
                logger.error(f"Model {args.model} not found in configuration")
                sys.exit(1)

            model_config = trainer.config.models[args.model]
            result = trainer.train_pytorch_model(args.model, model_config, training_data)

            if result['success']:
                logger.info(f"Successfully trained {args.model}")
                if args.tensorboard and 'tensorboard_dir' in result:
                    logger.info(f"TensorBoard logs available at: {result['tensorboard_dir']}")
                    logger.info("Run: tensorboard --logdir=" + result['tensorboard_dir'])
            else:
                logger.error(f"Failed to train {args.model}: {result.get('error', 'Unknown error')}")
                sys.exit(1)
        else:
            # Train all PyTorch models
            pytorch_models = [
                name for name, config in trainer.config.models.items()
                if config.model_type.lower() in ['lstm', 'gru', 'tft', 'transformer', 'pytorch']
            ]

            if not pytorch_models:
                logger.warning("No PyTorch models found in configuration")
                sys.exit(0)

            logger.info(f"Training PyTorch models: {pytorch_models}")

            results = {}
            for model_name in pytorch_models:
                model_config = trainer.config.models[model_name]
                result = trainer.train_pytorch_model(model_name, model_config, training_data)
                results[model_name] = result

                if result['success']:
                    logger.info(f"Successfully trained {model_name}")
                else:
                    logger.error(f"Failed to train {model_name}: {result.get('error', 'Unknown error')}")

            # Print summary
            successful = sum(1 for r in results.values() if r['success'])
            total = len(results)
            logger.info(f"Training completed: {successful}/{total} models successful")

            if args.tensorboard:
                logger.info(f"TensorBoard logs available at: {trainer.tensorboard_dir}")
                logger.info(f"Run: tensorboard --logdir={trainer.tensorboard_dir}")

    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
