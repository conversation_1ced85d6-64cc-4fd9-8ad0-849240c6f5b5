"""
Temporal Fusion Transformer (TFT) model implementation for time series prediction using PyTorch.

This module provides a TFT implementation using PyTorch/PyTorch-Forecasting.
"""
import logging
from typing import Dict, Optional, Any, Union
import os
from pathlib import Path
import numpy as np
import pandas as pd

# Set up logger
logger = logging.getLogger(__name__)

# Import PyTorch and PyTorch Forecasting
try:
    import torch

    # Handle different PyTorch Lightning versions
    try:
        import lightning.pytorch as pl
        from lightning.pytorch.callbacks import EarlyStopping, LearningRateMonitor
        from lightning.pytorch.loggers import TensorBoardLogger
        logger.info("Using Lightning 2.x imports")
    except ImportError:
        import pytorch_lightning as pl
        from pytorch_lightning.callbacks import EarlyStopping, LearningRateMonitor
        from pytorch_lightning.loggers import TensorBoardLogger
        logger.info("Using PyTorch Lightning 1.x imports")

    from pytorch_forecasting import TemporalFusionTransformer, TimeSeriesDataSet
    from pytorch_forecasting.metrics import QuantileLoss
    logger.info("PyTorch and PyTorch Forecasting available. Using full TFT implementation.")
except ImportError as e:
    logger.error(f"PyTorch or PyTorch Forecasting not available: {e}")
    raise ImportError("PyTorch and PyTorch Forecasting are required for TFT model. Please install them with: pip install torch pytorch-lightning pytorch-forecasting")

from .base_model import BaseModel

class TFTModel(BaseModel):
    """
    Temporal Fusion Transformer model implementation using PyTorch.

    This class provides a TFT model using PyTorch/PyTorch-Forecasting.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the TFT model.

        Args:
            config: Model configuration dictionary
        """
        # Set default model name if not provided
        if 'model_name' not in config:
            config['model_name'] = 'tft'

        # Set model type for proper file extension handling
        if 'model_type' not in config:
            config['model_type'] = 'tft'

        super().__init__(config)

        # Initialize PyTorch configuration
        self._init_pytorch_config()

        # Set tensor core precision for better GPU utilization
        self._configure_tensor_cores()

        # Suppress TensorFlow warnings
        self._suppress_tensorflow_warnings()

        # Initialize model components
        self.model = None
        self.scaler = None

    def _configure_tensor_cores(self):
        """Configure tensor cores for optimal GPU performance."""
        try:
            import torch
            if torch.cuda.is_available():
                # Set float32 matmul precision for tensor cores
                torch.set_float32_matmul_precision('medium')
                logger.info(f"TFT model '{self.model_name}': Configured tensor cores for optimal GPU performance")
        except Exception as e:
            logger.warning(f"TFT model '{self.model_name}': Could not configure tensor cores: {str(e)}")

    def _suppress_tensorflow_warnings(self):
        """Suppress TensorFlow deprecation warnings."""
        try:
            import os
            import warnings
            import logging

            # Suppress TensorFlow warnings at environment level
            os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress all TensorFlow messages except errors
            os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # Disable oneDNN optimizations warnings

            # Suppress TensorFlow logging
            tf_logger = logging.getLogger('tensorflow')
            tf_logger.setLevel(logging.ERROR)

            # Filter specific deprecation warnings
            warnings.filterwarnings('ignore', category=DeprecationWarning, module='tensorflow')
            warnings.filterwarnings('ignore', category=FutureWarning, module='tensorflow')
            warnings.filterwarnings('ignore', message='.*tf.losses.sparse_softmax_cross_entropy.*')
            warnings.filterwarnings('ignore', message='.*deprecated.*', module='tensorflow')
            warnings.filterwarnings('ignore', message='.*tf.compat.v1.*')

            logger.debug(f"TFT model '{self.model_name}': Suppressed TensorFlow warnings")
        except Exception as e:
            logger.warning(f"TFT model '{self.model_name}': Could not suppress TensorFlow warnings: {str(e)}")

    def _init_pytorch_config(self):
        """Initialize configuration for PyTorch implementation"""
        # Training parameters - check both 'epochs' and 'max_epochs' for compatibility
        self.epochs = self.config.get('epochs', self.config.get('max_epochs', 10))
        self.batch_size = self.config.get('batch_size', 64)
        self.patience = self.config.get('patience', 5)
        self.learning_rate = self.config.get('learning_rate', 1e-3)
        self.gradient_clip_val = self.config.get('gradient_clip_val', 0.1)
        self.num_workers = self.config.get('num_workers', 0)
        self.accelerator = self.config.get('accelerator', 'auto')
        self.devices = self.config.get('devices', 'auto')

        # Memory management parameters - increased limits for M5 timeframe
        self.max_samples_per_dataset = self.config.get('max_samples_per_dataset', 100000)  # Increased for M5 timeframe
        self.enable_memory_optimization = self.config.get('enable_memory_optimization', False)  # Disabled by default for better accuracy

        # Ensure device setting is compatible with accelerator
        if self.accelerator == 'cpu' and self.devices == 'auto':
            self.devices = 1
        elif self.accelerator == 'gpu' and self.devices == 'auto':
            self.devices = 1

        # Determine specific device for loading etc.
        self.device = self._get_device()

        # Loss function configuration
        self.loss_config = self.config.get('loss_config', {'class': 'QuantileLoss'})

        # Optimizer configuration
        self.optimizer_config = self.config.get('optimizer_config', {'class': 'Adam'})

        # Dataset placeholder
        self.training_dataset = None
        self.trainer = None

    def _get_device(self) -> Optional[Any]:
        """Determines the appropriate device (GPU or CPU) using PyTorch."""
        # This logic should ideally align with how PTL determines device from accelerator/devices config
        use_gpu = self.config.get('use_gpu', True) # Legacy key, prefer accelerator/devices
        if self.accelerator == 'gpu' or (self.accelerator == 'auto' and torch.cuda.is_available() and use_gpu):
            if torch.cuda.is_available():
                logger.info(f"CUDA available for model '{self.model_name}'. Using GPU.")
                # PTL handles device index, return generic cuda device
                return torch.device("cuda")
            else:
                logger.warning(f"Accelerator set to '{self.accelerator}' but CUDA not available for '{self.model_name}'. Falling back to CPU.")
                return torch.device("cpu")
        else:
            logger.info(f"Using CPU for model '{self.model_name}'.")
            return torch.device("cpu")

    def _get_loss_instance(self) -> Optional[Any]:
        """Instantiates the loss function based on config."""
        loss_class_name = self.loss_config.get('class', 'QuantileLoss')
        loss_params = {k: v for k, v in self.loss_config.items() if k != 'class'}

        # Import and instantiate loss (add more as needed)
        if loss_class_name == "QuantileLoss":
            # QuantileLoss default quantiles are [0.02, 0.1, 0.25, 0.5, 0.75, 0.9, 0.98]
            return QuantileLoss(**loss_params)
        elif loss_class_name == "MSELoss":
            from torch.nn import MSELoss
            return MSELoss(**loss_params)
        elif loss_class_name == "L1Loss": # MAE
            from torch.nn import L1Loss
            return L1Loss(**loss_params)
        else:
            logger.warning(f"Loss class '{loss_class_name}' not recognized for '{self.model_name}'. Defaulting to QuantileLoss.")
            return QuantileLoss()

    def build(self, dataset=None) -> None:
        """Build the TFT model architecture.

        Args:
            dataset: Optional TimeSeriesDataSet for PyTorch implementation
        """
        self._build_pytorch(dataset)

    def _build_pytorch(self, dataset=None) -> None:
        """Build the PyTorch TFT model architecture."""
        if self.model is not None:
            logger.info(f"PyTorch TFT model ('{self.model_name}') already built.")
            return

        if dataset is None:
            # Cannot build without dataset parameters - this is expected during initialization
            logger.debug(f"TFT model '{self.model_name}' build deferred until TimeSeriesDataSet is available.")
            return

        try:
            loss_instance = self._get_loss_instance()

            # Instantiate TFT model using data parameters
            self.model = TemporalFusionTransformer.from_dataset(
                dataset,
                # Pass hyperparameters from config using .get() with defaults
                learning_rate=self.learning_rate,
                hidden_size=self.config.get('hidden_size', 16),
                attention_head_size=self.config.get('num_attention_heads', 4),
                dropout=self.config.get('dropout', 0.1),
                lstm_layers=self.config.get('lstm_layers', 1),
                loss=loss_instance,
                reduce_on_plateau_patience=self.config.get('reduce_on_plateau_patience', 4),
                log_interval=self.config.get('log_interval', -1) # Default PTL logging
            )
            self.model.to(self.device) # Ensure model is on the correct device after creation
            logger.info(f"PyTorch TFT model ('{self.model_name}') built successfully using dataset parameters.")

        except Exception as e:
            logger.error(f"Error building PyTorch TFT model '{self.model_name}': {str(e)}", exc_info=True)
            self.model = None # Ensure model is None if build fails
            raise



    def train(
        self,
        X: Union[np.ndarray, Any],
        y: Optional[np.ndarray] = None,
        validation_data: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Train the TFT model using PyTorch.

        Args:
            X: TimeSeriesDataSet for PyTorch implementation
            y: Target values (not used for PyTorch implementation)
            validation_data: Optional validation data
            **kwargs: Additional keyword arguments for model training

        Returns:
            Dict containing training metrics
        """
        # For PyTorch, X should be a TimeSeriesDataSet
        # y parameter is not used as targets are embedded in TimeSeriesDataSet
        return self._train_pytorch(X, validation_data, **kwargs)

    def train_with_validation(
        self,
        X_train: Union[np.ndarray, Dict[str, np.ndarray]],
        y_train: np.ndarray,
        X_val: Union[np.ndarray, Dict[str, np.ndarray]],
        y_val: np.ndarray,
        **kwargs
    ) -> Dict[str, Any]:
        """Train TFT model with validation data.

        This method converts the dictionary input from ModelInputAdapter to TimeSeriesDataSet
        format required by PyTorch Forecasting TFT.

        Args:
            X_train: Training features (dictionary from ModelInputAdapter)
            y_train: Training targets
            X_val: Validation features (dictionary from ModelInputAdapter)
            y_val: Validation targets
            **kwargs: Additional training parameters

        Returns:
            Dict containing training metrics
        """
        try:
            logger.info(f"Converting input data to TimeSeriesDataSet for TFT model '{self.model_name}'")

            # Convert dictionary input to TimeSeriesDataSet
            train_dataset = self._create_timeseries_dataset(X_train, y_train, is_training=True)
            val_dataset = self._create_timeseries_dataset(X_val, y_val, is_training=False, reference_dataset=train_dataset)

            # Filter out parameters that should not be passed to PyTorch Lightning Trainer
            filtered_kwargs = {}
            for key, value in kwargs.items():
                if key not in ['epochs', 'batch_size', 'patience', 'verbose']:
                    filtered_kwargs[key] = value

            # Train using the PyTorch method
            return self._train_pytorch(train_dataset, val_dataset, **filtered_kwargs)

        except Exception as e:
            logger.error(f"Error in TFT train_with_validation: {str(e)}", exc_info=True)
            raise

    def _create_timeseries_dataset(
        self,
        X: Union[np.ndarray, Dict[str, np.ndarray]],
        y: np.ndarray,
        is_training: bool = True,
        reference_dataset: Optional[Any] = None
    ) -> Any:
        """Create TimeSeriesDataSet from input data.

        Args:
            X: Input features (dictionary from ModelInputAdapter or numpy array)
            y: Target values
            is_training: Whether this is for training (True) or validation (False)
            reference_dataset: Reference dataset for validation (to maintain consistency)

        Returns:
            TimeSeriesDataSet for PyTorch Forecasting
        """
        try:
            # Handle dictionary input from ModelInputAdapter
            if isinstance(X, dict):
                # Use encoder_cont if available (PyTorch TFT format)
                if 'encoder_cont' in X:
                    features = X['encoder_cont']  # Shape: (batch_size, seq_len, features)
                elif 'input_1' in X:
                    features = X['input_1']  # Shape: (batch_size, seq_len, features)
                else:
                    raise ValueError(f"Dictionary input for TFT must contain 'encoder_cont' or 'input_1' key, got: {list(X.keys())}")
            else:
                # Handle numpy array input
                features = X

            # Ensure features is 3D: (batch_size, seq_len, features)
            if len(features.shape) != 3:
                raise ValueError(f"Features must be 3D (batch_size, seq_len, features), got shape: {features.shape}")

            batch_size, seq_len, num_features = features.shape

            # Create DataFrame for TimeSeriesDataSet
            # Use a more memory-efficient approach for large datasets
            logger.info(f"Creating DataFrame for {batch_size} samples with {seq_len} timesteps each")

            # Apply memory optimization if enabled
            if self.enable_memory_optimization:
                max_samples = min(batch_size, self.max_samples_per_dataset)
                if batch_size > max_samples:
                    logger.warning(f"Memory optimization: Limiting dataset to {max_samples} samples (from {batch_size})")
                    # Store original size for consistent evaluation
                    self._original_batch_size = batch_size
                    # Use random sampling instead of just taking first samples to maintain data distribution
                    import random
                    indices = random.sample(range(batch_size), max_samples)
                    indices.sort()  # Keep temporal order
                    features = features[indices]
                    y = y[indices]
                    batch_size = max_samples
                    logger.info(f"Applied random sampling to maintain data distribution")
                else:
                    self._original_batch_size = batch_size
            else:
                logger.info(f"Memory optimization disabled. Using full dataset with {batch_size} samples")
                self._original_batch_size = batch_size

            # Create DataFrame more efficiently
            data_list = []
            for batch_idx in range(batch_size):
                for time_idx in range(seq_len):
                    row = {
                        'group_id': batch_idx,  # Group identifier
                        'time_idx': time_idx,   # Time index
                        'target': y[batch_idx] if time_idx == seq_len - 1 else 0.0,  # Target only at last timestep
                    }

                    # Add feature columns
                    for feat_idx in range(num_features):
                        row[f'feature_{feat_idx}'] = features[batch_idx, time_idx, feat_idx]

                    data_list.append(row)

            df = pd.DataFrame(data_list)
            logger.info(f"Created DataFrame with {len(df)} rows")

            # Define feature columns
            feature_columns = [f'feature_{i}' for i in range(num_features)]

            # Create TimeSeriesDataSet
            if is_training or reference_dataset is None:
                # For training dataset
                max_encoder_length = seq_len - 1  # Use all but last timestep for encoding
                max_prediction_length = 1  # Predict 1 step ahead

                dataset = TimeSeriesDataSet(
                    df,
                    time_idx="time_idx",
                    target="target",
                    group_ids=["group_id"],
                    min_encoder_length=max_encoder_length,
                    max_encoder_length=max_encoder_length,
                    min_prediction_length=max_prediction_length,
                    max_prediction_length=max_prediction_length,
                    time_varying_known_reals=feature_columns,
                    time_varying_unknown_reals=[],
                    static_categoricals=[],
                    static_reals=[],
                    target_normalizer="auto",  # Enable automatic target normalization
                    add_relative_time_idx=True,
                    add_target_scales=True,
                    add_encoder_length=True,
                )
            else:
                # For validation dataset, use reference dataset structure
                dataset = TimeSeriesDataSet.from_dataset(
                    reference_dataset,
                    df,
                    predict=False,
                    stop_randomization=True
                )

            logger.info(f"Created TimeSeriesDataSet with {len(df)} samples for TFT model '{self.model_name}'")
            return dataset

        except Exception as e:
            logger.error(f"Error creating TimeSeriesDataSet for TFT model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def _train_pytorch(
        self,
        dataset: Any,
        validation_dataset: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Train the PyTorch TFT model.

        Args:
            dataset: TimeSeriesDataSet for training
            validation_dataset: Optional TimeSeriesDataSet for validation
            **kwargs: Additional keyword arguments for PyTorch Lightning Trainer

        Returns:
            Dict containing training metrics
        """
        try:
            self.training_dataset = dataset # Store dataset for potential later use

            # Build Model if not already built/loaded
            if self.model is None:
                logger.info(f"PyTorch TFT Model '{self.model_name}' not built. Building using provided dataset...")
                self._build_pytorch(dataset)
            if self.model is None:
                raise RuntimeError(f"Failed to build PyTorch TFT model '{self.model_name}' before training.")

            # Prepare DataLoaders
            train_loader = dataset.to_dataloader(
                train=True, batch_size=self.batch_size,
                num_workers=self.num_workers, shuffle=True
            )

            val_loader = None
            if validation_dataset:
                val_loader = validation_dataset.to_dataloader(
                    train=False, batch_size=self.batch_size * 2,
                    num_workers=self.num_workers, shuffle=False
                )
            elif self.config.get('use_training_as_validation', False):
                logger.warning(f"No validation dataset provided for '{self.model_name}'. Using training data subset.")
                val_loader = dataset.to_dataloader(
                    train=False, batch_size=self.batch_size * 2,
                    num_workers=self.num_workers, shuffle=False
                )

            # Configure Trainer
            # Callbacks
            early_stop_monitor = self.config.get("early_stopping_monitor", "val_loss")
            early_stop_callback = EarlyStopping(
                monitor=early_stop_monitor,
                min_delta=self.config.get("early_stopping_min_delta", 1e-4),
                patience=self.patience,
                verbose=self.config.get('verbose', False),
                mode=self.config.get("early_stopping_mode", "min")
            )
            lr_monitor = LearningRateMonitor(logging_interval='step')
            callbacks = [lr_monitor, early_stop_callback]

            # Logger
            log_dir = Path(self.config.get('log_dir', 'lightning_logs'))
            logger_name = f"{self.model_name}_{self.timeframe}"
            tb_logger = TensorBoardLogger(save_dir=str(log_dir), name=logger_name)

            # Trainer instance
            trainer_args = {
                'max_epochs': self.epochs,
                'accelerator': self.accelerator,
                'devices': self.devices,
                'gradient_clip_val': self.gradient_clip_val,
                'callbacks': callbacks,
                'logger': tb_logger,
                'enable_progress_bar': self.config.get('enable_progress_bar', True)
            }
            trainer_args.update(kwargs) # Allow overriding via kwargs
            self.trainer = pl.Trainer(**trainer_args)

            # Run Training
            logger.info(f"Starting training for PyTorch TFT model '{self.model_name}' on accelerator '{self.accelerator}'...")

            # Verify model is a LightningModule
            logger.info(f"Model type: {type(self.model)}")
            logger.info(f"Model is LightningModule: {isinstance(self.model, pl.LightningModule)}")

            if not isinstance(self.model, pl.LightningModule):
                logger.error(f"Model is not a LightningModule. Type: {type(self.model)}")
                logger.error(f"Model MRO: {type(self.model).__mro__}")

                # Check if it's a PyTorch Forecasting model that should be a LightningModule
                if hasattr(self.model, '__class__') and 'TemporalFusionTransformer' in str(type(self.model)):
                    logger.error("TemporalFusionTransformer should inherit from LightningModule")
                    logger.error("This might be a version compatibility issue with PyTorch Forecasting")

                raise TypeError(f"Model must be a LightningModule, got {type(self.model)}")

            logger.info(f"Model type verified: {type(self.model)}")

            self.trainer.fit(
                self.model,
                train_dataloaders=train_loader,
                val_dataloaders=val_loader
            )

            # Store History/Results
            results = self.trainer.logged_metrics
            best_val_score = float('inf') # Default
            if hasattr(early_stop_callback, 'best_score') and early_stop_callback.best_score:
                best_val_score = early_stop_callback.best_score.item() # Get scalar value
            elif self.trainer.callback_metrics:
                best_val_score = self.trainer.callback_metrics.get(early_stop_monitor, float('inf'))
                if isinstance(best_val_score, torch.Tensor):
                    best_val_score = best_val_score.item()

            logger.info(f"PyTorch TFT model '{self.model_name}' training finished. Best Monitored Score ({early_stop_monitor}): {best_val_score:.6f}")
            self.history = {'logged_metrics': results, 'best_val_score': best_val_score}

            # Mark model as trained for ensemble compatibility
            self._is_trained = True

            return self.history

        except Exception as e:
            logger.error(f"Error training PyTorch TFT model '{self.model_name}': {str(e)}", exc_info=True)
            raise



    def predict(self, data) -> np.ndarray:
        """Make predictions using the PyTorch TFT model.

        Args:
            data: Input data for prediction (TimeSeriesDataSet, DataLoader, DataFrame, dictionary, or numpy array)

        Returns:
            np.ndarray: Model predictions
        """
        try:
            # Handle numpy array input (common from ensemble models)
            if isinstance(data, np.ndarray):
                logger.debug(f"Converting numpy array input to TFT format for '{self.model_name}'")
                # Convert numpy array to dictionary format expected by TFT
                if len(data.shape) == 3:  # (batch_size, seq_len, features)
                    data_dict = {'encoder_cont': data}
                elif len(data.shape) == 2:  # (batch_size, features) - expand to sequence
                    # Assume single timestep, expand to sequence
                    data_dict = {'encoder_cont': np.expand_dims(data, axis=1)}
                else:
                    raise ValueError(f"Unsupported numpy array shape for TFT: {data.shape}")

                # Create dummy targets for prediction
                batch_size = data_dict['encoder_cont'].shape[0]
                dummy_targets = np.zeros(batch_size)

                # Convert to TimeSeriesDataSet
                pred_dataset = self._create_timeseries_dataset(data_dict, dummy_targets, is_training=False, reference_dataset=self.training_dataset)
                return self._predict_pytorch(pred_dataset)

            # Handle dictionary input from ModelInputAdapter
            elif isinstance(data, dict):
                # Convert dictionary to TimeSeriesDataSet for prediction
                # Create dummy targets for prediction
                if 'encoder_cont' in data:
                    features = data['encoder_cont']
                elif 'input_1' in data:
                    features = data['input_1']
                else:
                    raise ValueError(f"Dictionary input must contain 'encoder_cont' or 'input_1' key, got: {list(data.keys())}")

                # Create dummy targets (zeros) for prediction
                batch_size = features.shape[0]
                dummy_targets = np.zeros(batch_size)

                # Convert to TimeSeriesDataSet
                pred_dataset = self._create_timeseries_dataset(data, dummy_targets, is_training=False, reference_dataset=self.training_dataset)
                return self._predict_pytorch(pred_dataset)
            else:
                return self._predict_pytorch(data)

        except Exception as e:
            logger.error(f"Error in TFT predict method for '{self.model_name}': {str(e)}")
            # Return zeros as fallback to prevent ensemble from failing
            if isinstance(data, np.ndarray):
                return np.zeros(data.shape[0])
            else:
                return np.zeros(1)  # Single prediction fallback

    def _predict_pytorch(
        self,
        data: Union[Any, pd.DataFrame]
    ) -> np.ndarray:
        """Make predictions using the PyTorch TFT model.

        Args:
            data: TimeSeriesDataSet, DataLoader, or DataFrame for prediction

        Returns:
            Numpy array of predictions
        """
        try:
            if self.model is None:
                raise RuntimeError(f"Model '{self.model_name}' must be built and loaded before prediction.")

            # Ensure model is on the correct device and in eval mode
            self.model.to(self.device)
            self.model.eval()

            # Create a minimal trainer instance if needed for prediction
            if not hasattr(self, 'trainer') or self.trainer is None:
                self.trainer = pl.Trainer(
                    accelerator=self.accelerator, devices=self.devices,
                    logger=False, enable_progress_bar=False
                )

            # Prepare DataLoader if DataFrame is passed
            if isinstance(data, pd.DataFrame):
                if not hasattr(self, 'training_dataset') or self.training_dataset is None:
                    raise RuntimeError(f"Cannot create prediction DataLoader from DataFrame for '{self.model_name}' without the original training TimeSeriesDataSet.")
                logger.info(f"Creating prediction DataLoader for '{self.model_name}' from DataFrame...")
                pred_dataset = TimeSeriesDataSet.from_dataset(
                    self.training_dataset, data,
                    stop_randomization=True, predict=True
                )
                pred_loader = pred_dataset.to_dataloader(
                    train=False, batch_size=self.batch_size * 2,
                    num_workers=self.num_workers, shuffle=False
                )
            elif hasattr(data, 'to_dataloader'):  # Assume it's a TimeSeriesDataSet
                logger.info(f"Creating prediction DataLoader for '{self.model_name}' from TimeSeriesDataSet...")
                pred_loader = data.to_dataloader(
                    train=False, batch_size=self.batch_size * 2,
                    num_workers=self.num_workers, shuffle=False
                )
            elif hasattr(data, 'dataset'):  # Assume it's a DataLoader
                logger.info(f"Using provided DataLoader for '{self.model_name}' prediction...")
                pred_loader = data
            else:
                raise TypeError("Unsupported data type for prediction. Provide DataFrame, TimeSeriesDataSet, or DataLoader.")

            # Make predictions using the trainer
            logger.info(f"Generating predictions for '{self.model_name}'...")

            # Use a simpler approach to avoid callback issues
            try:
                # Ensure model is on the correct device before prediction
                self.model = self.model.to(self.device)

                # Create a minimal trainer for prediction without callbacks
                pred_trainer = pl.Trainer(
                    accelerator=self.accelerator,
                    devices=self.devices,
                    logger=False,
                    enable_progress_bar=False,
                    callbacks=[]  # No callbacks to avoid index errors
                )
                predictions_list = pred_trainer.predict(self.model, dataloaders=pred_loader)
            except (IndexError, ValueError) as trainer_err:
                # Handle specific prediction errors more gracefully
                logger.info(f"Trainer prediction encountered expected issue: {trainer_err}. Using direct model prediction...")
                # Fallback to direct model prediction
                predictions_list = []
            except Exception as trainer_err:
                logger.warning(f"Unexpected trainer prediction error: {trainer_err}. Using direct model prediction...")
                # Fallback to direct model prediction
                predictions_list = []
                # Ensure model is on the correct device
                self.model = self.model.to(self.device)
                self.model.eval()
                with torch.no_grad():
                    for batch in pred_loader:
                        # Handle different batch formats
                        if isinstance(batch, (list, tuple)):
                            # Convert list/tuple to dictionary format expected by TFT
                            if len(batch) >= 2:
                                # Assume first element is input data, second is target
                                x_batch, y_batch = batch[0], batch[1]
                                if isinstance(x_batch, dict):
                                    batch_dict = x_batch
                                else:
                                    # Create a minimal dictionary structure
                                    batch_dict = {'encoder_cont': x_batch}
                            else:
                                # Single element batch
                                x_batch = batch[0]
                                if isinstance(x_batch, dict):
                                    batch_dict = x_batch
                                else:
                                    batch_dict = {'encoder_cont': x_batch}
                        elif isinstance(batch, dict):
                            batch_dict = batch
                        else:
                            # Single tensor
                            batch_dict = {'encoder_cont': batch}

                        # Move batch to device - handle all tensor types properly
                        device_batch_dict = {}
                        for k, v in batch_dict.items():
                            if isinstance(v, torch.Tensor):
                                device_batch_dict[k] = v.to(self.device)
                            elif isinstance(v, (list, tuple)) and len(v) > 0 and isinstance(v[0], torch.Tensor):
                                device_batch_dict[k] = [item.to(self.device) if isinstance(item, torch.Tensor) else item for item in v]
                            else:
                                device_batch_dict[k] = v
                        batch_dict = device_batch_dict

                        # Get predictions
                        try:
                            pred = self.model(batch_dict)
                            # Handle different prediction output types
                            if hasattr(pred, 'prediction'):
                                # PyTorch Forecasting Output object
                                predictions_list.append(pred.prediction)
                            elif isinstance(pred, dict) and 'prediction' in pred:
                                predictions_list.append(pred['prediction'])
                            elif isinstance(pred, torch.Tensor):
                                predictions_list.append(pred)
                            else:
                                # Try to extract the main prediction tensor
                                if hasattr(pred, 'detach'):
                                    predictions_list.append(pred.detach())
                                else:
                                    logger.warning(f"Unknown prediction type: {type(pred)}")
                                    predictions_list.append(pred)
                        except Exception as pred_err:
                            logger.error(f"Error in direct model prediction: {pred_err}")
                            logger.error(f"Batch keys: {list(batch_dict.keys()) if isinstance(batch_dict, dict) else 'Not a dict'}")
                            logger.error(f"Batch shapes: {[(k, v.shape if isinstance(v, torch.Tensor) else type(v)) for k, v in batch_dict.items()] if isinstance(batch_dict, dict) else 'Not a dict'}")
                            raise

            if predictions_list is None or len(predictions_list) == 0:
                logger.warning(f"TFT model '{self.model_name}' returned no predictions. Generating fallback prediction.")
                # Generate a fallback prediction based on input data shape
                try:
                    # Try to get the expected output shape from the data
                    if hasattr(pred_loader, 'dataset') and hasattr(pred_loader.dataset, '__len__'):
                        batch_size = len(pred_loader.dataset)
                    else:
                        batch_size = 1

                    # Create a simple fallback prediction (zeros with appropriate shape)
                    fallback_prediction = torch.zeros((batch_size, 1), device=self.device)
                    logger.info(f"Generated fallback prediction with shape {fallback_prediction.shape} for '{self.model_name}'")
                    predictions_list = [fallback_prediction]
                except Exception as fallback_err:
                    logger.error(f"Failed to generate fallback prediction for '{self.model_name}': {fallback_err}")
                    raise RuntimeError(f"Prediction returned None or empty list for '{self.model_name}' and fallback failed.")

            # Process Predictions
            # Concatenate list of batch outputs (expected to be tensors)
            try:
                predictions_raw = torch.cat(predictions_list, dim=0)
            except Exception as cat_err:
                logger.error(f"Error concatenating prediction tensors: {cat_err}", exc_info=True)
                raise RuntimeError("Failed to concatenate prediction tensors.") from cat_err

            # Extract desired prediction (e.g., median quantile)
            predictions_processed = predictions_raw  # Default to raw if not quantile
            if predictions_raw.ndim == 3 and hasattr(self.model.loss, 'quantiles'):
                quantiles = self.model.loss.quantiles
                try:
                    median_idx = quantiles.index(0.5)
                    predictions_processed = predictions_raw[:, :, median_idx]
                    logger.info(f"Extracted median (P50) prediction for '{self.model_name}'.")
                except (ValueError, IndexError):
                    logger.warning(f"Median quantile (0.5) not found for '{self.model_name}'. Returning middle index.")
                    middle_idx = predictions_raw.shape[-1] // 2
                    predictions_processed = predictions_raw[:, :, middle_idx]
            elif predictions_raw.ndim == 3 and predictions_raw.shape[-1] == 1:
                predictions_processed = predictions_raw.squeeze(-1)
            elif predictions_raw.ndim != 2:
                logger.warning(f"Unexpected prediction tensor shape {predictions_raw.shape} for '{self.model_name}'. Returning processed output as is.")

            # Move to CPU and convert to NumPy
            predictions_np = predictions_processed.cpu().numpy()

            logger.info(f"Returning predictions for '{self.model_name}'. Shape: {predictions_np.shape}")
            return predictions_np

        except Exception as e:
            logger.error(f"Error making predictions with PyTorch TFT model '{self.model_name}': {str(e)}", exc_info=True)
            raise



    def predict_sequence(self, data, prediction_length: int) -> np.ndarray:
        """
        Make predictions for a sequence of future values using PyTorch.

        Args:
            data: Initial input data (TimeSeriesDataSet, DataLoader, or DataFrame)
            prediction_length: Number of future predictions to make

        Returns:
            np.ndarray: Sequence of predictions
        """
        return self._predict_sequence_pytorch(data, prediction_length)

    def _predict_sequence_pytorch(self, data, prediction_length: int) -> np.ndarray:
        """
        Make sequence predictions using the PyTorch TFT model.

        Args:
            data: Initial input data (TimeSeriesDataSet, DataLoader, or DataFrame)
            prediction_length: Number of future predictions to make

        Returns:
            np.ndarray: Sequence of predictions
        """
        try:

            # For PyTorch TFT, we can use the built-in predict method with n_samples parameter
            # if the model supports it (PyTorch Forecasting TFT does)
            if hasattr(self.model, 'predict') and callable(getattr(self.model, 'predict')):
                logger.info(f"Using built-in sequence prediction for PyTorch TFT model '{self.model_name}'")

                # Prepare data for prediction
                if isinstance(data, pd.DataFrame):
                    if not hasattr(self, 'training_dataset') or self.training_dataset is None:
                        raise RuntimeError(f"Cannot create prediction dataset from DataFrame for '{self.model_name}' without the original training TimeSeriesDataSet.")
                    pred_dataset = TimeSeriesDataSet.from_dataset(
                        self.training_dataset, data,
                        stop_randomization=True, predict=True
                    )
                    pred_data = pred_dataset
                else:
                    # Assume it's already a TimeSeriesDataSet or compatible format
                    pred_data = data

                # Make prediction with n_samples parameter
                predictions = self.model.predict(
                    pred_data,
                    mode="prediction",
                    return_x=False,
                    n_samples=prediction_length
                )

                # Process predictions to match expected output format
                if isinstance(predictions, torch.Tensor):
                    predictions = predictions.cpu().numpy()

                return predictions

            else:
                # Fallback to iterative prediction if built-in method not available
                logger.warning(f"Built-in sequence prediction not available for PyTorch TFT model '{self.model_name}'. Using iterative prediction.")

                # Make initial prediction
                current_data = data
                predictions = []

                for i in range(prediction_length):
                    # Make single prediction
                    pred = self._predict_pytorch(current_data)
                    predictions.append(pred.flatten()[0])

                    # Update data for next prediction (simplified)
                    # This is a placeholder - actual implementation would depend on data format
                    logger.warning("Iterative sequence prediction for PyTorch TFT not fully implemented")

                return np.array(predictions)

        except Exception as e:
            logger.error(f"Error making sequence predictions with PyTorch TFT model '{self.model_name}': {str(e)}", exc_info=True)
            raise



    def save_model(self, path: str) -> None:
        """Save the PyTorch TFT model.

        Args:
            path: Path to save the model
        """
        self._save_pytorch_model(path)

    def _save_pytorch_model(self, path: str) -> None:
        """Save the PyTorch TFT model.

        Args:
            path: Path to save the model
        """
        try:
            if self.model is None:
                raise ValueError(f"PyTorch TFT model '{self.model_name}' not built or trained")

            # Ensure directory exists
            Path(path).parent.mkdir(parents=True, exist_ok=True)

            # Option 1: Use Trainer to save checkpoint (preferred if available)
            if hasattr(self, 'trainer') and self.trainer:
                self.trainer.save_checkpoint(path)
                logger.info(f"PyTorch TFT model '{self.model_name}' checkpoint saved via Trainer to {path}")
            # Option 2: Save model state_dict directly
            else:
                checkpoint = {
                    'state_dict': self.model.state_dict(),  # Use 'state_dict' key for compatibility with PTL load
                    'hyper_parameters': self.model.hparams,  # Save hparams needed for load_from_checkpoint
                    'config': self.config  # Also save our own config dict
                }
                torch.save(checkpoint, path)
                logger.warning(f"PyTorch TFT model '{self.model_name}' state_dict and hparams saved directly to {path}. Trainer state not included.")

        except Exception as e:
            logger.error(f"Error saving PyTorch TFT model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def load_model(self, path: str) -> None:
        """Load the PyTorch TFT model.

        Args:
            path: Path to load the model from
        """
        self._load_pytorch_model(path)

    def _load_pytorch_model(self, path: str) -> None:
        """Load the PyTorch TFT model.

        Args:
            path: Path to load the model from
        """
        try:
            if not os.path.exists(path):
                raise FileNotFoundError(f"PyTorch TFT model '{self.model_name}' checkpoint file not found: {path}")

            # Option 1: Use class method load_from_checkpoint (Recommended)
            try:
                # map_location ensures model loaded to correct device (CPU or GPU)
                self.model = TemporalFusionTransformer.load_from_checkpoint(path, map_location=self.device)
                logger.info(f"PyTorch TFT model '{self.model_name}' loaded successfully from checkpoint {path} using load_from_checkpoint.")
                self.model.to(self.device)  # Ensure model is on correct device
                return  # Success

            except Exception as load_err:
                logger.warning(f"Failed to load PyTorch TFT model '{self.model_name}' using load_from_checkpoint: {load_err}. Attempting manual load (may require dataset).", exc_info=False)

            # Option 2: Manual load (Requires training_dataset to be set)
            if not hasattr(self, 'training_dataset') or self.training_dataset is None:
                raise RuntimeError(f"Cannot manually load PyTorch TFT model '{self.model_name}' without the TimeSeriesDataSet used during training. Ensure dataset is available (e.g., run train first) or fix load_from_checkpoint.")

            logger.info(f"Attempting manual load for PyTorch TFT model '{self.model_name}' using stored training dataset structure...")
            checkpoint = torch.load(path, map_location=self.device)

            # Rebuild architecture using the dataset
            self._build_pytorch(self.training_dataset)
            if self.model is None:
                raise RuntimeError(f"Failed to rebuild PyTorch TFT model '{self.model_name}' architecture for manual loading.")

            # Load state dict (handle PTL key 'state_dict')
            state_dict = checkpoint.get('state_dict', checkpoint.get('model_state_dict'))
            if state_dict is None:
                raise KeyError(f"Could not find state dictionary ('state_dict' or 'model_state_dict') in checkpoint {path}")

            self.model.load_state_dict(state_dict)
            self.model.to(self.device)  # Ensure correct device
            logger.info(f"PyTorch TFT model '{self.model_name}' manually loaded successfully from {path} to {self.device}")

        except Exception as e:
            logger.error(f"Error loading PyTorch TFT model '{self.model_name}' from {path}: {str(e)}", exc_info=True)
            self.model = None  # Ensure model is None on failure
            raise

    def _get_model_extension(self) -> str:
        """
        Return the appropriate file extension for the model.
        This is used by BaseModel.save to construct the full path.
        """
        return ".pt"  # Consistent with other PyTorch models