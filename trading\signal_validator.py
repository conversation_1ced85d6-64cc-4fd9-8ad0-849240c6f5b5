"""
Signal Validator - Responsible for validating trading signals against market conditions.
Prevents invalid trading decisions through comprehensive checks.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
from collections import deque

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of signal validation."""
    is_valid: bool
    error_message: Optional[str] = None
    risk_level: str = "LOW"  # LOW, MEDIUM, HIGH
    confidence_score: float = 0.0
    validation_details: Dict[str, Any] = None
    recommended_adjustments: List[str] = None
    market_regime: str = "UNKNOWN"  # TRENDING_UP, TRENDING_DOWN, RANGING, VOLATILE, UNKNOWN
    volatility_level: str = "NORMAL"  # LOW, NORMAL, HIGH
    trend_strength: float = 0.0
    volume_profile: str = "NORMAL"  # LOW, NORMAL, HIGH
    liquidity_score: float = 0.0

class MarketRegime(Enum):
    """Enum representing different market regimes."""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"

class SignalValidator:
    """
    Validates trading signals against current market conditions.
    
    This class provides comprehensive validation for trading signals
    to ensure they're compatible with current market conditions and
    to prevent erroneous trading decisions.
    """
    
    def __init__(self, 
                base_confidence_threshold: float = 0.7,
                volatility_filter: bool = True,
                enable_strict_validation: bool = True,
                max_signal_history: int = 100):
        """
        Initialize the signal validator.
        
        Args:
            base_confidence_threshold: Base confidence threshold for signal validation
            volatility_filter: Whether to filter signals in high volatility conditions
            enable_strict_validation: Whether to enable strict validation rules
            max_signal_history: Maximum number of signals to keep in history
        """
        self.base_confidence_threshold = base_confidence_threshold
        self.volatility_filter = volatility_filter
        self.enable_strict_validation = enable_strict_validation
        self.max_signal_history = max_signal_history
        
        # Track previous signals for pattern detection
        self.signal_history = deque(maxlen=max_signal_history)
        
        # Track validation statistics
        self.validation_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'validation_errors': {},
            'risk_levels': {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0},
            'market_regimes': {regime.value: 0 for regime in MarketRegime},
            'volatility_levels': {'LOW': 0, 'NORMAL': 0, 'HIGH': 0},
            'volume_profiles': {'LOW': 0, 'NORMAL': 0, 'HIGH': 0}
        }
        
        logger.info(f"Signal validator initialized with confidence threshold {base_confidence_threshold}")
    
    def validate_signal(self, 
                       signal: Dict[str, Any], 
                       market_data: Dict[str, pd.DataFrame],
                       current_positions: List[Dict[str, Any]] = None) -> ValidationResult:
        """
        Validate a trading signal against current market conditions.
        
        Args:
            signal: Trading signal to validate
            market_data: Dictionary mapping timeframes to market data
            current_positions: List of current trading positions
            
        Returns:
            ValidationResult: Result of signal validation
        """
        self.validation_stats['total_validations'] += 1
        
        # Initialize validation result
        result = ValidationResult(
            is_valid=True,
            validation_details={},
            recommended_adjustments=[]
        )
        
        if not signal or signal.get('action') == 'HOLD':
            self.validation_stats['passed_validations'] += 1
            return result  # HOLD signals are always valid
        
        # Check if we have data to validate against
        if not market_data:
            result.is_valid = False
            result.error_message = "No market data available for validation"
            self.validation_stats['failed_validations'] += 1
            self.validation_stats['validation_errors']['no_market_data'] = self.validation_stats['validation_errors'].get('no_market_data', 0) + 1
            return result
        
        # Get the main timeframe data for analysis
        main_timeframe = next(iter(market_data))
        for tf in ['M15', 'H1', 'M5']:  # Prefer common timeframes if available
            if tf in market_data:
                main_timeframe = tf
                break
                
        data = market_data[main_timeframe]
        
        # Initialize validation checks
        validations = []
        
        # 1. Check for extreme volatility
        vol_check = self._check_volatility(signal, data)
        validations.append(vol_check)
        result.validation_details['volatility'] = vol_check[1]
        result.volatility_level = self._determine_volatility_level(data)
        
        # 2. Check for price gaps
        gap_check = self._check_price_gaps(signal, data)
        validations.append(gap_check)
        result.validation_details['price_gaps'] = gap_check[1]
        
        # 3. Check for technical indicator alignment
        indicator_check = self._check_indicator_alignment(signal, data)
        validations.append(indicator_check)
        result.validation_details['indicators'] = indicator_check[1]
        
        # 4. Check confidence level
        conf_check = self._check_confidence(signal)
        validations.append(conf_check)
        result.validation_details['confidence'] = conf_check[1]
        
        # 5. Check for excessive exposure (if positions provided)
        if current_positions:
            exp_check = self._check_exposure(signal, current_positions)
            validations.append(exp_check)
            result.validation_details['exposure'] = exp_check[1]
        
        # 6. Check for pattern repetition
        pattern_check = self._check_pattern_repetition(signal)
        validations.append(pattern_check)
        result.validation_details['pattern'] = pattern_check[1]
        
        # 7. Check for out-of-hours trading
        hours_check = self._check_trading_hours(signal)
        validations.append(hours_check)
        result.validation_details['trading_hours'] = hours_check[1]
        
        # 8. Check for abnormal price levels
        price_check = self._check_price_levels(signal, data)
        validations.append(price_check)
        result.validation_details['price_levels'] = price_check[1]
        
        # 9. Check for market regime compatibility
        regime_check = self._check_market_regime(signal, data)
        validations.append(regime_check)
        result.validation_details['market_regime'] = regime_check[1]
        result.market_regime = self._determine_market_regime(data)
        
        # 10. Check for recent price action
        price_action_check = self._check_recent_price_action(signal, data)
        validations.append(price_action_check)
        result.validation_details['price_action'] = price_action_check[1]
        
        # 11. Check volume profile
        volume_check = self._check_volume_profile(data)
        validations.append(volume_check)
        result.validation_details['volume'] = volume_check[1]
        result.volume_profile = self._determine_volume_profile(data)
        
        # 12. Check liquidity
        liquidity_check = self._check_liquidity(data)
        validations.append(liquidity_check)
        result.validation_details['liquidity'] = liquidity_check[1]
        result.liquidity_score = self._calculate_liquidity_score(data)
        
        # 13. Check trend strength
        trend_check = self._check_trend_strength(data)
        validations.append(trend_check)
        result.validation_details['trend'] = trend_check[1]
        result.trend_strength = self._calculate_trend_strength(data)
        
        # Compile validation results
        failed_validations = [v for v in validations if v[0] is False]
        
        if failed_validations:
            # Signal failed one or more validation checks
            error_messages = [msg for _, msg in failed_validations if msg]
            result.error_message = "; ".join(error_messages)
            result.is_valid = False
            self.validation_stats['failed_validations'] += 1
            
            # Update validation error statistics
            for _, msg in failed_validations:
                if msg:
                    error_type = msg.split(':')[0] if ':' in msg else msg
                    self.validation_stats['validation_errors'][error_type] = self.validation_stats['validation_errors'].get(error_type, 0) + 1
        else:
            # Signal passed all validation checks
            self.validation_stats['passed_validations'] += 1
            
            # Add to history for future pattern detection
            self.signal_history.append({
                'action': signal['action'],
                'confidence': signal.get('confidence', 0),
                'timestamp': datetime.now(),
                'validation_details': result.validation_details
            })
        
        # Calculate risk level and confidence score
        risk_metrics = self._calculate_risk_metrics(signal, data)
        result.risk_level = risk_metrics['risk_level']
        result.confidence_score = risk_metrics['confidence_score']
        
        # Update risk level statistics
        self.validation_stats['risk_levels'][result.risk_level] += 1
        self.validation_stats['market_regimes'][result.market_regime] += 1
        self.validation_stats['volatility_levels'][result.volatility_level] += 1
        self.validation_stats['volume_profiles'][result.volume_profile] += 1
        
        # Get recommended adjustments if any
        result.recommended_adjustments = self._get_recommended_adjustments(signal, risk_metrics)
        
        return result
    
    def _determine_volatility_level(self, data: pd.DataFrame) -> str:
        """Determine the current volatility level."""
        try:
            if 'close' not in data.columns:
                return "NORMAL"
            
            returns = data['close'].pct_change().dropna()
            recent_volatility = returns.std()
            historical_volatility = returns.rolling(window=20).std().mean()
            
            if recent_volatility < historical_volatility * 0.8:
                return "LOW"
            elif recent_volatility > historical_volatility * 1.2:
                return "HIGH"
            else:
                return "NORMAL"
                
        except Exception as e:
            logger.error(f"Error determining volatility level: {str(e)}")
            return "NORMAL"
    
    def _determine_market_regime(self, data: pd.DataFrame) -> str:
        """Determine the current market regime."""
        try:
            if 'close' not in data.columns:
                return MarketRegime.UNKNOWN.value
            
            # Calculate trend indicators
            sma_20 = data['close'].rolling(window=20).mean()
            sma_50 = data['close'].rolling(window=50).mean()
            
            if sma_20.isna().all() or sma_50.isna().all():
                return MarketRegime.UNKNOWN.value
            
            # Calculate volatility
            returns = data['close'].pct_change().dropna()
            volatility = returns.std()
            historical_volatility = returns.rolling(window=20).std().mean()
            
            # Calculate trend strength
            trend_strength = abs(sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
            
            # Determine regime
            if volatility > historical_volatility * 1.5:
                return MarketRegime.VOLATILE.value
            elif trend_strength > 0.02:  # 2% difference
                if sma_20.iloc[-1] > sma_50.iloc[-1]:
                    return MarketRegime.TRENDING_UP.value
                else:
                    return MarketRegime.TRENDING_DOWN.value
            else:
                return MarketRegime.RANGING.value
                
        except Exception as e:
            logger.error(f"Error determining market regime: {str(e)}")
            return MarketRegime.UNKNOWN.value
    
    def _determine_volume_profile(self, data: pd.DataFrame) -> str:
        """Determine the current volume profile."""
        try:
            if 'volume' not in data.columns:
                return "NORMAL"
            
            recent_volume = data['volume'].iloc[-5:].mean()
            historical_volume = data['volume'].rolling(window=20).mean().mean()
            
            if recent_volume < historical_volume * 0.8:
                return "LOW"
            elif recent_volume > historical_volume * 1.2:
                return "HIGH"
            else:
                return "NORMAL"
                
        except Exception as e:
            logger.error(f"Error determining volume profile: {str(e)}")
            return "NORMAL"
    
    def _calculate_liquidity_score(self, data: pd.DataFrame) -> float:
        """Calculate a liquidity score based on volume and spread."""
        try:
            if 'volume' not in data.columns or 'spread' not in data.columns:
                return 0.5
            
            # Normalize volume
            volume_score = data['volume'].iloc[-5:].mean() / data['volume'].rolling(window=20).mean().mean()
            
            # Normalize spread (inverse relationship)
            spread_score = 1 - (data['spread'].iloc[-5:].mean() / data['spread'].rolling(window=20).mean().mean())
            
            # Combine scores
            liquidity_score = (volume_score * 0.7 + spread_score * 0.3)
            
            return min(max(liquidity_score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating liquidity score: {str(e)}")
            return 0.5
    
    def _calculate_trend_strength(self, data: pd.DataFrame) -> float:
        """Calculate the strength of the current trend."""
        try:
            if 'close' not in data.columns:
                return 0.0
            
            # Calculate multiple trend indicators
            sma_20 = data['close'].rolling(window=20).mean()
            sma_50 = data['close'].rolling(window=50).mean()
            
            if sma_20.isna().all() or sma_50.isna().all():
                return 0.0
            
            # Price relative to moving averages
            price = data['close'].iloc[-1]
            ma_score = (price - sma_50.iloc[-1]) / sma_50.iloc[-1]
            
            # Moving average alignment
            ma_alignment = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
            
            # Combine scores
            trend_strength = (abs(ma_score) * 0.6 + abs(ma_alignment) * 0.4)
            
            return min(max(trend_strength, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating trend strength: {str(e)}")
            return 0.0
    
    def _check_volume_profile(self, data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """Check if the current volume profile is suitable for trading."""
        try:
            if 'volume' not in data.columns:
                return True, None
            
            recent_volume = data['volume'].iloc[-5:].mean()
            historical_volume = data['volume'].rolling(window=20).mean().mean()
            
            if recent_volume < historical_volume * 0.5:
                return False, "Volume too low for reliable trading"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking volume profile: {str(e)}")
            return True, None
    
    def _check_liquidity(self, data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """Check if there is sufficient liquidity for trading."""
        try:
            if 'volume' not in data.columns or 'spread' not in data.columns:
                return True, None
            
            # Check recent volume
            recent_volume = data['volume'].iloc[-5:].mean()
            historical_volume = data['volume'].rolling(window=20).mean().mean()
            
            # Check recent spread
            recent_spread = data['spread'].iloc[-5:].mean()
            historical_spread = data['spread'].rolling(window=20).mean().mean()
            
            if recent_volume < historical_volume * 0.5:
                return False, "Insufficient volume for reliable trading"
            
            if recent_spread > historical_spread * 2:
                return False, "Spread too wide for reliable trading"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking liquidity: {str(e)}")
            return True, None
    
    def _check_trend_strength(self, data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """Check if the current trend is strong enough for trading."""
        try:
            if 'close' not in data.columns:
                return True, None
            
            # Calculate trend strength
            trend_strength = self._calculate_trend_strength(data)
            
            if trend_strength < 0.1:
                return False, "Trend too weak for reliable trading"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking trend strength: {str(e)}")
            return True, None
    
    def _check_volatility(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check for extreme volatility conditions.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if not self.volatility_filter:
            return True, None
            
        try:
            # Calculate volatility using multiple methods
            volatility_checks = []
            
            # 1. Standard deviation-based volatility
            if 'close' in data.columns:
                returns = data['close'].pct_change().dropna()
                recent_volatility = returns.std()
                historical_volatility = returns.rolling(window=20).std().mean()
                
                if recent_volatility > historical_volatility * 2:
                    volatility_checks.append(f"Recent volatility ({recent_volatility:.4f}) exceeds historical average")
            
            # 2. True Range-based volatility
            if all(col in data.columns for col in ['high', 'low', 'close']):
                tr = np.maximum(
                    data['high'] - data['low'],
                    np.maximum(
                        np.abs(data['high'] - data['close'].shift(1)),
                        np.abs(data['low'] - data['close'].shift(1))
                    )
                )
                atr = tr.rolling(window=14).mean()
                recent_atr = atr.iloc[-1]
                avg_atr = atr.mean()
                
                if recent_atr > avg_atr * 2:
                    volatility_checks.append(f"Recent ATR ({recent_atr:.4f}) exceeds historical average")
            
            # 3. Volume-based volatility
            if 'volume' in data.columns:
                volume_std = data['volume'].rolling(window=20).std()
                recent_volume_std = volume_std.iloc[-1]
                avg_volume_std = volume_std.mean()
                
                if recent_volume_std > avg_volume_std * 2:
                    volatility_checks.append(f"Recent volume volatility exceeds historical average")
            
            if volatility_checks:
                return False, "High volatility detected: " + "; ".join(volatility_checks)
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking volatility: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_price_gaps(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check for abnormal price gaps.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            if len(data) < 3:
                return True, None
                
            if all(col in data.columns for col in ['open', 'close', 'high', 'low']):
                # Check for gaps between candles
                gaps = []
                for i in range(1, min(4, len(data))):
                    prev_close = data['close'].iloc[-i-1]
                    curr_open = data['open'].iloc[-i]
                    
                    gap_pct = abs(curr_open - prev_close) / prev_close
                    gaps.append(gap_pct)
                
                max_gap = max(gaps) if gaps else 0
                avg_range = (data['high'] - data['low']).pct_change().mean()
                
                if max_gap > avg_range * 3:
                    return False, f"Recent price gap detected ({max_gap:.4%}), which exceeds normal range"
                
                # Check for recent price shocks
                recent_range = data['high'].iloc[-3:].max() - data['low'].iloc[-3:].min()
                avg_day_range = (data['high'] - data['low']).mean()
                
                if recent_range > avg_day_range * 3:
                    return False, f"Abnormal price movement in recent bars"
                    
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking price gaps: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_indicator_alignment(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check for alignment between signal and technical indicators.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        if not self.enable_strict_validation:
            return True, None
            
        try:
            # Check for conflicting indicator signals if available
            indicator_checks = []
            
            # RSI check
            if 'rsi' in data.columns:
                rsi = data['rsi'].iloc[-1]
                rsi_bullish = rsi < 30
                rsi_bearish = rsi > 70
                
                if signal['action'] == 'BUY' and rsi_bearish:
                    indicator_checks.append(f"RSI overbought ({rsi:.1f})")
                elif signal['action'] == 'SELL' and rsi_bullish:
                    indicator_checks.append(f"RSI oversold ({rsi:.1f})")
            
            # MACD check
            if all(col in data.columns for col in ['macd', 'macd_signal']):
                macd = data['macd'].iloc[-1]
                macd_signal = data['macd_signal'].iloc[-1]
                macd_bullish = macd > macd_signal
                macd_bearish = macd < macd_signal
                
                if signal['action'] == 'BUY' and macd_bearish:
                    indicator_checks.append("MACD bearish")
                elif signal['action'] == 'SELL' and macd_bullish:
                    indicator_checks.append("MACD bullish")
            
            # Moving Average check
            if all(col in data.columns for col in ['sma_20', 'sma_50']):
                sma_20 = data['sma_20'].iloc[-1]
                sma_50 = data['sma_50'].iloc[-1]
                price = data['close'].iloc[-1]
                
                if signal['action'] == 'BUY':
                    if price < sma_20 or sma_20 < sma_50:
                        indicator_checks.append("Price below short-term MA or MA bearish")
                elif signal['action'] == 'SELL':
                    if price > sma_20 or sma_20 > sma_50:
                        indicator_checks.append("Price above short-term MA or MA bullish")
            
            if indicator_checks:
                return False, "Conflicting indicators: " + "; ".join(indicator_checks)
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking indicator alignment: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_confidence(self, signal: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Check if signal confidence meets threshold.
        
        Args:
            signal: The trading signal
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            confidence = signal.get('confidence', 0.0)
            
            # Adjust threshold based on market conditions
            threshold = self.base_confidence_threshold
            
            # Increase threshold for high volatility
            if signal.get('volatility', 0) > 2.0:
                threshold *= 1.2
            
            # Increase threshold for high impact news
            if signal.get('news_impact', 0) > 0.7:
                threshold *= 1.3
            
            if confidence < threshold:
                return False, f"Insufficient confidence ({confidence:.2f} < {threshold:.2f})"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking confidence: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_exposure(self, 
                       signal: Dict[str, Any], 
                       current_positions: List[Dict[str, Any]]) -> Tuple[bool, Optional[str]]:
        """
        Check for excessive exposure.
        
        Args:
            signal: The trading signal
            current_positions: List of current trading positions
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            if not current_positions:
                return True, None
            
            # Calculate total exposure
            total_exposure = sum(pos.get('volume', 0) for pos in current_positions)
            
            # Calculate directional exposure
            long_exposure = sum(pos.get('volume', 0) for pos in current_positions if pos.get('type') == 'BUY')
            short_exposure = sum(pos.get('volume', 0) for pos in current_positions if pos.get('type') == 'SELL')
            
            # Check if new signal would increase directional exposure
            if signal['action'] == 'BUY' and long_exposure > short_exposure * 2:
                return False, f"Excessive long exposure ({long_exposure} vs {short_exposure})"
            elif signal['action'] == 'SELL' and short_exposure > long_exposure * 2:
                return False, f"Excessive short exposure ({short_exposure} vs {long_exposure})"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking exposure: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_pattern_repetition(self, signal: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Check for repetitive trading patterns.
        
        Args:
            signal: The trading signal
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            if len(self.signal_history) < 3:
                return True, None
            
            # Get recent signals
            recent_signals = list(self.signal_history)[-3:]
            
            # Check for same action repeated
            if all(s['action'] == signal['action'] for s in recent_signals):
                return False, f"Same action ({signal['action']}) repeated 3 times"
            
            # Check for alternating actions
            if len(recent_signals) >= 2:
                if (recent_signals[-1]['action'] != recent_signals[-2]['action'] and
                    recent_signals[-1]['action'] == signal['action']):
                    return False, "Alternating actions detected"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking pattern repetition: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_trading_hours(self, signal: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Check if trading is allowed at current time.
        
        Args:
            signal: The trading signal
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            current_time = datetime.now()
            hour = current_time.hour
            
            # Check for high-impact news hours
            if hour in [8, 12, 14, 20]:  # Example high-impact news hours
                return False, f"Trading during high-impact news hour ({hour}:00)"
            
            # Check for low liquidity hours
            if hour in [0, 1, 2, 3, 4]:  # Example low liquidity hours
                return False, f"Trading during low liquidity hour ({hour}:00)"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking trading hours: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_price_levels(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check for abnormal price levels.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            if 'close' not in data.columns:
                return True, None
            
            current_price = data['close'].iloc[-1]
            
            # Calculate price statistics
            price_mean = data['close'].mean()
            price_std = data['close'].std()
            
            # Check for extreme price levels
            z_score = abs(current_price - price_mean) / price_std
            if z_score > 3:
                return False, f"Price at extreme level (z-score: {z_score:.2f})"
            
            # Check for price near round numbers
            round_numbers = [round(current_price, -1), round(current_price, -2)]
            if any(abs(current_price - rn) / current_price < 0.001 for rn in round_numbers):
                return False, "Price near round number level"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking price levels: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_market_regime(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check if signal is compatible with current market regime.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            if 'market_regime' not in signal:
                return True, None
            
            regime = signal['market_regime']
            
            # Check for incompatible signal-regime combinations
            if regime == MarketRegime.VOLATILE.value:
                if signal['action'] != 'HOLD':
                    return False, "Trading not recommended in volatile regime"
            elif regime == MarketRegime.RANGING.value:
                if signal.get('confidence', 0) < self.base_confidence_threshold * 1.2:
                    return False, "Higher confidence required in ranging market"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking market regime: {str(e)}")
            return True, None  # Fail open on error
    
    def _check_recent_price_action(self, signal: Dict[str, Any], data: pd.DataFrame) -> Tuple[bool, Optional[str]]:
        """
        Check recent price action for potential conflicts.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            if len(data) < 20:
                return True, None
            
            # Check for strong recent trend
            recent_close = data['close'].iloc[-1]
            prev_close = data['close'].iloc[-5]
            price_change = (recent_close - prev_close) / prev_close
            
            if abs(price_change) > 0.02:  # 2% move
                if (price_change > 0 and signal['action'] == 'SELL') or \
                   (price_change < 0 and signal['action'] == 'BUY'):
                    return False, f"Signal conflicts with recent {abs(price_change):.1%} price move"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking recent price action: {str(e)}")
            return True, None  # Fail open on error
    
    def _calculate_risk_metrics(self, 
                               signal: Dict[str, Any], 
                               data: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate risk metrics for the signal.
        
        Args:
            signal: The trading signal
            data: Market data DataFrame
            
        Returns:
            Dict[str, Any]: Risk metrics
        """
        try:
            metrics = {
                'risk_level': 'LOW',
                'confidence_score': signal.get('confidence', 0.0),
                'volatility_score': 0.0,
                'trend_score': 0.0,
                'volume_score': 0.0,
                'market_regime_score': 0.0
            }
            
            # Calculate volatility score
            if 'close' in data.columns:
                returns = data['close'].pct_change().dropna()
                recent_volatility = returns.std()
                historical_volatility = returns.rolling(window=20).std().mean()
                metrics['volatility_score'] = recent_volatility / historical_volatility
            
            # Calculate trend score
            if 'close' in data.columns:
                sma_20 = data['close'].rolling(window=20).mean()
                sma_50 = data['close'].rolling(window=50).mean()
                if not sma_50.isna().all():
                    metrics['trend_score'] = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
            
            # Calculate volume score
            if 'volume' in data.columns:
                recent_volume = data['volume'].iloc[-5:].mean()
                historical_volume = data['volume'].rolling(window=20).mean().mean()
                metrics['volume_score'] = recent_volume / historical_volume
            
            # Calculate market regime score
            if 'market_regime' in signal:
                regime = signal['market_regime']
                if regime == MarketRegime.VOLATILE.value:
                    metrics['market_regime_score'] = 0.3
                elif regime == MarketRegime.RANGING.value:
                    metrics['market_regime_score'] = 0.5
                elif regime in [MarketRegime.TRENDING_UP.value, MarketRegime.TRENDING_DOWN.value]:
                    metrics['market_regime_score'] = 0.8
            
            # Calculate overall risk level
            risk_score = (
                metrics['volatility_score'] * 0.3 +
                (1 - abs(metrics['trend_score'])) * 0.2 +
                metrics['volume_score'] * 0.2 +
                (1 - metrics['market_regime_score']) * 0.3
            )
            
            if risk_score > 0.7:
                metrics['risk_level'] = 'HIGH'
            elif risk_score > 0.4:
                metrics['risk_level'] = 'MEDIUM'
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {str(e)}")
            return {
                'risk_level': 'MEDIUM',  # Default to medium risk on error
                'confidence_score': signal.get('confidence', 0.0),
                'volatility_score': 0.0,
                'trend_score': 0.0,
                'volume_score': 0.0,
                'market_regime_score': 0.0
            }
    
    def _get_recommended_adjustments(self, 
                                   signal: Dict[str, Any], 
                                   risk_metrics: Dict[str, Any]) -> List[str]:
        """
        Get recommended adjustments based on risk metrics.
        
        Args:
            signal: The trading signal
            risk_metrics: Risk metrics
            
        Returns:
            List[str]: Recommended adjustments
        """
        adjustments = []
        
        try:
            # Adjust position size based on risk level
            if risk_metrics['risk_level'] == 'HIGH':
                adjustments.append("Consider reducing position size by 50%")
            elif risk_metrics['risk_level'] == 'MEDIUM':
                adjustments.append("Consider reducing position size by 25%")
            
            # Adjust stop loss based on volatility
            if risk_metrics['volatility_score'] > 1.5:
                adjustments.append("Consider widening stop loss due to high volatility")
            
            # Adjust take profit based on trend
            if abs(risk_metrics['trend_score']) > 0.1:
                adjustments.append("Consider adjusting take profit based on trend strength")
            
            # Adjust for market regime
            if risk_metrics['market_regime_score'] < 0.5:
                adjustments.append("Consider waiting for better market conditions")
            
            return adjustments
            
        except Exception as e:
            logger.error(f"Error getting recommended adjustments: {str(e)}")
            return []
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """
        Get summary of validation statistics.
        
        Returns:
            Dict[str, Any]: Validation statistics
        """
        return {
            'total_validations': self.validation_stats['total_validations'],
            'passed_validations': self.validation_stats['passed_validations'],
            'failed_validations': self.validation_stats['failed_validations'],
            'pass_rate': self.validation_stats['passed_validations'] / max(1, self.validation_stats['total_validations']),
            'validation_errors': self.validation_stats['validation_errors'],
            'risk_levels': self.validation_stats['risk_levels'],
            'market_regimes': self.validation_stats['market_regimes'],
            'volatility_levels': self.validation_stats['volatility_levels'],
            'volume_profiles': self.validation_stats['volume_profiles']
        } 