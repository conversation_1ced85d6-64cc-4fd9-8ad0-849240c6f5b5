# Data Flow Architecture

## Overview

The trading bot system implements a comprehensive data flow architecture that handles data collection, processing, model training, and real-time trading operations.

## Data Flow Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MT5 Terminals │    │  Data Collection│    │   Raw Data      │
│                 │    │                 │    │   Storage       │
│ • Terminal 1-5  │───►│ • Historical    │───►│                 │
│ • BTCUSD.a      │    │ • Real-time     │    │ • Parquet files │
│ • M5-H4 data    │    │ • OHLCV + Tech  │    │ • Terminal dirs │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Model Training│    │  Data Processing│    │  Processed Data │
│                 │    │                 │    │                 │
│ • 6 Model types │◄───│ • Preprocessing │◄───│ • Feature eng.  │
│ • 5 Timeframes  │    │ • Validation    │    │ • Normalization │
│ • Performance   │    │ • Splitting     │    │ • Sequences     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Trained Models │    │ Signal Generation│    │ Trading Engine  │
│                 │    │                 │    │                 │
│ • Model files   │───►│ • Predictions   │───►│ • Risk mgmt     │
│ • Metrics       │    │ • Confidence    │    │ • Execution     │
│ • Scalers       │    │ • Signals       │    │ • Monitoring    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Data Collection Flow

### 1. Historical Data Collection
```python
# Data collection process
for terminal_id in [1, 2, 3, 4, 5]:
    for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
        data = collect_historical_data(
            terminal_id=terminal_id,
            symbol='BTCUSD.a',
            timeframe=timeframe,
            years=3
        )
        save_to_parquet(data, terminal_id, timeframe)
```

### 2. Data Storage Structure
```
data/storage/historical/
├── terminal_1/
│   ├── BTCUSD.a_M5/
│   │   └── 2021-01-01_2024-01-01_terminal_1_20240101_120000.parquet
│   ├── BTCUSD.a_M15/
│   ├── BTCUSD.a_M30/
│   ├── BTCUSD.a_H1/
│   └── BTCUSD.a_H4/
├── terminal_2/
├── terminal_3/
├── terminal_4/
└── terminal_5/
```

### 3. Real-time Data Flow
```python
# Real-time data collection
while trading_active:
    for terminal_id in active_terminals:
        current_data = get_current_market_data(terminal_id)
        processed_data = preprocess_realtime_data(current_data)
        predictions = generate_predictions(processed_data)
        signals = generate_trading_signals(predictions)
        execute_trades(signals)
```

## Data Processing Pipeline

### 1. Data Preprocessing (`data/preprocessor.py`)
```python
class DataPreprocessor:
    def process_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        # 1. Data cleaning
        cleaned_data = self.clean_data(raw_data)
        
        # 2. Feature engineering
        features = self.calculate_technical_indicators(cleaned_data)
        
        # 3. Normalization
        normalized_data = self.normalize_features(features)
        
        # 4. Sequence creation
        sequences = self.create_sequences(normalized_data)
        
        return sequences
```

### 2. Feature Engineering
- **OHLCV data**: Open, High, Low, Close, Volume
- **Technical indicators**: RSI, MACD, Bollinger Bands, ATR
- **Price derivatives**: Returns, log returns, volatility
- **Time features**: Hour, day of week, month

### 3. Data Validation
```python
def validate_data_quality(data: pd.DataFrame) -> Dict:
    return {
        'missing_values': data.isnull().sum().sum(),
        'duplicate_rows': data.duplicated().sum(),
        'outliers': detect_outliers(data),
        'data_continuity': check_time_continuity(data),
        'quality_score': calculate_quality_score(data)
    }
```

## Model Training Flow

### 1. Training Data Preparation
```python
def prepare_training_data():
    # Load processed data
    train_data = load_processed_data('train')
    val_data = load_processed_data('validation')
    test_data = load_processed_data('test')
    
    # Prepare model-specific formats
    for model_type in ['lstm', 'gru', 'tft', 'transformer', 'xgboost', 'lightgbm']:
        X_train, y_train = adapt_data_for_model(train_data, model_type)
        X_val, y_val = adapt_data_for_model(val_data, model_type)
        X_test, y_test = adapt_data_for_model(test_data, model_type)
        
        yield model_type, (X_train, y_train, X_val, y_val, X_test, y_test)
```

### 2. Model Training Process
```python
def train_model(model_type: str, data: Tuple) -> Dict:
    X_train, y_train, X_val, y_val, X_test, y_test = data
    
    # Initialize model
    model = create_model(model_type)
    
    # Train model
    training_history = model.train_with_validation(
        X_train, y_train, X_val, y_val
    )
    
    # Evaluate model
    test_predictions = model.predict(X_test)
    test_metrics = calculate_metrics(y_test, test_predictions)
    
    # Save model
    model.save()
    
    return {
        'training_history': training_history,
        'test_metrics': test_metrics,
        'model_path': model.model_path
    }
```

### 3. Model Storage
```
models/
├── terminal_1/  # LSTM models
├── terminal_2/  # GRU models
├── terminal_3/  # TFT models
├── terminal_4/  # XGBoost models
└── terminal_5/  # LightGBM models
```

## Real-time Trading Flow

### 1. Data Ingestion
```python
def collect_realtime_data(terminal_id: str) -> pd.DataFrame:
    # Get current market data
    current_tick = mt5.symbol_info_tick(symbol)
    current_bars = mt5.copy_rates_from_pos(symbol, timeframe, 0, 100)
    
    # Convert to DataFrame
    data = pd.DataFrame(current_bars)
    
    # Calculate technical indicators
    data = calculate_technical_indicators(data)
    
    return data
```

### 2. Model Prediction
```python
def generate_predictions(data: pd.DataFrame, terminal_id: str) -> np.ndarray:
    # Load appropriate model for terminal
    model = load_model_for_terminal(terminal_id)
    
    # Preprocess data for model
    processed_data = preprocess_for_prediction(data)
    
    # Generate predictions
    predictions = model.predict(processed_data)
    
    return predictions
```

### 3. Signal Generation
```python
def generate_trading_signal(predictions: np.ndarray) -> Dict:
    # Calculate confidence
    confidence = calculate_prediction_confidence(predictions)
    
    # Determine action
    if confidence > 0.65:
        predicted_price = predictions[-1]
        current_price = get_current_price()
        
        if predicted_price > current_price * 1.001:  # 0.1% threshold
            return {"action": "BUY", "confidence": confidence}
        elif predicted_price < current_price * 0.999:
            return {"action": "SELL", "confidence": confidence}
    
    return {"action": "HOLD", "confidence": confidence}
```

### 4. Trade Execution
```python
def execute_trade(signal: Dict, terminal_id: str) -> bool:
    if signal["action"] in ["BUY", "SELL"]:
        # Calculate position size
        position_size = calculate_position_size(signal)
        
        # Set risk parameters
        stop_loss = calculate_stop_loss(signal)
        take_profit = calculate_take_profit(signal)
        
        # Execute trade
        result = place_order(
            terminal_id=terminal_id,
            action=signal["action"],
            volume=position_size,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        return result.success
    
    return False
```

## Performance Monitoring Flow

### 1. Real-time Metrics
```python
def update_performance_metrics(trade_result: Dict):
    # Update trading metrics
    update_trading_metrics(trade_result)
    
    # Update model performance
    update_model_performance(trade_result)
    
    # Update risk metrics
    update_risk_metrics(trade_result)
    
    # Generate alerts if needed
    check_performance_alerts()
```

### 2. Data Persistence
- **Trade logs**: All executed trades with timestamps
- **Performance metrics**: Real-time performance tracking
- **Model metrics**: Model prediction accuracy
- **System metrics**: Resource usage and health

### 3. Visualization
- **Real-time dashboards**: Live trading performance
- **Historical analysis**: Performance trends and patterns
- **Model comparison**: Comparative model performance
- **Risk monitoring**: Risk exposure and limits
