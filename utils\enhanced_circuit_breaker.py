"""
Enhanced Circuit Breaker Pattern Implementation.

This module extends the basic circuit breaker with advanced features:
1. Multiple failure detection strategies (count-based, percentage-based, time-based)
2. Integration with the Enhanced Memory Manager
3. Integration with the Intelligent Cache System
4. Comprehensive metrics and monitoring
5. Fallback mechanisms
6. Automatic retry with backoff
"""

import time
import logging
import threading
import functools
import random
from enum import Enum
from typing import Dict, Any, Optional, Callable, List, Tuple, Union, TypeVar, Generic
from dataclasses import dataclass, field
from datetime import datetime, timedelta

# Import base circuit breaker
from utils.circuit_breaker import CircuitBreaker as BaseCircuitBreaker
from utils.circuit_breaker import CircuitState, CircuitOpenError, CircuitStats

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic return type
T = TypeVar('T')

class FailureStrategy(Enum):
    """Strategies for determining when to open the circuit."""
    COUNT_BASED = "count_based"  # Based on consecutive failures
    PERCENTAGE_BASED = "percentage_based"  # Based on failure percentage
    TIME_BASED = "time_based"  # Based on failure rate over time
    HYBRID = "hybrid"  # Combination of strategies

class RetryStrategy(Enum):
    """Strategies for retrying failed operations."""
    NONE = "none"  # No retry
    CONSTANT = "constant"  # Retry with constant delay
    LINEAR = "linear"  # Retry with linearly increasing delay
    EXPONENTIAL = "exponential"  # Retry with exponentially increasing delay
    RANDOM = "random"  # Retry with random delay within a range

@dataclass
class EnhancedCircuitStats(CircuitStats):
    """Enhanced statistics for circuit breaker monitoring."""
    # Additional metrics
    timeout_count: int = 0
    retry_count: int = 0
    fallback_count: int = 0
    latency_sum: float = 0.0  # Sum of operation latencies in seconds
    latency_count: int = 0  # Number of operations with latency recorded

    # Time-based metrics
    recent_calls: List[Tuple[datetime, bool]] = field(default_factory=list)  # (timestamp, success)

    def record_latency(self, latency: float) -> None:
        """Record operation latency."""
        self.latency_sum += latency
        self.latency_count += 1

    def get_average_latency(self) -> float:
        """Get average operation latency."""
        if self.latency_count == 0:
            return 0.0
        return self.latency_sum / self.latency_count

    def record_call(self, success: bool) -> None:
        """Record a call with timestamp for time-based metrics."""
        self.recent_calls.append((datetime.now(), success))

        # Keep only recent history (last 100 calls)
        if len(self.recent_calls) > 100:
            self.recent_calls.pop(0)

    def get_failure_percentage(self, window_seconds: Optional[int] = None) -> float:
        """
        Get the percentage of failed calls.

        Args:
            window_seconds: Optional time window in seconds (None for all calls)

        Returns:
            float: Failure percentage (0-100)
        """
        if window_seconds is None:
            # Use all calls
            total = self.successful_requests + self.failed_requests
            if total == 0:
                return 0.0
            return (self.failed_requests / total) * 100.0

        # Use calls within the time window
        now = datetime.now()
        window_start = now - timedelta(seconds=window_seconds)

        # Filter calls in the time window
        recent = [success for time, success in self.recent_calls
                 if time >= window_start]

        if not recent:
            return 0.0

        # Count failures
        failures = sum(1 for success in recent if not success)
        return (failures / len(recent)) * 100.0

    def get_call_rate(self, window_seconds: int = 60) -> float:
        """
        Get the call rate per second.

        Args:
            window_seconds: Time window in seconds

        Returns:
            float: Calls per second
        """
        now = datetime.now()
        window_start = now - timedelta(seconds=window_seconds)

        # Count calls in the time window
        recent_count = sum(1 for time, _ in self.recent_calls
                         if time >= window_start)

        return recent_count / window_seconds

class EnhancedCircuitBreaker(BaseCircuitBreaker):
    """
    Enhanced circuit breaker with advanced features.

    This class extends the base circuit breaker with additional strategies
    for failure detection, retry logic, fallback mechanisms, and integration
    with other system components.
    """

    # Class-level registry of all enhanced circuit breakers
    _enhanced_registry: Dict[str, 'EnhancedCircuitBreaker'] = {}
    _enhanced_registry_lock = threading.RLock()

    # Standard circuit breaker configurations
    _standard_breakers = {
        'mt5_connection': {'failure_threshold': 3, 'recovery_timeout': 60, 'retry_strategy': RetryStrategy.EXPONENTIAL},
        'data_collection': {'failure_threshold': 5, 'recovery_timeout': 120, 'retry_strategy': RetryStrategy.LINEAR},
        'model_prediction': {'failure_threshold': 3, 'recovery_timeout': 300, 'retry_strategy': RetryStrategy.EXPONENTIAL},
        'trade_execution': {'failure_threshold': 2, 'recovery_timeout': 60, 'retry_strategy': RetryStrategy.CONSTANT}
    }

    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        half_open_max_calls: int = 1,
        name: str = "default",
        failure_strategy: FailureStrategy = FailureStrategy.COUNT_BASED,
        failure_percentage_threshold: float = 50.0,
        failure_time_window: int = 60,
        retry_strategy: RetryStrategy = RetryStrategy.NONE,
        max_retries: int = 3,
        retry_initial_delay: float = 1.0,
        retry_max_delay: float = 30.0,
        retry_jitter: float = 0.1,
        fallback_function: Optional[Callable] = None,
        memory_manager = None,
        cache_results: bool = False,
        cache_ttl: Optional[int] = None
    ):
        """
        Initialize an enhanced circuit breaker.

        Args:
            failure_threshold: Number of consecutive failures before opening the circuit
            recovery_timeout: Seconds to wait before attempting recovery
            half_open_max_calls: Maximum number of calls allowed in half-open state
            name: Unique name for this circuit breaker
            failure_strategy: Strategy for determining when to open the circuit
            failure_percentage_threshold: Percentage of failures to open circuit
            failure_time_window: Time window in seconds for failure rate calculation
            retry_strategy: Strategy for retrying failed operations
            max_retries: Maximum number of retry attempts
            retry_initial_delay: Initial delay between retries in seconds
            retry_max_delay: Maximum delay between retries in seconds
            retry_jitter: Random jitter factor to add to retry delays
            fallback_function: Function to call when operation fails
            memory_manager: Optional memory manager for integration
            cache_results: Whether to cache successful results
            cache_ttl: Time-to-live for cached results in seconds
        """
        # Initialize base circuit breaker
        super().__init__(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            half_open_max_calls=half_open_max_calls,
            name=name
        )

        # Enhanced settings
        self.failure_strategy = failure_strategy
        self.failure_percentage_threshold = failure_percentage_threshold
        self.failure_time_window = failure_time_window
        self.retry_strategy = retry_strategy
        self.max_retries = max_retries
        self.retry_initial_delay = retry_initial_delay
        self.retry_max_delay = retry_max_delay
        self.retry_jitter = retry_jitter
        self.fallback_function = fallback_function
        self.memory_manager = memory_manager
        self.cache_results = cache_results
        self.cache_ttl = cache_ttl

        # Replace base stats with enhanced stats
        self._stats = EnhancedCircuitStats()

        # Cache for results
        self._result_cache = {}
        self._cache_lock = threading.RLock()

        # Register this enhanced circuit breaker
        with EnhancedCircuitBreaker._enhanced_registry_lock:
            EnhancedCircuitBreaker._enhanced_registry[name] = self

        logger.info(
            f"Enhanced circuit breaker '{name}' initialized with "
            f"{failure_strategy.value} strategy, {retry_strategy.value} retry"
        )

        # Register with memory manager if available
        if self.memory_manager:
            try:
                self.memory_manager.register_component(
                    f"circuit_breaker_{name}",
                    cleanup_handlers={
                        "light": lambda component_name: self._clear_cache() or 0,
                        "moderate": lambda component_name: self._clear_cache() or 0,
                        "aggressive": lambda component_name: (self.reset(), 0)[1]
                    }
                )
                logger.info(f"Circuit breaker '{name}' registered with memory manager")
            except Exception as e:
                logger.warning(f"Failed to register circuit breaker with memory manager: {str(e)}")

    def execute(self, func: Callable[..., T], *args: Any, circuit_name: Optional[str] = None,
               retry: Optional[bool] = None, use_cache: Optional[bool] = None,
               cache_key: Optional[str] = None, **kwargs: Any) -> T:
        """
        Execute a function with enhanced circuit breaker protection.

        Args:
            func: The function to execute
            *args: Arguments to pass to the function
            circuit_name: Optional specific name for this execution context
            retry: Whether to retry on failure (overrides instance setting)
            use_cache: Whether to use cache for this call (overrides instance setting)
            cache_key: Custom cache key (default: function name + args hash)
            **kwargs: Keyword arguments to pass to the function

        Returns:
            The result of the function execution

        Raises:
            CircuitOpenError: If the circuit is open
            Exception: Any exception raised by the function after retries
        """
        context_name = circuit_name or self.name
        should_retry = retry if retry is not None else (self.retry_strategy != RetryStrategy.NONE)
        should_cache = use_cache if use_cache is not None else self.cache_results

        # Generate cache key if needed
        if should_cache and not cache_key:
            cache_key = self._generate_cache_key(func, args, kwargs)

        # Check cache first if enabled
        if should_cache:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result

        # Check circuit state
        state = self.state

        with self._state_lock:
            self._stats.total_requests += 1

            # Check if circuit is open
            if state == CircuitState.OPEN:
                self._stats.total_open_time += (datetime.now() - self._stats.last_state_change_time).total_seconds()
                logger.warning(f"Circuit '{context_name}' is OPEN, rejecting request")

                # Try fallback if available
                if self.fallback_function:
                    logger.info(f"Using fallback for circuit '{context_name}'")
                    self._stats.fallback_count += 1
                    return self.fallback_function(*args, **kwargs)

                raise CircuitOpenError(context_name)

            # Check if we've reached the half-open call limit
            if state == CircuitState.HALF_OPEN and self._half_open_calls >= self.half_open_max_calls:
                logger.warning(f"Circuit '{context_name}' is HALF_OPEN but call limit reached, rejecting request")

                # Try fallback if available
                if self.fallback_function:
                    logger.info(f"Using fallback for circuit '{context_name}'")
                    self._stats.fallback_count += 1
                    return self.fallback_function(*args, **kwargs)

                raise CircuitOpenError(context_name, "Call limit reached in half-open state")

            # Increment half-open call counter if applicable
            if state == CircuitState.HALF_OPEN:
                self._half_open_calls += 1
                logger.info(f"Circuit '{context_name}' is HALF_OPEN, allowing test request ({self._half_open_calls}/{self.half_open_max_calls})")

        # Execute with retry if enabled
        if should_retry:
            return self._execute_with_retry(func, args, kwargs, context_name, cache_key if should_cache else None)

        # Execute without retry
        return self._execute_once(func, args, kwargs, context_name, cache_key if should_cache else None)

    def _execute_once(self, func: Callable[..., T], args: Any, kwargs: Any,
                     context_name: str, cache_key: Optional[str] = None) -> T:
        """Execute the function once with timing and error handling."""
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            latency = time.time() - start_time

            # Record success
            with self._state_lock:
                self._handle_success()

                # Record additional metrics
                if isinstance(self._stats, EnhancedCircuitStats):
                    self._stats.record_latency(latency)
                    self._stats.record_call(True)

            # Cache result if enabled
            if cache_key:
                self._add_to_cache(cache_key, result)

            return result

        except Exception as e:
            latency = time.time() - start_time

            # Record failure
            with self._state_lock:
                self._handle_failure(e, context_name)

                # Record additional metrics
                if isinstance(self._stats, EnhancedCircuitStats):
                    self._stats.record_latency(latency)
                    self._stats.record_call(False)
                    if isinstance(e, TimeoutError):
                        self._stats.timeout_count += 1

            # Try fallback if available
            if self.fallback_function:
                logger.info(f"Using fallback for circuit '{context_name}' after failure: {str(e)}")
                self._stats.fallback_count += 1
                return self.fallback_function(*args, **kwargs)

            # Re-raise the exception
            raise

    def _execute_with_retry(self, func: Callable[..., T], args: Any, kwargs: Any,
                           context_name: str, cache_key: Optional[str] = None) -> T:
        """Execute the function with retry logic."""
        last_exception = None

        for attempt in range(self.max_retries + 1):  # +1 for initial attempt
            try:
                if attempt > 0:
                    # This is a retry attempt
                    logger.info(f"Retry attempt {attempt}/{self.max_retries} for circuit '{context_name}'")
                    self._stats.retry_count += 1

                    # Calculate delay based on retry strategy
                    delay = self._calculate_retry_delay(attempt)

                    # Wait before retry
                    if delay > 0:
                        logger.debug(f"Waiting {delay:.2f}s before retry {attempt}")
                        time.sleep(delay)

                # Execute the function
                result = self._execute_once(func, args, kwargs, context_name, cache_key)
                return result

            except CircuitOpenError:
                # Don't retry if circuit is open
                raise

            except Exception as e:
                last_exception = e

                # Check if circuit is open after this failure
                if self.state == CircuitState.OPEN:
                    logger.warning(f"Circuit '{context_name}' opened during retry, aborting further attempts")
                    break

                # Continue to next retry attempt
                logger.warning(f"Attempt {attempt+1} failed: {str(e)}")

        # All retries failed
        if self.fallback_function:
            logger.info(f"Using fallback for circuit '{context_name}' after all retries failed")
            self._stats.fallback_count += 1
            return self.fallback_function(*args, **kwargs)

        # Re-raise the last exception
        if last_exception:
            raise last_exception

        # This should never happen
        raise RuntimeError(f"Unexpected error in circuit breaker '{context_name}'")

    def _calculate_retry_delay(self, attempt: int) -> float:
        """
        Calculate delay for retry attempt based on strategy.

        Args:
            attempt: The retry attempt number (1-based)

        Returns:
            float: Delay in seconds
        """
        if self.retry_strategy == RetryStrategy.NONE:
            return 0.0

        elif self.retry_strategy == RetryStrategy.CONSTANT:
            delay = self.retry_initial_delay

        elif self.retry_strategy == RetryStrategy.LINEAR:
            delay = self.retry_initial_delay * attempt

        elif self.retry_strategy == RetryStrategy.EXPONENTIAL:
            delay = self.retry_initial_delay * (2 ** (attempt - 1))

        elif self.retry_strategy == RetryStrategy.RANDOM:
            min_delay = self.retry_initial_delay
            max_delay = min(self.retry_initial_delay * attempt, self.retry_max_delay)
            delay = random.uniform(min_delay, max_delay)

        else:
            delay = self.retry_initial_delay

        # Apply jitter to avoid thundering herd
        jitter = random.uniform(-self.retry_jitter, self.retry_jitter)
        delay = delay * (1 + jitter)

        # Cap at max delay
        return min(delay, self.retry_max_delay)

    def _handle_failure(self, exception: Exception, context_name: str) -> None:
        """
        Enhanced failure handling with different strategies.

        Args:
            exception: The exception that occurred
            context_name: Name of the execution context
        """
        # Update base stats
        super()._handle_failure(exception, context_name)

        # Check if we should open the circuit based on the selected strategy
        if self.state == CircuitState.CLOSED and self._should_open_circuit():
            logger.warning(
                f"Circuit '{context_name}' failure threshold reached "
                f"using {self.failure_strategy.value} strategy, opening circuit"
            )
            self._transition_to_open()

    def _should_open_circuit(self) -> bool:
        """
        Determine if the circuit should be opened based on the strategy.

        Returns:
            bool: True if circuit should be opened, False otherwise
        """
        if self.state == CircuitState.OPEN:
            return True

        if not isinstance(self._stats, EnhancedCircuitStats):
            # Fall back to base implementation if stats are not enhanced
            return self._stats.consecutive_failures >= self.failure_threshold

        if self.failure_strategy == FailureStrategy.COUNT_BASED:
            return self._stats.consecutive_failures >= self.failure_threshold

        elif self.failure_strategy == FailureStrategy.PERCENTAGE_BASED:
            # Need at least 5 calls to calculate percentage
            if (self._stats.successful_requests + self._stats.failed_requests) < 5:
                return False

            return self._stats.get_failure_percentage() >= self.failure_percentage_threshold

        elif self.failure_strategy == FailureStrategy.TIME_BASED:
            # Need at least a few calls in the window
            if len(self._stats.recent_calls) < 5:
                return False

            return self._stats.get_failure_percentage(self.failure_time_window) >= self.failure_percentage_threshold

        elif self.failure_strategy == FailureStrategy.HYBRID:
            # Open if any strategy indicates failure
            count_based = self._stats.consecutive_failures >= self.failure_threshold

            # Only check percentage if we have enough data
            percentage_based = False
            if (self._stats.successful_requests + self._stats.failed_requests) >= 5:
                percentage_based = self._stats.get_failure_percentage() >= self.failure_percentage_threshold

            # Only check time-based if we have enough data
            time_based = False
            if len(self._stats.recent_calls) >= 5:
                time_based = self._stats.get_failure_percentage(self.failure_time_window) >= self.failure_percentage_threshold

            return count_based or percentage_based or time_based

        # Default to base implementation
        return self._stats.consecutive_failures >= self.failure_threshold

    def _generate_cache_key(self, func: Callable, args: Any, kwargs: Any) -> str:
        """Generate a cache key for the function call."""
        # Simple implementation - can be enhanced for better uniqueness
        func_name = func.__name__
        args_str = str(args)
        kwargs_str = str(sorted(kwargs.items()))
        return f"{func_name}:{hash(args_str + kwargs_str)}"

    def _add_to_cache(self, key: str, value: Any) -> None:
        """Add a result to the cache."""
        with self._cache_lock:
            self._result_cache[key] = {
                'value': value,
                'timestamp': datetime.now()
            }

    def _get_from_cache(self, key: str) -> Optional[Any]:
        """Get a result from the cache if it exists and is not expired."""
        with self._cache_lock:
            if key not in self._result_cache:
                return None

            entry = self._result_cache[key]

            # Check if entry is expired
            if self.cache_ttl is not None:
                age = (datetime.now() - entry['timestamp']).total_seconds()
                if age > self.cache_ttl:
                    # Remove expired entry
                    del self._result_cache[key]
                    return None

            return entry['value']

    def _clear_cache(self) -> None:
        """Clear the result cache."""
        with self._cache_lock:
            self._result_cache.clear()
        logger.debug(f"Cleared result cache for circuit '{self.name}'")
        return 0  # For memory manager integration

    def reset(self) -> None:
        """Reset the circuit breaker and clear the cache."""
        super().reset()
        self._clear_cache()

    def get_enhanced_stats(self) -> EnhancedCircuitStats:
        """Get enhanced statistics for this circuit breaker."""
        with self._state_lock:
            if isinstance(self._stats, EnhancedCircuitStats):
                return self._stats

            # Convert base stats to enhanced stats if needed
            enhanced_stats = EnhancedCircuitStats(
                total_requests=self._stats.total_requests,
                successful_requests=self._stats.successful_requests,
                failed_requests=self._stats.failed_requests,
                consecutive_failures=self._stats.consecutive_failures,
                total_open_time=self._stats.total_open_time,
                last_failure_time=self._stats.last_failure_time,
                last_success_time=self._stats.last_success_time,
                last_state_change_time=self._stats.last_state_change_time,
                open_count=self._stats.open_count
            )
            return enhanced_stats

    @classmethod
    def get_enhanced(cls, name: str) -> Optional['EnhancedCircuitBreaker']:
        """
        Get an enhanced circuit breaker by name.

        Args:
            name: Name of the circuit breaker to retrieve

        Returns:
            The enhanced circuit breaker instance or None if not found
        """
        with cls._enhanced_registry_lock:
            return cls._enhanced_registry.get(name)

    @classmethod
    def get_all_enhanced(cls) -> Dict[str, 'EnhancedCircuitBreaker']:
        """
        Get all registered enhanced circuit breakers.

        Returns:
            Dictionary of all enhanced circuit breakers, keyed by name
        """
        with cls._enhanced_registry_lock:
            return cls._enhanced_registry.copy()

    @classmethod
    def register_standard_breakers(cls, memory_manager=None) -> Dict[str, 'EnhancedCircuitBreaker']:
        """
        Register standard circuit breakers with consistent settings.

        Args:
            memory_manager: Optional memory manager for integration

        Returns:
            Dict[str, EnhancedCircuitBreaker]: Dictionary of registered circuit breakers
        """
        registered = {}

        with cls._enhanced_registry_lock:
            for name, settings in cls._standard_breakers.items():
                # Skip if already registered
                if name in cls._enhanced_registry:
                    registered[name] = cls._enhanced_registry[name]
                    continue

                # Create new circuit breaker with standard settings
                cb_settings = settings.copy()

                # Add memory manager if provided
                if memory_manager:
                    cb_settings['memory_manager'] = memory_manager

                # Create and register the circuit breaker
                cb = cls(name=name, **cb_settings)
                registered[name] = cb

                logger.info(f"Registered standard circuit breaker: {name}")

        return registered

# Decorator for using the enhanced circuit breaker
def with_circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    failure_strategy: FailureStrategy = FailureStrategy.COUNT_BASED,
    retry_strategy: RetryStrategy = RetryStrategy.NONE,
    max_retries: int = 3,
    fallback_function: Optional[Callable] = None,
    cache_results: bool = False,
    cache_ttl: Optional[int] = None
):
    """
    Decorator for using an enhanced circuit breaker.

    Example:
        @with_circuit_breaker("my_service", retry_strategy=RetryStrategy.EXPONENTIAL)
        def my_function():
            # Function that might fail
            pass
    """
    def decorator(func):
        # Get or create the circuit breaker
        circuit_breaker = EnhancedCircuitBreaker.get_enhanced(name)
        if circuit_breaker is None:
            circuit_breaker = EnhancedCircuitBreaker(
                name=name,
                failure_threshold=failure_threshold,
                recovery_timeout=recovery_timeout,
                failure_strategy=failure_strategy,
                retry_strategy=retry_strategy,
                max_retries=max_retries,
                fallback_function=fallback_function,
                cache_results=cache_results,
                cache_ttl=cache_ttl
            )

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return circuit_breaker.execute(func, *args, **kwargs)

        return wrapper

    return decorator

# Convenience functions
def get_circuit_breaker(name: str) -> Optional[EnhancedCircuitBreaker]:
    """Get an enhanced circuit breaker by name."""
    return EnhancedCircuitBreaker.get_enhanced(name)

def create_circuit_breaker(name: str, **kwargs) -> EnhancedCircuitBreaker:
    """Create a new enhanced circuit breaker."""
    circuit_breaker = EnhancedCircuitBreaker.get_enhanced(name)
    if circuit_breaker is None:
        circuit_breaker = EnhancedCircuitBreaker(name=name, **kwargs)
    return circuit_breaker

def reset_circuit_breaker(name: str) -> bool:
    """
    Reset a circuit breaker by name.

    Args:
        name: Name of the circuit breaker

    Returns:
        bool: True if reset successful, False if circuit breaker not found
    """
    circuit_breaker = EnhancedCircuitBreaker.get_enhanced(name)
    if circuit_breaker:
        circuit_breaker.reset()
        return True
    return False

def get_all_circuit_breakers() -> Dict[str, EnhancedCircuitBreaker]:
    """Get all registered enhanced circuit breakers."""
    return EnhancedCircuitBreaker.get_all_enhanced()

def register_standard_circuit_breakers(memory_manager=None) -> Dict[str, EnhancedCircuitBreaker]:
    """Register standard circuit breakers with consistent settings."""
    return EnhancedCircuitBreaker.register_standard_breakers(memory_manager)

# Try to import memory manager for integration
try:
    from utils.enhanced_memory_manager import enhanced_memory_manager
    memory_manager_available = True
except ImportError:
    memory_manager_available = False
    logger.debug("Enhanced memory manager not available for circuit breaker integration")
