#!/usr/bin/env python
"""
Model comparison utility for evaluating and comparing different models.

This script provides comprehensive model comparison functionality including:

1. Performance metrics comparison across all models
2. Statistical significance testing
3. Visualization of model performance
4. Ranking and recommendation system
5. Detailed comparison reports

Usage:
    python tests/model_comparison.py [--models MODEL_NAMES] [--output OUTPUT_DIR]
"""

import sys
import logging
import argparse
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_comparison.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('model_comparison')

# Import project modules with error handling
try:
    from config import ConfigurationManager
    from model_adapter import ModelInputAdapter
except ImportError as e:
    logger.error(f"Failed to import required modules: {str(e)}")
    sys.exit(1)

class ModelComparator:
    """Main class for comparing model performance."""

    def __init__(self, output_dir: Optional[str] = None):
        """Initialize model comparator.

        Args:
            output_dir: Optional output directory for results
        """
        self.config_manager = ConfigurationManager()
        self.config = self.config_manager.get_config()

        # Create output directory
        self.output_dir = Path(output_dir) if output_dir else Path("comparison_results") / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.model_results = {}
        self.comparison_metrics = {}

        logger.info(f"Model comparator initialized with output directory: {self.output_dir}")

    def load_model_results(self, model_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Load results from trained models.

        Args:
            model_names: Optional list of specific models to compare

        Returns:
            Dictionary containing model results
        """
        try:
            models_to_compare = model_names if model_names else list(self.config.models.keys())
            logger.info(f"Loading results for models: {models_to_compare}")

            results = {}

            for model_name in models_to_compare:
                if model_name not in self.config.models:
                    logger.warning(f"Model {model_name} not found in configuration")
                    continue

                model_config = self.config.models[model_name]

                # Try to load model and evaluate
                try:
                    model_result = self._evaluate_model(model_name, model_config)
                    if model_result:
                        results[model_name] = model_result
                        logger.info(f"Successfully loaded results for {model_name}")
                    else:
                        logger.warning(f"Failed to evaluate {model_name}")

                except Exception as e:
                    logger.error(f"Error evaluating {model_name}: {str(e)}")
                    continue

            self.model_results = results
            return results

        except Exception as e:
            logger.error(f"Error loading model results: {str(e)}")
            return {}

    def _evaluate_model(self, model_name: str, model_config: Any) -> Optional[Dict[str, Any]]:
        """Evaluate a single model.

        Args:
            model_name: Name of the model
            model_config: Model configuration

        Returns:
            Model evaluation results or None if failed
        """
        try:
            # Load test data
            data_dir = Path(self.config.data_base_path) / "processed"
            test_path = data_dir / "test" / "BTCUSD.a_M5.parquet"

            if not test_path.exists():
                logger.error(f"Test data not found: {test_path}")
                return None

            test_df = pd.read_parquet(test_path)

            # Prepare features and targets
            feature_columns = model_config.FEATURE_COLUMNS
            if not feature_columns:
                logger.error(f"No feature columns specified for {model_name}")
                return None

            X_test = test_df[feature_columns].values
            y_test = test_df['close'].values

            # Handle NaN values
            X_test = np.nan_to_num(X_test, nan=0.0)
            y_test = np.nan_to_num(y_test, nan=0.0)

            # Adapt input for model
            target_shape = (model_config.sequence_length, len(feature_columns))
            X_test_adapted = ModelInputAdapter.adapt_input(X_test, target_shape, model_config.model_type)

            if X_test_adapted is None:
                logger.error(f"Failed to adapt input for {model_name}")
                return None

            # Try to load and evaluate model
            model_class = self._get_model_class(model_config.model_type)
            if model_class is None:
                logger.error(f"Unknown model type for {model_name}: {model_config.model_type}")
                return None

            # Create model configuration
            config_dict = {
                'model_name': model_name,
                'model_type': model_config.model_type,
                'timeframe': 'M5',
                'terminal_id': '1',
                'models_base_path': self.config.models_base_path,
                'model_filename': f"{model_name}_model",
                'input_dim': X_test_adapted.shape[-1] if len(X_test_adapted.shape) > 1 else 1,
                'output_dim': 1,
                'FEATURE_COLUMNS': feature_columns,
                **{k: v for k, v in model_config.__dict__.items() if not k.startswith('_')}
            }

            # Initialize and load model
            model = model_class(config_dict)

            if not model.load():
                logger.warning(f"Could not load trained model for {model_name}")
                return None

            # Make predictions
            predictions = model.predict(X_test_adapted)

            # Calculate metrics
            metrics = self._calculate_comprehensive_metrics(y_test, predictions.flatten())

            # Calculate additional performance indicators
            performance_indicators = self._calculate_performance_indicators(y_test, predictions.flatten())

            return {
                'model_name': model_name,
                'model_type': model_config.model_type,
                'metrics': metrics,
                'performance_indicators': performance_indicators,
                'predictions': predictions.flatten().tolist()[:100],  # Store first 100 predictions for analysis
                'actual': y_test.tolist()[:100],
                'evaluation_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error evaluating model {model_name}: {str(e)}")
            return None

    def _get_model_class(self, model_type: str):
        """Get model class based on model type."""
        try:
            model_type_lower = model_type.lower()

            if 'lstm' in model_type_lower:
                from models.lstm_model import LSTMModel
                return LSTMModel
            elif 'gru' in model_type_lower:
                from models.gru_model import GRUModel
                return GRUModel
            elif 'xgboost' in model_type_lower:
                from models.xgboost_model import XGBoostModel
                return XGBoostModel
            elif 'lightgbm' in model_type_lower:
                from models.lightgbm_model import LightGBMModel
                return LightGBMModel
            elif 'tft' in model_type_lower:
                from models.tft_model import TFTModel
                return TFTModel
            else:
                return None

        except ImportError as e:
            logger.error(f"Failed to import model class for {model_type}: {str(e)}")
            return None

    def _calculate_comprehensive_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics."""
        try:
            from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

            metrics = {
                'mae': float(mean_absolute_error(y_true, y_pred)),
                'mse': float(mean_squared_error(y_true, y_pred)),
                'rmse': float(np.sqrt(mean_squared_error(y_true, y_pred))),
                'r2': float(r2_score(y_true, y_pred))
            }

            # Additional metrics
            metrics['mape'] = float(np.mean(np.abs((y_true - y_pred) / np.where(y_true != 0, y_true, 1))) * 100)
            metrics['median_ae'] = float(np.median(np.abs(y_true - y_pred)))
            metrics['max_ae'] = float(np.max(np.abs(y_true - y_pred)))
            metrics['std_ae'] = float(np.std(np.abs(y_true - y_pred)))

            # Directional accuracy
            if len(y_true) > 1:
                true_direction = np.diff(y_true)
                pred_direction = np.diff(y_pred)
                directional_accuracy = np.mean((true_direction * pred_direction) > 0)
                metrics['directional_accuracy'] = float(directional_accuracy)

            # Bias
            metrics['bias'] = float(np.mean(y_pred - y_true))

            return metrics

        except Exception as e:
            logger.error(f"Error calculating metrics: {str(e)}")
            return {}

    def _calculate_performance_indicators(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate additional performance indicators."""
        try:
            indicators = {}

            # Prediction consistency
            pred_std = np.std(y_pred)
            true_std = np.std(y_true)
            indicators['prediction_consistency'] = float(1 - abs(pred_std - true_std) / true_std) if true_std > 0 else 0

            # Trend following ability
            true_trends = np.sign(np.diff(y_true))
            pred_trends = np.sign(np.diff(y_pred))
            trend_accuracy = np.mean(true_trends == pred_trends)
            indicators['trend_following'] = float(trend_accuracy)

            # Volatility prediction
            true_volatility = np.std(np.diff(y_true))
            pred_volatility = np.std(np.diff(y_pred))
            volatility_ratio = min(pred_volatility, true_volatility) / max(pred_volatility, true_volatility) if max(pred_volatility, true_volatility) > 0 else 0
            indicators['volatility_prediction'] = float(volatility_ratio)

            # Extreme value handling
            true_extremes = np.abs(y_true - np.mean(y_true)) > 2 * np.std(y_true)
            if np.any(true_extremes):
                extreme_mae = np.mean(np.abs(y_true[true_extremes] - y_pred[true_extremes]))
                normal_mae = np.mean(np.abs(y_true[~true_extremes] - y_pred[~true_extremes]))
                indicators['extreme_value_handling'] = float(1 - extreme_mae / normal_mae) if normal_mae > 0 else 0
            else:
                indicators['extreme_value_handling'] = 1.0

            return indicators

        except Exception as e:
            logger.error(f"Error calculating performance indicators: {str(e)}")
            return {}

    def compare_models(self) -> Dict[str, Any]:
        """Compare all loaded models and generate rankings."""
        try:
            if not self.model_results:
                logger.error("No model results available for comparison")
                return {}

            logger.info(f"Comparing {len(self.model_results)} models")

            # Create comparison dataframe
            comparison_data = []

            for model_name, result in self.model_results.items():
                row = {
                    'model_name': model_name,
                    'model_type': result['model_type'],
                    **result['metrics'],
                    **result['performance_indicators']
                }
                comparison_data.append(row)

            comparison_df = pd.DataFrame(comparison_data)

            # Calculate rankings
            rankings = self._calculate_rankings(comparison_df)

            # Generate statistical comparisons
            statistical_tests = self._perform_statistical_tests()

            # Create visualizations
            self._create_comparison_visualizations(comparison_df)

            # Generate recommendations
            recommendations = self._generate_recommendations(comparison_df, rankings)

            comparison_results = {
                'comparison_summary': {
                    'timestamp': datetime.now().isoformat(),
                    'models_compared': len(self.model_results),
                    'metrics_evaluated': len(comparison_df.columns) - 2  # Exclude model_name and model_type
                },
                'model_results': self.model_results,
                'rankings': rankings,
                'statistical_tests': statistical_tests,
                'recommendations': recommendations,
                'comparison_dataframe': comparison_df.to_dict('records')
            }

            # Save comparison results
            self._save_comparison_results(comparison_results)

            return comparison_results

        except Exception as e:
            logger.error(f"Error comparing models: {str(e)}")
            return {}

    def _calculate_rankings(self, comparison_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate model rankings based on different criteria."""
        try:
            rankings = {}

            # Key metrics for ranking (lower is better for error metrics, higher for others)
            error_metrics = ['mae', 'mse', 'rmse', 'mape', 'median_ae', 'max_ae', 'std_ae']
            performance_metrics = ['r2', 'directional_accuracy', 'prediction_consistency', 'trend_following', 'volatility_prediction', 'extreme_value_handling']

            # Overall ranking based on multiple criteria
            ranking_scores = {}

            for model_name in comparison_df['model_name']:
                score = 0

                # Error metrics (lower is better)
                for metric in error_metrics:
                    if metric in comparison_df.columns:
                        rank = comparison_df[metric].rank(ascending=True)
                        model_rank = rank[comparison_df['model_name'] == model_name].iloc[0]
                        score += (len(comparison_df) - model_rank + 1) / len(comparison_df)

                # Performance metrics (higher is better)
                for metric in performance_metrics:
                    if metric in comparison_df.columns:
                        rank = comparison_df[metric].rank(ascending=False)
                        model_rank = rank[comparison_df['model_name'] == model_name].iloc[0]
                        score += (len(comparison_df) - model_rank + 1) / len(comparison_df)

                ranking_scores[model_name] = score / (len(error_metrics) + len(performance_metrics))

            # Sort by overall score
            sorted_rankings = sorted(ranking_scores.items(), key=lambda x: x[1], reverse=True)

            rankings['overall'] = [{'model': model, 'score': score} for model, score in sorted_rankings]

            # Individual metric rankings
            for metric in error_metrics + performance_metrics:
                if metric in comparison_df.columns:
                    ascending = metric in error_metrics
                    metric_ranking = comparison_df.nsmallest(len(comparison_df), metric) if ascending else comparison_df.nlargest(len(comparison_df), metric)
                    rankings[metric] = [{'model': row['model_name'], 'value': row[metric]} for _, row in metric_ranking.iterrows()]

            return rankings

        except Exception as e:
            logger.error(f"Error calculating rankings: {str(e)}")
            return {}

    def _perform_statistical_tests(self) -> Dict[str, Any]:
        """Perform statistical significance tests between models."""
        try:
            # This would require actual prediction arrays for proper statistical testing
            # For now, return placeholder structure
            return {
                'note': 'Statistical tests require full prediction arrays',
                'tests_performed': [],
                'significant_differences': []
            }

        except Exception as e:
            logger.error(f"Error performing statistical tests: {str(e)}")
            return {}

    def _create_comparison_visualizations(self, comparison_df: pd.DataFrame):
        """Create visualization plots for model comparison."""
        try:
            # Set style
            plt.style.use('default')
            sns.set_palette("husl")

            # Create figure with subplots
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('Model Performance Comparison', fontsize=16)

            # Plot 1: RMSE vs R²
            axes[0, 0].scatter(comparison_df['rmse'], comparison_df['r2'])
            for i, model in enumerate(comparison_df['model_name']):
                axes[0, 0].annotate(model, (comparison_df['rmse'].iloc[i], comparison_df['r2'].iloc[i]))
            axes[0, 0].set_xlabel('RMSE (lower is better)')
            axes[0, 0].set_ylabel('R² (higher is better)')
            axes[0, 0].set_title('Accuracy vs Precision Trade-off')

            # Plot 2: Directional Accuracy
            if 'directional_accuracy' in comparison_df.columns:
                axes[0, 1].bar(comparison_df['model_name'], comparison_df['directional_accuracy'])
                axes[0, 1].set_ylabel('Directional Accuracy')
                axes[0, 1].set_title('Directional Prediction Accuracy')
                axes[0, 1].tick_params(axis='x', rotation=45)

            # Plot 3: Multiple metrics heatmap
            metrics_for_heatmap = ['mae', 'rmse', 'r2', 'directional_accuracy']
            available_metrics = [m for m in metrics_for_heatmap if m in comparison_df.columns]

            if available_metrics:
                heatmap_data = comparison_df[['model_name'] + available_metrics].set_index('model_name')
                # Normalize data for better visualization
                heatmap_data_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

                sns.heatmap(heatmap_data_norm.T, annot=True, cmap='RdYlBu_r', ax=axes[1, 0])
                axes[1, 0].set_title('Normalized Metrics Heatmap')

            # Plot 4: Performance indicators
            performance_cols = ['prediction_consistency', 'trend_following', 'volatility_prediction']
            available_perf = [p for p in performance_cols if p in comparison_df.columns]

            if available_perf:
                perf_data = comparison_df[['model_name'] + available_perf].set_index('model_name')
                perf_data.plot(kind='bar', ax=axes[1, 1])
                axes[1, 1].set_title('Performance Indicators')
                axes[1, 1].tick_params(axis='x', rotation=45)
                axes[1, 1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')

            plt.tight_layout()

            # Save plot
            plot_path = self.output_dir / "model_comparison_plots.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"Comparison plots saved to {plot_path}")

        except Exception as e:
            logger.error(f"Error creating visualizations: {str(e)}")

    def _generate_recommendations(self, comparison_df: pd.DataFrame, rankings: Dict[str, Any]) -> Dict[str, Any]:
        """Generate recommendations based on comparison results."""
        try:
            recommendations = {}

            if 'overall' in rankings and rankings['overall']:
                best_model = rankings['overall'][0]
                recommendations['best_overall'] = {
                    'model': best_model['model'],
                    'score': best_model['score'],
                    'reason': 'Highest overall ranking across all metrics'
                }

                # Best for specific use cases
                if 'directional_accuracy' in rankings:
                    best_direction = rankings['directional_accuracy'][0]
                    recommendations['best_for_direction'] = {
                        'model': best_direction['model'],
                        'value': best_direction['value'],
                        'reason': 'Best at predicting price direction'
                    }

                if 'rmse' in rankings:
                    best_accuracy = rankings['rmse'][0]
                    recommendations['most_accurate'] = {
                        'model': best_accuracy['model'],
                        'value': best_accuracy['value'],
                        'reason': 'Lowest prediction error (RMSE)'
                    }

                # Model type recommendations
                model_types = comparison_df.groupby('model_type').agg({
                    'rmse': 'mean',
                    'r2': 'mean',
                    'directional_accuracy': 'mean'
                }).round(4)

                recommendations['model_type_analysis'] = model_types.to_dict('index')

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return {}

    def _save_comparison_results(self, results: Dict[str, Any]):
        """Save comparison results to files."""
        try:
            # Save JSON report
            json_path = self.output_dir / "model_comparison_report.json"
            with open(json_path, 'w') as f:
                json.dump(results, f, indent=4, default=str)

            logger.info(f"Comparison report saved to {json_path}")

            # Save CSV summary
            if 'comparison_dataframe' in results:
                csv_path = self.output_dir / "model_comparison_summary.csv"
                pd.DataFrame(results['comparison_dataframe']).to_csv(csv_path, index=False)
                logger.info(f"Comparison summary saved to {csv_path}")

        except Exception as e:
            logger.error(f"Error saving comparison results: {str(e)}")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Compare model performance')
    parser.add_argument('--models', nargs='+', help='Specific models to compare')
    parser.add_argument('--output', type=str, help='Output directory for results')
    return parser.parse_args()


def main():
    """Main function."""
    try:
        args = parse_arguments()

        logger.info("Starting model comparison")

        # Initialize comparator
        comparator = ModelComparator(args.output)

        # Load model results
        results = comparator.load_model_results(args.models)

        if not results:
            logger.error("No model results could be loaded")
            sys.exit(1)

        # Perform comparison
        comparison_results = comparator.compare_models()

        if comparison_results:
            logger.info("Model comparison completed successfully")

            # Print summary
            if 'recommendations' in comparison_results and 'best_overall' in comparison_results['recommendations']:
                best_model = comparison_results['recommendations']['best_overall']
                logger.info(f"Best overall model: {best_model['model']} (score: {best_model['score']:.4f})")
        else:
            logger.error("Model comparison failed")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Comparison interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
