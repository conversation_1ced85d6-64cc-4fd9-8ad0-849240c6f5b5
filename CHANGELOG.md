# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Enhanced documentation with updated README.md
- New CHANGELOG.md file to track changes
- Improved error handling documentation

### Changed
- Updated README.md with current project structure and features
- Improved documentation for MT5 connection management
- Enhanced documentation for model adapter system
- Added documentation for signal generator system

### Fixed
- Fixed MT5 data retrieval in TradingBot to properly handle market data
- Improved signal handling in TradingStrategy to support both dictionary and object formats
- Enhanced lot size calculation in TradeExecutor with better fallback mechanisms
- Fixed account information handling to properly work with MT5's named tuples
- Corrected market regime detection to properly map string values to enum types
- Updated ModelManager to use EnhancedErrorHandler consistently

## [1.0.0] - 2023-06-15

### Added
- Initial release of the trading bot system
- Multi-terminal MT5 integration with connection pooling
- Advanced data preprocessing with custom technical indicators
- Ensemble model system with multiple ML models
- Real-time trading signal generation and execution
- Comprehensive error handling and recovery system
- Context-aware model loading and management
- Memory-optimized data processing
- Circuit breaker pattern for system protection
- Multi-threaded execution with resource management
- Real-time performance monitoring
- Automated recovery mechanisms
- Centralized configuration management
- Advanced training visualization system

### Changed
- Improved error handling with EnhancedErrorHandler
- Enhanced MT5 connection management
- Optimized model loading and prediction

### Fixed
- Fixed memory leaks in MT5 connection handling
- Corrected signal generation for edge cases
- Fixed model loading issues for certain model types
- Improved error recovery for network timeouts
