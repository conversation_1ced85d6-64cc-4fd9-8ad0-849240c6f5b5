# Multi-Terminal Model Implementations

## Overview

The multi-terminal trading bot implements **9 different model types**, each designed for specific trading scenarios and timeframes across 5 MT5 terminals. All models inherit from the `BaseModel` class, use standardized interfaces, and support GPU acceleration with NVIDIA RTX 4070.

## Model Architecture Foundation

### Base Model Class (`models/base_model.py`)

All models inherit from the standardized `BaseModel` abstract class:

```python
class BaseModel(ABC):
    def __init__(self, config: Dict[str, Any]):
        """Initialize with terminal-specific configuration."""
        self.config = config
        self.model_name = config.get('model_name')
        self.timeframe = config.get('timeframe', 'M5')
        self.terminal_id = str(config.get('terminal_id', "1"))

        # Standardized path: models/terminal_X/timeframe/model_name/
        self._setup_model_paths()

        # Training state management
        self.is_trained = False
        self.training_history = []
        self.performance_metrics = {}

    def _setup_model_paths(self):
        """Set up standardized model directory structure."""
        models_base_path = Path(self.config.get('models_base_path', 'models'))
        normalized_terminal_id = str(self.terminal_id).replace("terminal", "")
        self.model_dir = models_base_path / f"terminal_{normalized_terminal_id}" / self.timeframe / f"{self.model_name}_model"
        self.model_dir.mkdir(parents=True, exist_ok=True)
```

## Model Types

### 1. LSTM Model (`models/lstm_model.py`) - Terminal 1 Primary

**Implementation**: PyTorch-based Long Short-Term Memory network with GPU acceleration
**Use Case**: Sequential pattern recognition in price data, primary model for Terminal 1
**Architecture**:
```python
class LSTMNetwork(nn.Module):
    def __init__(self, input_dim, hidden_units, num_layers, output_dim, dropout_rate):
        super().__init__()
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_units,
            num_layers=num_layers,
            dropout=dropout_rate if num_layers > 1 else 0,
            batch_first=True
        )
        self.dropout = nn.Dropout(dropout_rate)
        self.output_layer = nn.Linear(hidden_units, output_dim)

    def forward(self, x):
        lstm_out, (hidden, cell) = self.lstm(x)
        # Use the last output for prediction
        last_output = lstm_out[:, -1, :]
        dropped = self.dropout(last_output)
        output = self.output_layer(dropped)
        return output
```

**Features**:
- Multi-layer LSTM with dropout regularization
- Batch-first processing for efficiency
- Dense output layer with linear activation
- GPU/CPU compatibility with automatic device detection
- Feature scaling with StandardScaler
- Gradient clipping to prevent exploding gradients
- Early stopping with patience mechanism

**Configuration**:
```json
{
  "model_type": "lstm",
  "input_dim": 5,
  "sequence_length": 60,
  "hidden_units": 64,
  "num_layers": 2,
  "dropout_rate": 0.2,
  "learning_rate": 0.001,
  "batch_size": 32,
  "epochs": 100,
  "FEATURE_COLUMNS": ["open", "high", "low", "close", "tick_volume"]
}
```

### 2. GRU Model (`models/gru_model.py`) - Terminal 2 Primary

**Implementation**: PyTorch-based Gated Recurrent Unit network
**Use Case**: Efficient sequential modeling with fewer parameters than LSTM
**Architecture**:
```python
class GRUNetwork(nn.Module):
    def __init__(self, input_dim, hidden_units, num_layers, output_dim, dropout_rate):
        super().__init__()
        self.gru = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_units,
            num_layers=num_layers,
            dropout=dropout_rate if num_layers > 1 else 0,
            batch_first=True
        )
        self.dropout = nn.Dropout(dropout_rate)
        self.output_layer = nn.Linear(hidden_units, output_dim)
```

**Features**:
- Simpler architecture than LSTM with comparable performance
- Faster training and inference
- Lower memory requirements
- GPU acceleration support

### 3. Transformer Model (`models/transformer_model.py`)

**Implementation**: PyTorch-based Transformer with multi-head attention
**Use Case**: Advanced pattern recognition with attention mechanisms
**Architecture**:
```python
class TransformerNetwork(nn.Module):
    def __init__(self, input_dim, d_model, nhead, num_layers, output_dim, dropout_rate):
        super().__init__()
        self.input_projection = nn.Linear(input_dim, d_model)
        self.positional_encoding = PositionalEncoding(d_model, dropout_rate)

        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dropout=dropout_rate,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        self.output_layer = nn.Linear(d_model, output_dim)
```

**Features**:
- Multi-head self-attention mechanism
- Positional encoding for sequence understanding
- Parallel processing capabilities
- State-of-the-art performance on sequence tasks

**Configuration**:
```json
{
  "model_type": "transformer",
  "input_dim": 5,
  "sequence_length": 60,
  "d_model": 64,
  "nhead": 8,
  "num_layers": 2,
  "dropout_rate": 0.1,
  "learning_rate": 0.001,
  "batch_size": 32,
  "epochs": 100
}
```

### 4. TFT Model (`models/tft_model.py`) - Terminal 3 Primary

**Implementation**: Temporal Fusion Transformer using PyTorch Forecasting
**Use Case**: Advanced time series forecasting with attention mechanisms and interpretability
**Architecture**:
```python
# Uses PyTorch Forecasting's TemporalFusionTransformer
from pytorch_forecasting import TemporalFusionTransformer, TimeSeriesDataSet

self.model = TemporalFusionTransformer.from_dataset(
    dataset,
    learning_rate=0.03,
    hidden_size=16,
    attention_head_size=4,
    dropout=0.1,
    lstm_layers=1,
    loss=QuantileLoss()
)
```

**Features**:
- Variable selection networks for feature importance
- Temporal attention mechanisms
- Static and dynamic covariates support
- Quantile regression for uncertainty estimation
- PyTorch Lightning 2.x compatibility
- GPU/CPU automatic device handling
- Memory optimization for large datasets
- Configurable dataset size limits
- Automatic target normalization
- Fallback prediction mechanism for empty predictions

**Configuration**:
```json
{
  "model_type": "tft",
  "max_encoder_length": 60,
  "max_prediction_length": 1,
  "hidden_size": 16,
  "lstm_layers": 1,
  "num_attention_heads": 4,
  "dropout": 0.1,
  "learning_rate": 0.03,
  "batch_size": 64,
  "epochs": 100,
  "max_samples_per_dataset": 50000,
  "enable_memory_optimization": true
}
```

**Memory Optimization**:
- **max_samples_per_dataset**: Limits dataset size to prevent OOM errors (default: 10,000)
- **enable_memory_optimization**: Enables/disables memory optimization (default: true)
- **Automatic scaling**: Handles large datasets by intelligent sampling
- **GPU memory management**: Optimizes CUDA memory usage during training
- **Fallback mechanism**: Returns last known value if prediction fails

### 5. XGBoost Model (`models/xgboost_model.py`) - Terminal 4 Primary

**Implementation**: Gradient boosting decision trees with advanced hyperparameter optimization
**Use Case**: Non-linear pattern recognition with feature importance analysis
**Features**:
- Tree-based ensemble learning
- Feature importance analysis and selection
- Cross-validation with early stopping
- Hyperparameter optimization
- Robust handling of missing values
- Built-in regularization (L1/L2)

**Configuration**:
```json
{
  "model_type": "xgboost",
  "max_depth": 6,
  "n_estimators": 100,
  "learning_rate": 0.01,
  "subsample": 0.8,
  "colsample_bytree": 0.8,
  "random_state": 42,
  "reg_alpha": 0.1,
  "reg_lambda": 0.1
}
```

**Input Features**: ["open", "high", "low", "close", "tick_volume", "rsi", "macd", "bb_upper", "bb_lower", "atr"]

### 6. LightGBM Model (`models/lightgbm_model.py`) - Terminal 5 Primary

**Implementation**: Microsoft's gradient boosting framework with leaf-wise tree growth
**Use Case**: Fast and efficient tree-based learning with memory optimization
**Features**:
- Leaf-wise tree growth for better accuracy
- Categorical feature support without preprocessing
- Memory efficiency and fast training
- GPU acceleration support
- Built-in feature selection
- Network communication for distributed training

**Configuration**:
```json
{
  "model_type": "lightgbm",
  "max_depth": 6,
  "n_estimators": 100,
  "num_leaves": 31,
  "learning_rate": 0.01,
  "subsample": 0.8,
  "colsample_bytree": 0.8,
  "random_state": 42,
  "reg_alpha": 0.1,
  "reg_lambda": 0.1
}
```

**Input Features**: ["open", "high", "low", "close", "tick_volume", "rsi", "macd", "bb_upper", "bb_lower", "atr"]

### 7. ARIMA Model (`models/arima_model.py`) - Statistical Baseline

**Implementation**: AutoRegressive Integrated Moving Average using statsmodels
**Use Case**: Traditional time series forecasting with trend and seasonality analysis
**Features**:
- Automatic order selection (p,d,q) with auto_arima
- Seasonal ARIMA support (SARIMA)
- Statistical significance testing
- Residual analysis and diagnostics
- Box-Jenkins methodology
- AIC/BIC model selection criteria

**Configuration**:
```json
{
  "model_type": "arima",
  "order": [1, 1, 1],
  "seasonal_order": [0, 0, 0, 0],
  "trend": "c",
  "method": "lbfgs",
  "maxiter": 50,
  "auto_arima": true,
  "max_p": 5,
  "max_q": 5,
  "max_d": 2
}
```

### 8. LSTM-ARIMA Ensemble (`models/ensemble_model.py`) - Hybrid Neural-Statistical

**Implementation**: Ensemble combining LSTM neural network and ARIMA statistical predictions
**Use Case**: Leveraging both neural network pattern recognition and statistical time series analysis
**Features**:
- Weighted ensemble predictions with configurable weights
- Individual model performance tracking and validation
- Adaptive weight adjustment based on recent performance
- Fallback to individual models if ensemble fails
- Cross-validation for weight optimization
- Uncertainty quantification from both models

**Configuration**:
```json
{
  "model_type": "lstm_arima",
  "lstm_weight": 0.7,
  "arima_weight": 0.3,
  "ensemble_method": "weighted_average",
  "adaptive_weights": true,
  "performance_window": 100
}
```

### 9. TFT-ARIMA Ensemble (`models/ensemble_model.py`) - Advanced Hybrid

**Implementation**: Ensemble combining Temporal Fusion Transformer and ARIMA predictions
**Use Case**: Advanced ensemble with attention-based neural networks and statistical models
**Features**:
- Dynamic weight adjustment based on prediction confidence
- Performance-based weighting with rolling window evaluation
- Uncertainty quantification from both model types
- Model health monitoring and automatic fallback
- Attention-based feature importance from TFT
- Statistical validation from ARIMA

**Configuration**:
```json
{
  "model_type": "tft_arima",
  "tft_weight": 0.8,
  "arima_weight": 0.2,
  "ensemble_method": "adaptive_weighted",
  "confidence_threshold": 0.6,
  "performance_window": 50
}
```

## Standardized Model Interface

### Base Model Class (`models/base_model.py`)

All 9 models inherit from the standardized `BaseModel` abstract class which provides:

### Core Abstract Methods
- `build()`: Model architecture construction with GPU/CPU detection
- `train(X_train, y_train, X_val, y_val)`: Training interface with validation
- `predict(X)`: Prediction interface with error handling
- `save_model(path)`: Model persistence with metadata
- `load_model(path)`: Model loading with validation
- `evaluate(X_test, y_test)`: Performance evaluation with comprehensive metrics

### Standardized Features
- **Path Management**: Automatic model directory creation (`models/terminal_X/timeframe/model_name/`)
- **GPU Support**: Automatic device detection and CUDA memory management
- **Error Handling**: Comprehensive error handling with fallback mechanisms
- **Metadata Storage**: Training history, performance metrics, and configuration
- **Validation**: Model health checking and integrity validation

### Performance Metrics
- **Regression Metrics**: MAE, MSE, RMSE, R², MAPE
- **Trading Metrics**: Directional accuracy, profit/loss correlation
- **Statistical Tests**: Residual analysis, normality tests
- **Model Diagnostics**: Overfitting detection, convergence analysis

## Multi-Terminal Training Pipeline

### 1. Data Preparation (Multi-Terminal)
```python
# Collect data from all 5 terminals
from collect_from_all_terminals import collect_data_all_terminals

# Load and preprocess data for specific terminal/timeframe
data = load_training_data(terminal_id="1", timeframe="M5", symbol="BTCUSD.a")
X_train, y_train, X_val, y_val = prepare_features_targets(data)

# Apply feature scaling
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_val_scaled = scaler.transform(X_val)
```

### 2. Model Initialization (Terminal-Specific)
```python
# Create model instance with terminal-specific configuration
config = config_manager.get_model_config(model_name="lstm")
config.update({
    "terminal_id": "1",
    "timeframe": "M5",
    "models_base_path": "models"
})

model = LSTMModel(config)
model.build()  # Automatically detects GPU/CPU
```

### 3. Training Process (GPU Accelerated)
```python
# Train with validation and GPU acceleration
training_history = model.train(
    X_train_scaled, y_train,
    X_val_scaled, y_val,
    epochs=100,
    batch_size=32,
    early_stopping=True,
    patience=10
)

# Monitor training progress
logger.info(f"Training completed. Final validation loss: {training_history['val_loss'][-1]}")
```

### 4. Evaluation and Deployment
```python
# Comprehensive evaluation
test_metrics = model.evaluate(X_test_scaled, y_test)
logger.info(f"Test R²: {test_metrics['r2']:.4f}, MAE: {test_metrics['mae']:.4f}")

# Save trained model with metadata
model.save_model()  # Saves to models/terminal_1/M5/lstm_model/

# Validate model health
is_healthy = model_manager.validate_model_health(model)
logger.info(f"Model health check: {'PASSED' if is_healthy else 'FAILED'}")
```

## Model Storage Structure

```
models/
├── terminal_1/
│   ├── M5/
│   │   ├── lstm_model/
│   │   │   ├── lstm_model.pt
│   │   │   ├── scaler.pkl
│   │   │   └── metadata.json
│   │   ├── gru_model/
│   │   └── ...
│   ├── M15/
│   └── ...
├── terminal_2/
├── terminal_3/
├── terminal_4/
└── terminal_5/
```

## GPU Acceleration Support

All PyTorch models (LSTM, GRU, Transformer, TFT) support GPU acceleration:

- **Automatic Device Detection**: Models automatically detect and use NVIDIA RTX 4070
- **Memory Management**: Intelligent CUDA memory management and cleanup
- **Batch Processing**: Optimized batch sizes for 12.88GB VRAM
- **Mixed Precision**: Optional FP16 training for memory efficiency
