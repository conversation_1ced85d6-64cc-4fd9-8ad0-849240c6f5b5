"""
Logging configuration utility with rotation and analysis capabilities.
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional
import json
from datetime import datetime, timedelta
import re

class LogManager:
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.log_patterns = {
            'error': r'ERROR.*',
            'warning': r'WARNING.*',
            'critical': r'CRITICAL.*',
            'memory': r'Memory.*',
            'trading': r'Trading.*'
        }
    
    def setup_logging(self, log_level: str = "INFO") -> None:
        """Configure logging with rotation and formatting."""
        # Main log file
        main_log = self.log_dir / "trading_bot.log"
        main_handler = logging.handlers.RotatingFileHandler(
            main_log,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        
        # Error log file
        error_log = self.log_dir / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        
        # Console handler
        console_handler = logging.StreamHandler()
        
        # Formatters
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        main_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        root_logger.addHandler(main_handler)
        root_logger.addHandler(error_handler)
        root_logger.addHandler(console_handler)
    
    def analyze_logs(self, hours: int = 24) -> Dict[str, Any]:
        """Analyze log files for patterns and statistics."""
        results = {
            'error_count': 0,
            'warning_count': 0,
            'critical_count': 0,
            'memory_issues': 0,
            'trading_events': 0,
            'patterns': {}
        }
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Analyze main log
        main_log = self.log_dir / "trading_bot.log"
        if main_log.exists():
            with open(main_log, 'r') as f:
                for line in f:
                    try:
                        # Parse timestamp
                        timestamp_str = line[:23]  # Assuming standard format
                        log_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                        
                        if log_time >= cutoff_time:
                            # Check patterns
                            for pattern_name, pattern in self.log_patterns.items():
                                if re.search(pattern, line):
                                    results['patterns'].setdefault(pattern_name, 0)
                                    results['patterns'][pattern_name] += 1
                            
                            # Count log levels
                            if 'ERROR' in line:
                                results['error_count'] += 1
                            elif 'WARNING' in line:
                                results['warning_count'] += 1
                            elif 'CRITICAL' in line:
                                results['critical_count'] += 1
                    except Exception as e:
                        print(f"Error parsing log line: {str(e)}")
        
        return results
    
    def cleanup_old_logs(self, days: int = 30) -> None:
        """Remove log files older than specified days."""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for log_file in self.log_dir.glob('*.log*'):
            try:
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_time:
                    log_file.unlink()
            except Exception as e:
                print(f"Error removing log file {log_file}: {str(e)}")
    
    def get_log_summary(self) -> Dict[str, Any]:
        """Get summary of current log state."""
        summary = {
            'total_size': 0,
            'file_count': 0,
            'oldest_log': None,
            'newest_log': None
        }
        
        for log_file in self.log_dir.glob('*.log*'):
            try:
                file_size = log_file.stat().st_size
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                
                summary['total_size'] += file_size
                summary['file_count'] += 1
                
                if summary['oldest_log'] is None or file_time < summary['oldest_log']:
                    summary['oldest_log'] = file_time
                if summary['newest_log'] is None or file_time > summary['newest_log']:
                    summary['newest_log'] = file_time
            except Exception as e:
                print(f"Error processing log file {log_file}: {str(e)}")
        
        return summary

def main():
    """Run logging configuration and analysis."""
    log_manager = LogManager()
    
    # Setup logging
    log_manager.setup_logging()
    
    # Analyze recent logs
    analysis = log_manager.analyze_logs()
    print("Log Analysis Results:")
    print(json.dumps(analysis, indent=2))
    
    # Get log summary
    summary = log_manager.get_log_summary()
    print("\nLog Summary:")
    print(json.dumps(summary, indent=2))
    
    # Cleanup old logs
    log_manager.cleanup_old_logs()

if __name__ == "__main__":
    main() 