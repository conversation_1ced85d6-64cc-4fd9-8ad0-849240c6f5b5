"""
Configuration package for the trading bot.
Provides centralized configuration management through the ConfigurationManager.
"""
from .consolidated_config import (
    ConfigurationManager,
    TradingConfig,
    MT5Config,
    StrategyConfig,
    ModelConfig,
    SystemConfig,
    MonitoringConfig,
    MT5TerminalConfig
)

# Create a singleton instance for backwards compatibility
config_manager = ConfigurationManager()

# Define helper functions for backwards compatibility
def get_config():
    return config_manager.get_config()

def get_mt5_config():
    return config_manager.get_mt5_config()

def get_strategy_config():
    return config_manager.get_strategy_config()

def get_model_config(model_name):
    return config_manager.get_model_config(model_name)

def get_all_model_configs():
    return config_manager.get_all_model_configs()

def get_system_config():
    return config_manager.get_system_config()

def get_monitoring_config():
    return config_manager.get_monitoring_config()

def get_data_base_path():
    return config_manager.get_config().data_base_path

def get_models_base_path():
    return config_manager.get_config().models_base_path

def get_data_path(terminal_id=None, timeframe=None):
    return config_manager.get_data_path(terminal_id, timeframe)

def get_model_path(model_name, terminal_id=None, timeframe=None):
    return config_manager.get_model_path(model_name, terminal_id, timeframe)

__all__ = [
    'config_manager',
    'get_config',
    'get_mt5_config',
    'get_strategy_config',
    'get_model_config',
    'get_all_model_configs',
    'get_system_config',
    'get_monitoring_config',
    'get_data_base_path',
    'get_models_base_path',
    'get_data_path',
    'get_model_path',
    'TradingConfig',
    'MT5Config',
    'StrategyConfig',
    'ModelConfig',
    'SystemConfig',
    'MonitoringConfig',
    'MT5TerminalConfig',
    'ConfigurationManager'
]