#!/usr/bin/env python3
"""
Setup validation script for the MT5 Trading Bot.
This script validates the entire system setup and identifies any issues.
"""

import sys
import logging
from pathlib import Path
import importlib
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SetupValidator:
    """Validates the trading bot setup and configuration."""

    def __init__(self):
        self.errors = []
        self.warnings = []
        self.project_root = Path(__file__).parent

    def validate_all(self) -> bool:
        """Run all validation checks."""
        logger.info("🔍 Starting comprehensive setup validation...")

        # Core validation checks
        checks = [
            ("Python Environment", self.validate_python_environment),
            ("Project Structure", self.validate_project_structure),
            ("Configuration Files", self.validate_configuration),
            ("Dependencies", self.validate_dependencies),
            ("Model Structure", self.validate_model_structure),
            ("Data Directories", self.validate_data_directories),
            ("Import Consistency", self.validate_imports),
            ("Code Quality", self.validate_code_quality)
        ]

        all_passed = True
        for check_name, check_func in checks:
            logger.info(f"🔧 Running {check_name} validation...")
            try:
                if not check_func():
                    all_passed = False
                    logger.error(f"❌ {check_name} validation failed")
                else:
                    logger.info(f"✅ {check_name} validation passed")
            except Exception as e:
                logger.error(f"❌ {check_name} validation error: {str(e)}")
                self.errors.append(f"{check_name}: {str(e)}")
                all_passed = False

        # Print summary
        self.print_summary()
        return all_passed

    def validate_python_environment(self) -> bool:
        """Validate Python version and virtual environment."""
        # Check Python version
        if sys.version_info < (3, 8):
            self.errors.append("Python 3.8+ required")
            return False

        # Check if in virtual environment
        if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            self.warnings.append("Not running in virtual environment")

        return True

    def validate_project_structure(self) -> bool:
        """Validate project directory structure."""
        required_dirs = [
            "config", "data", "logs", "models", "trading", "utils",
            "monitoring", "scripts", "tests"
        ]

        required_files = [
            "main.py", "requirements.txt", "README.md",
            "config/config.json.example", "run_trading_bot.bat", "run_trading_bot.sh"
        ]

        missing_dirs = []
        missing_files = []

        for dir_name in required_dirs:
            if not (self.project_root / dir_name).exists():
                missing_dirs.append(dir_name)

        for file_name in required_files:
            if not (self.project_root / file_name).exists():
                missing_files.append(file_name)

        if missing_dirs:
            self.errors.append(f"Missing directories: {missing_dirs}")
        if missing_files:
            self.errors.append(f"Missing files: {missing_files}")

        return len(missing_dirs) == 0 and len(missing_files) == 0

    def validate_configuration(self) -> bool:
        """Validate configuration files."""
        config_path = self.project_root / "config" / "config.json"
        example_path = self.project_root / "config" / "config.json.example"

        if not example_path.exists():
            self.errors.append("config.json.example not found")
            return False

        if not config_path.exists():
            self.warnings.append("config.json not found - run setup_config.py")

        # Validate example config structure
        try:
            with open(example_path, 'r') as f:
                config = json.load(f)

            required_sections = ["mt5", "strategy", "models", "data_base_path", "models_base_path"]
            for section in required_sections:
                if section not in config:
                    self.errors.append(f"Missing config section: {section}")
                    return False
        except json.JSONDecodeError as e:
            self.errors.append(f"Invalid JSON in config.json.example: {str(e)}")
            return False

        return True

    def validate_dependencies(self) -> bool:
        """Validate that required dependencies can be imported."""
        required_packages = [
            "numpy", "pandas", "MetaTrader5", "psutil", "joblib",
            "sklearn", "xgboost", "lightgbm"
        ]

        optional_packages = [
            "tensorflow", "torch", "pytorch_lightning", "pytorch_forecasting"
        ]

        missing_required = []
        missing_optional = []

        for package in required_packages:
            try:
                importlib.import_module(package)
            except ImportError:
                missing_required.append(package)

        for package in optional_packages:
            try:
                importlib.import_module(package)
            except ImportError:
                missing_optional.append(package)

        if missing_required:
            self.errors.append(f"Missing required packages: {missing_required}")
        if missing_optional:
            self.warnings.append(f"Missing optional packages: {missing_optional}")

        return len(missing_required) == 0

    def validate_model_structure(self) -> bool:
        """Validate model directory structure."""
        models_dir = self.project_root / "models"

        required_model_files = [
            "base_model.py", "lstm_model.py", "gru_model.py", "tft_model.py",
            "xgboost_model.py", "lightgbm_model.py", "arima_model.py",
            "ensemble_model.py"
        ]

        missing_files = []
        for file_name in required_model_files:
            if not (models_dir / file_name).exists():
                missing_files.append(file_name)

        if missing_files:
            self.errors.append(f"Missing model files: {missing_files}")
            return False

        return True

    def validate_data_directories(self) -> bool:
        """Validate data directory structure."""
        data_dir = self.project_root / "data"

        # Create directories if they don't exist
        required_subdirs = ["raw", "processed", "cache", "validation"]
        for subdir in required_subdirs:
            (data_dir / subdir).mkdir(parents=True, exist_ok=True)

        # Check for timeframe-specific data
        timeframes = ["M5", "M15", "M30", "H1", "H4"]
        processed_dir = data_dir / "processed"

        missing_data = []
        for split in ["train", "validation", "test"]:
            split_dir = processed_dir / split
            split_dir.mkdir(parents=True, exist_ok=True)

            for timeframe in timeframes:
                data_file = split_dir / f"BTCUSD.a_{timeframe}.parquet"
                if not data_file.exists():
                    missing_data.append(f"{split}/BTCUSD.a_{timeframe}.parquet")

        if missing_data:
            self.warnings.append(f"Missing training data files: {missing_data[:5]}{'...' if len(missing_data) > 5 else ''}")
            self.warnings.append("Run 'python prepare_training_data.py' to prepare data for all timeframes")

        return True

    def validate_imports(self) -> bool:
        """Validate that all imports work correctly."""
        try:
            # Test critical imports
            config_module = importlib.import_module('config')
            error_handler_module = importlib.import_module('utils.enhanced_error_handler')
            model_manager_module = importlib.import_module('utils.model_manager')
            trading_bot_module = importlib.import_module('trading.bot')

            # Verify key classes exist
            assert hasattr(config_module, 'ConfigurationManager')
            assert hasattr(error_handler_module, 'EnhancedErrorHandler')
            assert hasattr(model_manager_module, 'ModelManager')
            assert hasattr(trading_bot_module, 'TradingBot')

            logger.info("All critical imports successful")
            return True
        except (ImportError, AssertionError) as e:
            self.errors.append(f"Import error: {str(e)}")
            return False

    def validate_code_quality(self) -> bool:
        """Basic code quality checks."""
        # Check for common issues in key files
        key_files = [
            "main.py", "trading/bot.py", "utils/model_manager.py",
            "config/consolidated_config.py"
        ]

        issues = []
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check for deprecated pandas methods
                if 'fillna(method=' in content:
                    issues.append(f"{file_path}: Contains deprecated fillna(method=)")

                # Check for proper error handling
                if 'except:' in content and 'except Exception:' not in content:
                    issues.append(f"{file_path}: Contains bare except clause")

        if issues:
            self.warnings.extend(issues)

        return True

    def print_summary(self):
        """Print validation summary."""
        logger.info("\n" + "="*60)
        logger.info("🔍 SETUP VALIDATION SUMMARY")
        logger.info("="*60)

        if not self.errors and not self.warnings:
            logger.info("✅ All validations passed! System is ready.")
        else:
            if self.errors:
                logger.error(f"❌ {len(self.errors)} ERRORS found:")
                for error in self.errors:
                    logger.error(f"   • {error}")

            if self.warnings:
                logger.warning(f"⚠️  {len(self.warnings)} WARNINGS found:")
                for warning in self.warnings:
                    logger.warning(f"   • {warning}")

        logger.info("="*60)

def main():
    """Main validation function."""
    validator = SetupValidator()
    success = validator.validate_all()

    if success:
        logger.info("🎉 Setup validation completed successfully!")
        return 0
    else:
        logger.error("❌ Setup validation failed. Please fix the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
