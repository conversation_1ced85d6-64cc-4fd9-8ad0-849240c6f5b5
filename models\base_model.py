"""
Base model class for all machine learning models.
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import numpy as np
from pathlib import Path
from datetime import datetime
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

logger = logging.getLogger(__name__)

class BaseModel(ABC):
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the base model with a configuration dictionary.

        Args:
            config (Dict[str, Any]): Model-specific configuration dictionary.
                                      Expected keys include: model_name, timeframe,
                                      terminal_id, models_base_path, model_filename,
                                      input_dim, output_dim, FEATURE_COLUMNS, etc.
        """
        self.config = config # Store the passed config dictionary
        self.model = None
        self.history = None

        # Extract essential info from config, using .get() for safety/defaults
        self.model_name = self.config.get('model_name')
        self.timeframe = self.config.get('timeframe', 'M5')
        self.terminal_id = str(self.config.get('terminal_id', "1")) # Ensure string

        if not self.model_name:
             raise ValueError("Configuration must include a 'model_name' key.")

        # Validate configuration essentials (example)
        required_keys = ['models_base_path', 'model_filename', 'input_dim', 'output_dim', 'FEATURE_COLUMNS']
        for key in required_keys:
            if key not in self.config:
                # Allow FEATURE_COLUMNS to be potentially empty list, but key should exist if expected
                 if key == 'FEATURE_COLUMNS' and self.config.get(key) is not None:
                     continue
                 # Log warning or raise error based on strictness
                 logger.warning(f"Configuration for model '{self.model_name}' might be missing expected key: '{key}'")
                 # raise ValueError(f"Configuration for model '{self.model_name}' is missing required key: '{key}'")


        # Set up model path using standardized path methods
        try:
            # Use the standardized path methods from config (lazy import to avoid circular imports)
            try:
                from config import get_model_path
            except ImportError:
                logger.warning("Could not import get_model_path from config, using fallback method")
                get_model_path = None

            # Normalize terminal ID for consistency
            try:
                from utils.common import normalize_terminal_id
                normalized_terminal_id = normalize_terminal_id(self.terminal_id)
            except ImportError:
                normalized_terminal_id = str(self.terminal_id)

            # Get the model directory using standardized path method
            if get_model_path is not None:
                self.model_dir = get_model_path(self.model_name, normalized_terminal_id, self.timeframe)
            else:
                # Fallback to direct path construction
                raise ImportError("get_model_path not available, using fallback method")

            # Ensure parent directories exist
            self.model_dir.parent.mkdir(parents=True, exist_ok=True)

            # Get the appropriate file extension for this model type
            extension = self._get_model_extension()

            # Construct the full model path with filename
            model_filename = self.config.get('model_path')  # Use model_path from config if available

            # If model_path is not specified or is just a filename without path
            if not model_filename or '/' not in model_filename and '\\' not in model_filename:
                # Use standardized naming convention: model_name_model.extension
                model_filename = f"{self.model_name}_model{extension}"
                logger.info(f"Using standardized model filename: {model_filename}")

            # Set the full model path
            self.model_path = self.model_dir / model_filename

            logger.info(f"Model path set to: {self.model_path}")

        except (ImportError, AttributeError) as e:
            # Fallback to standardized path construction
            logger.warning(f"Standardized path methods not available: {str(e)}. Using direct standardized path construction.")

            # Use standardized model paths with proper terminal-specific structure
            models_base_path = Path(self.config.get('models_base_path', 'models'))

            # Normalize terminal ID
            normalized_terminal_id = str(self.terminal_id).replace("terminal", "")

            # Use the standardized path structure: models/terminal_X/timeframe/model_name/
            self.model_dir = models_base_path / f"terminal_{normalized_terminal_id}" / self.timeframe / f"{self.model_name}_model"

            # Ensure directory exists
            self.model_dir.mkdir(parents=True, exist_ok=True)

            # Get the appropriate file extension
            extension = self._get_model_extension()

            # Use standardized filename: {model_name}_model{extension}
            model_filename = f"{self.model_name}_model{extension}"

            # Set the full model path
            self.model_path = self.model_dir / model_filename

            logger.info(f"Model path set to: {self.model_path} (standardized fallback method)")

        # Initialize training history
        self.training_history = []
        self.last_training_date = None
        self.performance_metrics = {}
        self.initialized = False # Consider setting True after successful init

        # Training state
        self.is_trained = False
        self.training_start_time = None
        self.training_end_time = None

    @abstractmethod
    def build(self) -> None:
        """Build the model architecture."""
        pass

    @abstractmethod
    def train(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[tuple] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Train the model.

        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data tuple (X_val, y_val)
            **kwargs: Additional training parameters

        Returns:
            Dictionary containing training metrics and history
        """
        pass

    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions."""
        pass

    def train_with_validation(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Standardized training method with validation and error handling.

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            **kwargs: Additional training parameters

        Returns:
            Dictionary containing training metrics and history
        """
        try:
            self.training_start_time = datetime.now()
            logger.info(f"Starting training for {self.model_name} at {self.training_start_time}")

            # Validate inputs
            if X_train is None or y_train is None:
                raise ValueError("Training data cannot be None")

            if len(X_train) != len(y_train):
                raise ValueError("Training features and targets must have same length")

            # Prepare validation data
            validation_data = None
            if X_val is not None and y_val is not None:
                if len(X_val) != len(y_val):
                    raise ValueError("Validation features and targets must have same length")
                validation_data = (X_val, y_val)

            # Call the model-specific training method
            training_result = self.train(X_train, y_train, validation_data, **kwargs)

            # Update training state
            self.is_trained = True
            self.training_end_time = datetime.now()
            self.last_training_date = self.training_end_time

            # Store training history
            if isinstance(training_result, dict):
                self.training_history.append({
                    'timestamp': self.training_end_time,
                    'metrics': training_result,
                    'training_duration': (self.training_end_time - self.training_start_time).total_seconds()
                })

            logger.info(f"Training completed for {self.model_name} at {self.training_end_time}")
            logger.info(f"Training duration: {(self.training_end_time - self.training_start_time).total_seconds():.2f} seconds")

            return training_result

        except Exception as e:
            logger.error(f"Error during training for {self.model_name}: {str(e)}", exc_info=True)
            self.training_end_time = datetime.now()
            raise

    def save(self, path_prefix: str = None) -> bool:
        """
        Save the model, using either the provided path prefix or the model's path.

        Args:
            path_prefix (str, optional): The base path and filename without extension.
                                        If None, uses self.model_path.

        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            if not self.model:
                logger.error(f"Cannot save model {self.model_name}: Model not built or trained")
                return False

            # Use provided path_prefix or self.model_path
            if path_prefix is None:
                # Use the model path constructed during initialization
                full_path = self.model_path
            else:
                # If path_prefix is provided, construct the full path with extension
                extension = self._get_model_extension()

                # Check if path_prefix already has an extension
                if Path(path_prefix).suffix:
                    # Use the provided path as is
                    full_path = Path(path_prefix)
                else:
                    # Append the model-specific extension
                    full_path = Path(f"{path_prefix}{extension}")

            # Ensure directory exists
            full_path.parent.mkdir(parents=True, exist_ok=True)

            # Save model using model-specific method
            if hasattr(self, 'save_model'):
                # Convert to string for compatibility with model-specific save methods
                self.save_model(str(full_path))
                logger.info(f"Model {self.model_name} saved using model-specific save_model method to {full_path}")
            else:
                # Try to infer the model type and use appropriate save method
                model_type = self.config.get('model_type', '').lower()

                if hasattr(self.model, 'save_model'):
                    # Some models have their own save_model method
                    self.model.save_model(str(full_path))
                    logger.info(f"Model {self.model_name} saved using model's save_model method to {full_path}")
                elif 'torch' in model_type or 'pytorch' in model_type:
                    # PyTorch model
                    import torch
                    torch.save(self.model.state_dict(), str(full_path))
                    logger.info(f"PyTorch model {self.model_name} saved to {full_path}")
                elif 'tensorflow' in model_type or 'keras' in model_type:
                    # Legacy TensorFlow/Keras model - convert to PyTorch
                    logger.warning(f"TensorFlow/Keras model format detected for {self.model_name}. Please retrain with PyTorch.")
                    return False
                elif 'sklearn' in model_type:
                    # Scikit-learn model
                    import joblib
                    joblib.dump(self.model, str(full_path))
                    logger.info(f"Scikit-learn model {self.model_name} saved to {full_path}")
                elif 'xgboost' in model_type:
                    # XGBoost model
                    self.model.save_model(str(full_path))
                    logger.info(f"XGBoost model {self.model_name} saved to {full_path}")
                elif 'lightgbm' in model_type:
                    # LightGBM model
                    self.model.save_model(str(full_path))
                    logger.info(f"LightGBM model {self.model_name} saved to {full_path}")
                else:
                    # Unknown model type
                    error_msg = f"Model type '{model_type}' for '{self.model_name}' must implement a specific 'save_model(full_path)' method or handle saving in BaseModel.save"
                    logger.error(error_msg)
                    raise NotImplementedError(error_msg)

            # Save additional metadata if needed
            self._save_metadata(full_path)

            logger.info(f"Model {self.model_name} saved successfully to {full_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving model {self.model_name}: {str(e)}", exc_info=True)
            return False

    def load(self, path_prefix: str = None) -> bool:
        """
        Load the model, using either the provided path prefix or the model's path.

        Args:
            path_prefix (str, optional): The base path and filename without extension.
                                        If None, uses self.model_path.

        Returns:
            bool: True if load was successful, False otherwise
        """
        try:
            # Use provided path_prefix or self.model_path
            if path_prefix is None:
                # Use the model path constructed during initialization
                full_path = self.model_path
            else:
                # If path_prefix is provided, construct the full path with extension
                extension = self._get_model_extension()

                # Check if path_prefix already has an extension
                if Path(path_prefix).suffix:
                    # Use the provided path as is
                    full_path = Path(path_prefix)
                else:
                    # Append the model-specific extension
                    full_path = Path(f"{path_prefix}{extension}")

            # Check if the model file exists
            if not full_path.exists():
                logger.error(f"Model file not found: {full_path}")
                return False

            # Load model using model-specific method
            if hasattr(self, 'load_model'):
                # Convert to string for compatibility with model-specific load methods
                self.load_model(str(full_path))
                logger.info(f"Model {self.model_name} loaded using model-specific load_model method from {full_path}")
            else:
                # Try to infer the model type and use appropriate load method
                model_type = self.config.get('model_type', '').lower()

                if hasattr(self.model, 'load_model'):
                    # Some models have their own load_model method
                    self.model.load_model(str(full_path))
                    logger.info(f"Model {self.model_name} loaded using model's load_model method from {full_path}")
                elif 'torch' in model_type or 'pytorch' in model_type:
                    # PyTorch model
                    import torch
                    # Ensure model architecture is built first
                    if not self.model:
                        self.build()
                    self.model.load_state_dict(torch.load(str(full_path)))
                    logger.info(f"PyTorch model {self.model_name} loaded from {full_path}")
                elif 'tensorflow' in model_type or 'keras' in model_type:
                    # Legacy TensorFlow/Keras model - convert to PyTorch
                    logger.warning(f"TensorFlow/Keras model format detected for {self.model_name}. Please retrain with PyTorch.")
                    return False
                elif 'sklearn' in model_type:
                    # Scikit-learn model
                    import joblib
                    self.model = joblib.load(str(full_path))
                    logger.info(f"Scikit-learn model {self.model_name} loaded from {full_path}")
                elif 'xgboost' in model_type:
                    # XGBoost model - check if this is an XGBoost-specific class
                    if self.__class__.__name__ == 'XGBoostModel':
                        # This is the XGBoost model class, call its specific load_model method
                        # But we need to avoid infinite recursion, so call the method directly
                        from models.xgboost_model import XGBoostModel
                        XGBoostModel.load_model(self, str(full_path))
                    else:
                        # Fallback to basic XGBoost loading for base class
                        import xgboost as xgb
                        # Build the model first to get proper structure
                        if not self.model:
                            self.build()
                        if self.model:
                            self.model.load_model(str(full_path))
                            logger.info(f"XGBoost model {self.model_name} loaded from {full_path}")
                        else:
                            raise RuntimeError(f"Failed to build XGBoost model {self.model_name} before loading")
                elif 'lightgbm' in model_type:
                    # LightGBM model
                    import lightgbm as lgb
                    self.model = lgb.Booster(model_file=str(full_path))
                    logger.info(f"LightGBM model {self.model_name} loaded from {full_path}")
                else:
                    # Unknown model type
                    error_msg = f"Model type '{model_type}' for '{self.model_name}' must implement a specific 'load_model(full_path)' method or handle loading in BaseModel.load"
                    logger.error(error_msg)
                    raise NotImplementedError(error_msg)

            # Load metadata if available
            self._load_metadata(full_path)

            logger.info(f"Model {self.model_name} loaded successfully from {full_path}")
            return True

        except Exception as e:
            logger.error(f"Error loading model {self.model_name}: {str(e)}", exc_info=True)
            return False

    def _get_model_extension(self) -> str:
        """
        Return the file extension for the specific model type (e.g., '.xgb', '.pt', '.joblib').

        This method determines the appropriate file extension based on the model type.
        It can be overridden by child classes for model-specific extensions.

        Returns:
            str: The file extension including the dot (e.g., '.pt')
        """
        # Get model type from config, falling back to model_name if not specified
        model_type = self.config.get('model_type', '').lower()
        if not model_type:
            model_type = self.model_name.lower()

        # Map model types to appropriate extensions according to requirements
        if any(x in model_type for x in ['arima', 'lstm_arima', 'tft_arima']):
            return ".pkl"  # ARIMA and ensemble models use .pkl
        elif any(x in model_type for x in ['lstm', 'tft', 'gru', 'transformer']):
            return ".pt"   # Neural network models use .pt (PyTorch format)
        elif any(x in model_type for x in ['xgboost', 'xgb']):
            return ".xgb"  # XGBoost models use .xgb (native binary format)
        elif any(x in model_type for x in ['lightgbm', 'lgb']):
            return ".txt"   # LightGBM models use .txt (native format)
        elif any(x in model_type for x in ['sklearn', 'scikit']):
            return ".joblib"
        else:
            logger.warning(f"Unknown model type '{model_type}' for model '{self.model_name}'. Using generic '.pkl' extension.")
            return ".pkl"  # Fallback to .pkl for compatibility

    def _save_metadata(self, model_path: Path) -> None:
        """
        Save model metadata alongside the model file.

        This method saves important model metadata such as feature columns,
        training history, and performance metrics to help with model management.

        Args:
            model_path (Path): Path to the saved model file
        """
        try:
            import json

            # Create metadata path by replacing the extension with .json
            metadata_path = model_path.with_suffix('.json')

            # Prepare metadata dictionary
            metadata = {
                'model_name': self.model_name,
                'model_type': self.config.get('model_type', ''),
                'timeframe': self.timeframe,
                'terminal_id': self.terminal_id,
                'feature_columns': self.config.get('FEATURE_COLUMNS', []),
                'input_dim': self.config.get('input_dim'),
                'output_dim': self.config.get('output_dim'),
                'last_training_date': str(self.last_training_date) if self.last_training_date else None,
                'performance_metrics': {k: float(v) if isinstance(v, (int, float, np.number)) else str(v)
                                       for k, v in self.performance_metrics.items()} if self.performance_metrics else {},
                'saved_at': str(datetime.now())
            }

            # Save metadata to file
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=4)

            logger.info(f"Model metadata saved to {metadata_path}")

        except Exception as e:
            logger.error(f"Error saving model metadata: {str(e)}", exc_info=True)
            # Don't raise the exception - this is a non-critical operation

    def _load_metadata(self, model_path: Path) -> None:
        """
        Load model metadata from a JSON file alongside the model file.

        This method loads important model metadata such as feature columns,
        training history, and performance metrics to help with model management.

        Args:
            model_path (Path): Path to the loaded model file
        """
        try:
            import json

            # Create metadata path by replacing the extension with .json
            metadata_path = model_path.with_suffix('.json')

            # Check if metadata file exists
            if not metadata_path.exists():
                logger.info(f"No metadata file found at {metadata_path}")
                return

            # Load metadata from file
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)

            # Update model attributes from metadata
            if 'feature_columns' in metadata and metadata['feature_columns']:
                self.config['FEATURE_COLUMNS'] = metadata['feature_columns']

            if 'last_training_date' in metadata and metadata['last_training_date']:
                try:
                    # Try to parse the date string
                    from dateutil import parser
                    self.last_training_date = parser.parse(metadata['last_training_date'])
                except (ImportError, ValueError):
                    # If parsing fails, store as string
                    self.last_training_date = metadata['last_training_date']

            if 'performance_metrics' in metadata and metadata['performance_metrics']:
                self.performance_metrics = metadata['performance_metrics']

            logger.info(f"Model metadata loaded from {metadata_path}")

        except Exception as e:
            logger.error(f"Error loading model metadata: {str(e)}", exc_info=True)
            # Don't raise the exception - this is a non-critical operation

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the model."""
        # Access config using dictionary style
        feature_cols = self.config.get('FEATURE_COLUMNS')
        if feature_cols is None:
            logger.warning(f"FEATURE_COLUMNS not found in config for {self.model_name}")
            feature_cols = [] # Default to empty list

        return {
            'model_name': self.model_name,
            'timeframe': self.timeframe,
            'terminal_id': self.terminal_id,
            'input_dim': self.config.get('input_dim'), # Use get for safety
            'output_dim': self.config.get('output_dim'), # Use get for safety
            'feature_columns': feature_cols,
            # model_path is now constructed during __init__, potentially using a default.
            # Returning the resolved path might be better.
            'model_path': str(self.model_path)
        }

    def update_performance_metrics(
        self,
        predictions: np.ndarray,
        actual_values: np.ndarray,
        timestamp: datetime
    ) -> None:
        """
        Update model performance metrics.

        Args:
            predictions: Model predictions
            actual_values: Actual values
            timestamp: Current timestamp
        """
        try:
            if predictions.shape != actual_values.shape:
                raise ValueError("Predictions and actual values must have the same shape")

            # Calculate basic metrics
            mse = np.mean((predictions - actual_values) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(predictions - actual_values))

            # Calculate R-squared score
            try:
                r2 = r2_score(actual_values, predictions)
            except ValueError as r2_err:
                logger.warning(f"Could not calculate R2 score in update_performance_metrics: {r2_err}")
                r2 = np.nan

            # Calculate directional accuracy
            correct_direction = np.sum(
                np.sign(predictions[1:] - predictions[:-1]) ==
                np.sign(actual_values[1:] - actual_values[:-1])
            )
            directional_accuracy = correct_direction / (len(predictions) - 1)

            # Update performance metrics
            self.performance_metrics.update({
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'directional_accuracy': directional_accuracy,
                'last_update': timestamp
            })

            logger.info(f"Updated performance metrics: {self.performance_metrics}")

        except Exception as e:
            logger.error(f"Error updating performance metrics: {str(e)}")
            raise

    def check_model_decay(self) -> bool:
        """
        Check if model performance is degrading.

        Returns:
            bool: True if model needs retraining, False otherwise
        """
        try:
            if not self.performance_metrics:
                return False

            # Use .get() for safe access to potentially missing config keys
            accuracy_threshold = self.config.get('decay_accuracy_threshold', 0.5)
            rmse_increase_factor = self.config.get('decay_rmse_increase_factor', 1.2) # e.g., 20% increase

            # Check if directional accuracy is below threshold
            if self.performance_metrics.get('directional_accuracy', 0) < accuracy_threshold:
                logger.warning(f"Model performance degrading: Directional Accuracy ({self.performance_metrics.get('directional_accuracy', 0):.2f}) < Threshold ({accuracy_threshold:.2f})")
                return True

            # Check if RMSE is increasing based on historical training validation RMSE
            if len(self.training_history) > 0: # Check if history exists
                # Assuming history stores dicts like {'val_rmse': value}
                last_val_rmse = self.training_history[-1].get('val_rmse') # Get last validation RMSE from training
                if last_val_rmse is not None:
                     current_rmse = self.performance_metrics.get('rmse')
                     if current_rmse is not None and current_rmse > last_val_rmse * rmse_increase_factor:
                          logger.warning(f"Model performance degrading: Current RMSE ({current_rmse:.4f}) > {rmse_increase_factor:.1f} * Last Train Val RMSE ({last_val_rmse:.4f})")
                          return True
                else:
                     logger.debug("Cannot check RMSE decay: 'val_rmse' not found in last training history entry.")

            return False

        except Exception as e:
            logger.error(f"Error checking model decay: {str(e)}", exc_info=True)
            return False # Fail safe

    def evaluate(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Evaluate model predictions using various metrics"""
        try:
            if y_true.shape != y_pred.shape:
                # Consider checking if shapes are compatible even if not identical (e.g., broadcasting)
                logger.error(f"Shape mismatch in evaluation: y_true {y_true.shape}, y_pred {y_pred.shape}")
                raise ValueError("True values and predictions must have compatible shapes")

            metrics = {}

            # Basic checks for NaNs or Infs which break metrics
            if np.isnan(y_pred).any() or np.isinf(y_pred).any():
                logger.warning("NaN or Inf detected in predictions during evaluation. Metrics may be invalid.")
                # Optionally handle or return specific error metric value
            if np.isnan(y_true).any() or np.isinf(y_true).any():
                 logger.warning("NaN or Inf detected in true values during evaluation. Metrics may be invalid.")

            # Mean Absolute Error
            metrics['mae'] = mean_absolute_error(y_true, y_pred)

            # Mean Squared Error
            metrics['mse'] = mean_squared_error(y_true, y_pred)

            # Root Mean Squared Error
            metrics['rmse'] = np.sqrt(metrics['mse']) # Safe if mse is non-negative

            # Mean Absolute Percentage Error (handle potential division by zero)
            mask = y_true != 0
            if np.sum(mask) > 0:
                 metrics['mape'] = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
            else:
                 metrics['mape'] = np.inf # Or NaN, or some indicator of undefined

            # R-squared score
            try:
                 metrics['r2'] = r2_score(y_true, y_pred)
            except ValueError as r2_err:
                 logger.warning(f"Could not calculate R2 score: {r2_err}")
                 metrics['r2'] = np.nan # Or some indicator value

            return metrics

        except Exception as e:
            logger.error(f"Error evaluating model: {str(e)}", exc_info=True)
            # Instead of raising, maybe return dict with error indicator?
            # raise # Or re-raise if caller should handle
            return {'error': str(e)} # Example error return