# Multi-Terminal MT5 Integration

## Overview

The multi-terminal trading system integrates with **5 MetaTrader 5 terminals** simultaneously for real-time data collection and automated trading execution. Each terminal operates independently with algorithmic trading enabled and specific model assignments.

## Terminal Configuration

### Complete Terminal Setup
All 5 terminals are configured with specific credentials, paths, and model assignments:

```json
{
  "terminals": {
    "1": {
      "login": "61336224",
      "password": "126820Al_",
      "server": "ICMarkets-Demo",
      "path": "C:/Users/<USER>/Desktop/MT5 IC 01/terminal64.exe",
      "primary_model": "lstm"
    },
    "2": {
      "login": "61336225",
      "password": "126820Al_",
      "server": "ICMarkets-Demo",
      "path": "C:/Users/<USER>/Desktop/MT5 IC 02/terminal64.exe",
      "primary_model": "gru"
    },
    "3": {
      "login": "61336226",
      "password": "126820Al_",
      "server": "ICMarkets-Demo",
      "path": "C:/Users/<USER>/Desktop/MT5 IC 03/terminal64.exe",
      "primary_model": "tft"
    },
    "4": {
      "login": "61336227",
      "password": "126820Al_",
      "server": "PepperStone-Demo",
      "path": "C:/Users/<USER>/Desktop/MT5 Pepper 02/terminal64.exe",
      "primary_model": "xgboost"
    },
    "5": {
      "login": "61336228",
      "password": "126820Al_",
      "server": "PepperStone-Demo",
      "path": "C:/Users/<USER>/Desktop/MT5 Pepper 03/terminal64.exe",
      "primary_model": "lightgbm"
    }
  }
}
```

### Terminal-Model Assignments
- **Terminal 1**: LSTM model (Primary) + 8 additional models
- **Terminal 2**: GRU model (Primary) + 8 additional models
- **Terminal 3**: TFT model (Primary) + 8 additional models
- **Terminal 4**: XGBoost model (Primary) + 8 additional models
- **Terminal 5**: LightGBM model (Primary) + 8 additional models

### Algorithmic Trading Status
All terminals have algorithmic trading **ENABLED** for automated execution.

## Connection Management (`trading/mt5_connection_manager.py`)

### Enhanced MT5ConnectionManager Class
Handles all 5 MT5 terminal connections with advanced features:

```python
class MT5ConnectionManager:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.connections = {}
        self.connection_status = {}
        self.algorithmic_trading_status = {}
        self.terminal_processes = {}

    def connect_all_terminals(self) -> Dict[str, bool]:
        """Connect to all 5 terminals simultaneously."""
        results = {}
        terminal_ids = ["1", "2", "3", "4", "5"]

        for terminal_id in terminal_ids:
            success = self.connect_terminal(terminal_id)
            results[terminal_id] = success
            if success:
                self.enable_algorithmic_trading(terminal_id)

        return results

    def connect_terminal(self, terminal_id: str) -> bool:
        """Connect to specific terminal with enhanced error handling."""

    def enable_algorithmic_trading(self, terminal_id: str) -> bool:
        """Enable algorithmic trading on terminal."""

    def check_algorithmic_trading_status(self) -> Dict[str, bool]:
        """Check algorithmic trading status on all terminals."""

    def get_terminal_info(self, terminal_id: str) -> Dict:
        """Get comprehensive terminal information."""

    def is_connected(self, terminal_id: str) -> bool
    def disconnect_terminal(self, terminal_id: str) -> bool
    def shutdown_all(self) -> None
```

### Advanced Connection Features
- **Multi-Terminal Support**: Simultaneous connection to all 5 terminals
- **Algorithmic Trading Management**: Enable/disable algorithmic trading per terminal
- **Process Management**: Terminal process launching and monitoring
- **Auto-reconnection**: Automatic reconnection with exponential backoff
- **Health monitoring**: Continuous connection and trading status checks
- **Error handling**: Comprehensive error handling with circuit breakers
- **Resource management**: Proper cleanup and memory management
- **Failover Support**: Automatic failover between terminals

## Multi-Terminal Data Collection

### Enhanced Data Collection (`trading/data_collector.py`)
Collects data from all 5 terminals simultaneously with advanced error handling:

```python
class DataCollector:
    def __init__(self, mt5_manager, memory_manager, error_handler):
        self.mt5_manager = mt5_manager
        self.memory_manager = memory_manager
        self.error_handler = error_handler

    def collect_from_all_terminals(
        self,
        symbol: str = "BTCUSD.a",
        timeframes: List[str] = ["M5", "M15", "M30", "H1", "H4"],
        bars: int = 10000
    ) -> Dict[str, pd.DataFrame]:
        """Collect data from all 5 terminals simultaneously."""

    def collect_historical_data(
        self,
        terminal_id: str,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """Collect historical data with enhanced error handling."""

    def collect_realtime_data(
        self,
        terminal_id: str,
        symbol: str,
        timeframe: str
    ) -> pd.DataFrame:
        """Collect real-time market data."""
```

### Multi-Terminal Data Features
- **Parallel Collection**: Simultaneous data collection from all 5 terminals
- **Error Recovery**: Automatic retry with exponential backoff
- **Memory Management**: Intelligent memory usage and cleanup
- **Data Validation**: Comprehensive data quality checks
- **Chunk Processing**: Large dataset handling with chunking
- **Cache Integration**: Intelligent caching for performance

### Supported Timeframes
- **M5**: 5-minute bars (primary for real-time trading)
- **M15**: 15-minute bars (short-term patterns)
- **M30**: 30-minute bars (medium-term analysis)
- **H1**: 1-hour bars (trend analysis)
- **H4**: 4-hour bars (long-term patterns)

### Enhanced Data Storage Structure
```
data/
├── terminal_1/
│   ├── M5/
│   │   ├── BTCUSD.a_M5_20240101_120000.parquet
│   │   └── processed/
│   ├── M15/
│   ├── M30/
│   ├── H1/
│   └── H4/
├── terminal_2/
├── terminal_3/
├── terminal_4/
├── terminal_5/
├── combined/
│   └── aggregated_data.parquet
└── cache/
    └── realtime_cache/
```

## Trading Operations

### Signal Generation (`trading/signal_generator.py`)
Generates trading signals based on model predictions:

```python
class SignalGenerator:
    def generate_signal(
        self,
        predictions: np.ndarray,
        current_price: float,
        confidence_threshold: float = 0.65
    ) -> Dict[str, Any]
```

### Trade Execution (`trading/executor.py`)
Executes trades on MT5 terminals:

```python
class TradeExecutor:
    def execute_trade(
        self,
        terminal_id: str,
        signal: Dict[str, Any]
    ) -> bool
    
    def close_position(
        self,
        terminal_id: str,
        position_id: int
    ) -> bool
```

### Risk Management
- **Position sizing**: Based on account balance and risk parameters
- **Stop loss**: Automatic stop loss placement
- **Take profit**: Automatic take profit placement
- **Maximum positions**: Limit on concurrent positions
- **Daily loss limit**: Maximum daily loss protection

## Trading Strategy (`trading/strategy.py`)

### Strategy Implementation
```python
class TradingStrategy:
    def __init__(self, config):
        self.symbol = config.strategy.symbol
        self.timeframes = config.strategy.timeframes
        self.risk_per_trade = config.strategy.risk_per_trade
        self.max_positions = config.strategy.max_positions
    
    def should_enter_trade(self, signal: Dict) -> bool
    def calculate_position_size(self, signal: Dict) -> float
    def set_stop_loss(self, entry_price: float, signal: Dict) -> float
    def set_take_profit(self, entry_price: float, signal: Dict) -> float
```

### Strategy Parameters
- **Symbol**: "BTCUSD.a"
- **Risk per trade**: 1% of account balance
- **Stop loss**: 200 pips
- **Take profit**: 400 pips
- **Maximum spread**: 50 pips
- **Maximum daily trades**: 5
- **Cooldown period**: 600 seconds

## Error Handling and Reliability

### Connection Monitoring
- Continuous connection health checks
- Automatic reconnection attempts
- Connection timeout handling
- Server response validation

### Trade Validation
- Pre-trade validation checks
- Market condition verification
- Account balance verification
- Position limit checks

### Logging and Monitoring
- Comprehensive trade logging
- Performance metrics tracking
- Error logging and alerting
- System health monitoring
