"""
Progress visualization module for the trading bot.
Provides real-time visualization of data collection, model training, and trading progress.
"""
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, Optional
import logging
from pathlib import Path
import time
import queue
import psutil
from config import config_manager

logger = logging.getLogger(__name__)

class ProgressVisualizer:
    def __init__(self):
        """Initialize the progress visualizer."""
        self.monitoring_config = config_manager.get_monitoring_config()
        self.fig = None
        self.axs = None
        self.initialized = False
        self.update_queue = queue.Queue()
        self.running = False
        self.update_thread = None

        # Initialize metrics
        self.data_collection = {
            'time': [],
            'progress': []
        }
        self.model_training = {
            'time': [],
            'loss': [],
            'val_loss': []
        }
        self.trading_performance = {
            'time': [],
            'trades': [],
            'win_rate': [],
            'pnl': []  # Initialize pnl list
        }
        self.system_metrics = {
            'time': [],
            'memory_usage': [],
            'cpu_usage': [],
            'gpu_usage': []
        }

        # Create output directory
        output_dir = getattr(self.monitoring_config, 'output_dir', 'monitoring')
        self.output_dir = Path(output_dir) / 'progress'
        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info("Progress visualizer initialized")

    def initialize(self) -> bool:
        """
        Initialize the visualization window.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Set up matplotlib backend
            plt.switch_backend('TkAgg')  # Use TkAgg backend for better compatibility
            plt.ion()  # Turn on interactive mode

            # Create the figure and subplots
            self.fig, self.axs = plt.subplots(2, 2, figsize=(12, 10))
            self.fig.canvas.manager.set_window_title('Trading Bot Progress')

            # Flatten axs for easier access
            self.axs = self.axs.flatten()

            # Set up subplot titles
            self.axs[0].set_title('Data Collection Progress')
            self.axs[1].set_title('Model Training Progress')
            self.axs[2].set_title('Trading Performance')
            self.axs[3].set_title('System Metrics')

            # Show the window
            plt.tight_layout()
            plt.show(block=False)
            plt.pause(0.1)

            self.initialized = True
            logger.info("Progress visualizer display initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize progress visualizer display: {str(e)}")
            return False

    def close(self):
        """Close the visualization window."""
        try:
            if self.fig is not None:
                plt.close(self.fig)
            self.initialized = False
            logger.info("Progress visualizer closed")
        except Exception as e:
            logger.error(f"Error closing progress visualizer: {str(e)}")

    def __enter__(self):
        """Context manager entry."""
        if not self.initialize():
            raise RuntimeError("Failed to initialize progress visualizer")
        return self

    def __exit__(self, exc_type, exc_val, _):
        """Context manager exit."""
        self.close()
        if exc_type is not None:
            logger.error(f"Error in ProgressVisualizer: {exc_val}")
            return False
        return True

    def update(self, data=None):
        """Update all visualization plots."""
        if data is not None:
            # This is the new update method that takes data as an argument
            self._update_with_data(data)
            return

        # Original update method without arguments
        if not self.initialized or self.fig is None or self.axs is None:
            logger.warning("Cannot update - visualizer not initialized")
            return

        try:
            # Clear all plots
            for ax in self.axs:
                ax.clear()

            # Update Data Collection Progress
            if self.data_collection['time']:
                self.axs[0].plot(self.data_collection['time'], self.data_collection['progress'])
                self.axs[0].set_title('Data Collection Progress')
                self.axs[0].set_xlabel('Time')
                self.axs[0].set_ylabel('Progress (%)')
                self.axs[0].grid(True)

            # Update Model Training Progress
            if self.model_training['time']:
                self.axs[1].plot(self.model_training['time'], self.model_training['loss'], label='Training Loss')
                self.axs[1].plot(self.model_training['time'], self.model_training['val_loss'], label='Validation Loss')
                self.axs[1].set_title('Model Training Progress')
                self.axs[1].set_xlabel('Time')
                self.axs[1].set_ylabel('Loss')
                self.axs[1].legend()
                self.axs[1].grid(True)

            # Update Trading Performance
            if self.trading_performance['time']:
                ax2 = self.axs[2]
                ax2_twin = ax2.twinx()

                # Plot trades on left y-axis
                ax2.plot(self.trading_performance['time'], self.trading_performance['trades'],
                        'b-', label='Total Trades')
                ax2.set_ylabel('Number of Trades', color='b')
                ax2.tick_params(axis='y', labelcolor='b')

                # Plot win rate on right y-axis
                ax2_twin.plot(self.trading_performance['time'], self.trading_performance['win_rate'],
                            'r-', label='Win Rate')
                ax2_twin.set_ylabel('Win Rate (%)', color='r')
                ax2_twin.tick_params(axis='y', labelcolor='r')

                ax2.set_title('Trading Performance')
                ax2.set_xlabel('Time')

                # Combine legends
                lines1, labels1 = ax2.get_legend_handles_labels()
                lines2, labels2 = ax2_twin.get_legend_handles_labels()
                ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
                ax2.grid(True)

            # Update System Metrics
            if self.system_metrics['time']:
                self.axs[3].plot(self.system_metrics['time'], self.system_metrics['memory_usage'],
                                label='Memory')
                self.axs[3].plot(self.system_metrics['time'], self.system_metrics['cpu_usage'],
                                label='CPU')
                if self.system_metrics['gpu_usage']:  # Only plot GPU if available
                    self.axs[3].plot(self.system_metrics['time'], self.system_metrics['gpu_usage'],
                                    label='GPU')
                self.axs[3].set_title('System Resource Usage')
                self.axs[3].set_xlabel('Time')
                self.axs[3].set_ylabel('Usage (%)')
                self.axs[3].legend()
                self.axs[3].grid(True)

            # Adjust layout and draw
            plt.tight_layout()
            self.fig.canvas.draw()
            self.fig.canvas.flush_events()

            # Save the current plot
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            save_path = self.output_dir / f'progress_{timestamp}.png'
            self.fig.savefig(save_path)
            logger.debug(f"Progress plot saved to {save_path}")

        except Exception as e:
            logger.error(f"Error updating visualization: {str(e)}")

    def _update_with_data(self, data):
        """Update with performance data from bots."""
        try:
            # Store the data
            self.bot_data = data

            # Only log if there's actual data to display
            has_data = False
            for terminal_id, metrics in data.items():
                if metrics:
                    has_data = True
                    break

            if not has_data:
                # Don't spam logs when there's no data
                return

            # Log summary of the data
            logger.info("=== Trading Bot Performance ===")

            for terminal_id, metrics in data.items():
                logger.info(f"Terminal {terminal_id}:")

                if not metrics:
                    logger.info("  No metrics available")
                    continue

                # Display key metrics
                for model_name, model_metrics in metrics.items():
                    if not isinstance(model_metrics, dict):
                        continue

                    logger.info(f"  Model: {model_name}")

                    # Display win rate if available
                    if 'win_rate' in model_metrics:
                        win_rate = model_metrics['win_rate']
                        logger.info(f"    Win Rate: {win_rate:.2f}%")

                    # Display profit if available
                    if 'total_profit' in model_metrics:
                        total_profit = model_metrics['total_profit']
                        logger.info(f"    Total Profit: {total_profit:.2f}")

                    # Display average return if available
                    if 'avg_return' in model_metrics:
                        avg_return = model_metrics['avg_return']
                        logger.info(f"    Avg Return: {avg_return:.2f}")

                    # Display trade count if available
                    if 'trade_count' in model_metrics:
                        trade_count = model_metrics['trade_count']
                        logger.info(f"    Trade Count: {trade_count}")

            logger.info("===================================")

        except Exception as e:
            logger.error(f"Error updating with data: {str(e)}")

    def _update_data_collection_plot(self):
        """Update the data collection progress plot."""
        self.axs[0].clear()
        self.axs[0].set_title('Data Collection Progress')
        if self.data_collection:
            self.axs[0].bar(self.data_collection.keys(), self.data_collection.values())
            self.axs[0].set_ylabel('Number of Records')
            self.axs[0].tick_params(axis='x', rotation=45)

    def _update_model_training_plot(self):
        """Update the model training progress plot."""
        self.axs[1].clear()
        self.axs[1].set_title('Model Training Progress')
        for model, losses in self.model_training.items():
            if losses['train_loss']:
                epochs = range(1, len(losses['train_loss']) + 1)
                self.axs[1].plot(epochs, losses['train_loss'], 'b-', label=f'{model} Train')
                self.axs[1].plot(epochs, losses['val_loss'], 'r-', label=f'{model} Val')
        self.axs[1].set_xlabel('Epoch')
        self.axs[1].set_ylabel('Loss')
        if self.model_training:
            self.axs[1].legend()

    def _update_trading_performance_plot(self):
        """Update the trading performance plot."""
        self.axs[2].clear()
        self.axs[2].set_title('Trading Performance')
        if self.trading_performance['pnl']:
            metrics = ['PnL', 'Trades', 'Win Rate']
            values = [
                self.trading_performance['pnl'][-1],
                self.trading_performance['trades'][-1],
                self.trading_performance['win_rate'][-1]
            ]
            self.axs[2].bar(metrics, values)
            self.axs[2].set_ylabel('Value')

    def _update_system_status_plot(self):
        """Update the system resources plot."""
        self.axs[3].clear()
        self.axs[3].set_title('System Resources')
        if self.system_metrics['memory_usage']:
            resources = ['Memory', 'CPU']
            values = [
                self.system_metrics['memory_usage'][-1],
                self.system_metrics['cpu_usage'][-1]
            ]
            if self.system_metrics['gpu_usage']:
                resources.append('GPU')
                values.append(self.system_metrics['gpu_usage'][-1])
            self.axs[3].bar(resources, values)
            self.axs[3].set_ylabel('Usage (%)')

    def update_data_collection(self, terminal_id: int, timeframe: str, records: int):
        """Update data collection progress."""
        if not hasattr(self, 'data_collection'):
            self.data_collection = {}

        if terminal_id not in self.data_collection:
            self.data_collection[terminal_id] = {}

        self.data_collection[terminal_id][timeframe] = records
        self.update()  # Update the visualization

    def update_model_training(self, model_name: str, _epoch: int, _total_epochs: int,
                            train_loss: float, val_loss: float):
        """
        Update model training progress.

        Args:
            model_name: Name of the model being trained
            _epoch: Current epoch (unused)
            _total_epochs: Total number of epochs (unused)
            train_loss: Current training loss
            val_loss: Current validation loss
        """
        if model_name not in self.model_training:
            self.model_training[model_name] = {'train_loss': [], 'val_loss': []}

        self.model_training[model_name]['train_loss'].append(train_loss)
        self.model_training[model_name]['val_loss'].append(val_loss)
        self.update_queue.put(('model_training',))

    def update_system_status(self, memory_usage: float, cpu_usage: float, gpu_usage: Optional[float] = None):
        """
        Update system status metrics.

        Args:
            memory_usage: Current memory usage percentage
            cpu_usage: Current CPU usage percentage
            gpu_usage: Current GPU usage percentage (optional)
        """
        try:
            current_time = time.time()

            # Update metrics
            self.system_metrics['memory_usage'].append(memory_usage)
            self.system_metrics['cpu_usage'].append(cpu_usage)
            if gpu_usage is not None:
                self.system_metrics['gpu_usage'].append(gpu_usage)
            self.system_metrics['time'].append(current_time)

            # Keep only the last 1000 data points
            max_points = 1000
            if len(self.system_metrics['memory_usage']) > max_points:
                self.system_metrics['memory_usage'] = self.system_metrics['memory_usage'][-max_points:]
                self.system_metrics['cpu_usage'] = self.system_metrics['cpu_usage'][-max_points:]
                if gpu_usage is not None:
                    self.system_metrics['gpu_usage'] = self.system_metrics['gpu_usage'][-max_points:]
                self.system_metrics['time'] = self.system_metrics['time'][-max_points:]

            # Update visualization
            self._update_system_status_plot()

        except Exception as e:
            logger.error(f"Error updating system status: {str(e)}")
            self.log_error(f"Error updating system status: {str(e)}")

    def update_system_metrics(self, memory_usage: float, cpu_usage: float, gpu_usage: Optional[float] = None):
        """
        Alias for update_system_status to maintain backward compatibility.
        """
        self.update_system_status(memory_usage, cpu_usage, gpu_usage)

    def update_trading_performance(self, performance_metrics: Optional[Dict] = None):
        """
        Update trading performance metrics.

        Args:
            performance_metrics: Dictionary containing performance metrics
        """
        try:
            if performance_metrics is None:
                performance_metrics = {}

            # Update metrics
            current_time = datetime.now()
            self.trading_performance['time'].append(current_time)

            # Extract metrics with default values
            total_profit = performance_metrics.get('total_profit', 0.0)
            total_trades = performance_metrics.get('total_trades', 0)
            win_rate = performance_metrics.get('win_rate', 0.0)

            # Update lists
            self.trading_performance['pnl'].append(total_profit)
            self.trading_performance['trades'].append(total_trades)
            self.trading_performance['win_rate'].append(win_rate)

            # Keep only the last 1000 data points
            max_points = 1000
            for key in self.trading_performance:
                if len(self.trading_performance[key]) > max_points:
                    self.trading_performance[key] = self.trading_performance[key][-max_points:]

            # Update visualization
            self._update_trading_performance_plot()

        except Exception as e:
            logger.error(f"Error updating trading performance: {str(e)}")
            self.log_error(f"Error updating trading performance: {str(e)}")

    def save_progress(self):
        """Save the current progress plot."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = self.output_dir / f"progress_{timestamp}.png"
        self.fig.savefig(save_path)
        logger.info(f"Saved progress plot to {save_path}")

    def close(self):
        """Close the progress visualization."""
        try:
            # Turn off interactive mode
            plt.ioff()

            # Close all figures
            plt.close('all')

            # Reset matplotlib state
            plt.rcdefaults()

            # Force garbage collection
            import gc
            gc.collect()

            # Additional cleanup for Tk windows
            try:
                import tkinter as tk
                root = tk.Tk()
                root.destroy()
            except:
                pass

        except Exception as e:
            logging.error(f"Error closing visualization: {str(e)}")

    def __del__(self):
        """Cleanup when the object is destroyed."""
        self.close()

    def get_system_metrics(self):
        """Get current system metrics (memory usage, CPU usage, GPU usage if available)."""
        try:
            # Memory usage
            memory_usage = psutil.virtual_memory().percent

            # CPU usage (average across all cores)
            cpu_usage = psutil.cpu_percent(interval=0.1)
            if cpu_usage == 0:  # Avoid division by zero
                cpu_usage = 0.1

            # GPU usage if available
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu_usage = gpus[0].load * 100
                else:
                    gpu_usage = 0
            except (ImportError, Exception):
                gpu_usage = 0

            return memory_usage, cpu_usage, gpu_usage
        except Exception as e:
            logger.warning(f"Error getting system metrics: {str(e)}")
            return 0, 0, 0  # Return safe default values

    def log_error(self, error_message):
        """Log an error message to be displayed in the visualization."""
        if not hasattr(self, 'error_log'):
            self.error_log = []
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.error_log.append(f"[{timestamp}] {error_message}")
        # Keep only the last 10 errors
        if len(self.error_log) > 10:
            self.error_log = self.error_log[-10:]