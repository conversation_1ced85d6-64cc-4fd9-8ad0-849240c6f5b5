# Model Training Visualization

This directory contains visualizations of the model training process.

## Available Visualizations

1. **Training Progress** - Loss curves for each model
   - `training_progress.html` - Interactive visualization
   - `training_progress.png` - Static image

2. **Model Comparison** - Comparison of final validation loss across models
   - `model_comparison.html` - Interactive visualization
   - `model_comparison.png` - Static image

3. **Training Time Comparison** - Comparison of training time across models
   - `training_time_comparison.html` - Interactive visualization
   - `training_time_comparison.png` - Static image

## How to Use

### View Existing Visualizations

Open the HTML files in a web browser to interact with the visualizations.

### Generate New Visualizations

Run the visualization script:

```bash
python visualize_training.py --log_file model_training.log --output_dir visualizations
```

### Monitor Training Progress in Real-Time

Run the visualization script with the `--monitor` flag:

```bash
python visualize_training.py --log_file model_training.log --output_dir visualizations --monitor --interval 30
```

Or use the provided batch file:

```bash
monitor_training.bat
```

## Options

- `--log_file` - Path to the training log file (default: `model_training.log`)
- `--output_dir` - Directory to save visualizations (default: `visualizations`)
- `--models` - List of models to visualize (e.g., `lstm gru xgboost`)
- `--monitor` - Monitor training progress in real-time
- `--interval` - Interval in seconds to update visualizations (default: 10)

## Examples

Visualize specific models:

```bash
python visualize_training.py --log_file model_training.log --models lstm gru
```

Monitor training progress with a longer update interval:

```bash
python visualize_training.py --log_file model_training.log --monitor --interval 60
```
