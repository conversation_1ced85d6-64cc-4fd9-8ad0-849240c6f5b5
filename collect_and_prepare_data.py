#!/usr/bin/env python
"""
<PERSON>ript to collect and prepare 6-year historical BTCUSD.a data from all 5 MT5 terminals
across multiple timeframes (M5, M15, M30, H1, H4) in parquet format.

This script:
1. Starts all MT5 terminals
2. Collects historical OHLCV data for BTCUSD.a
3. Processes and validates the data
4. Prepares the data for model training
5. Verifies the collected and prepared data
"""

import logging
import argparse
import subprocess
import sys
import time
from pathlib import Path
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('data_pipeline')

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect and prepare BTCUSD.a data for model training')
    parser.add_argument('--terminal_ids', nargs='+', type=int, default=[1, 2, 3, 4, 5],
                        help='List of terminal IDs to collect data from')
    parser.add_argument('--raw_dir', type=str, default='data/raw',
                        help='Directory for raw collected data')
    parser.add_argument('--processed_dir', type=str, default='data/processed',
                        help='Directory for processed data')
    parser.add_argument('--years', type=int, default=6,
                        help='Number of years of historical data to collect')
    parser.add_argument('--force', action='store_true',
                        help='Force data collection and preparation even if files already exist')
    parser.add_argument('--skip_collection', action='store_true',
                        help='Skip data collection and only run preparation')
    parser.add_argument('--skip_preparation', action='store_true',
                        help='Skip data preparation and only run collection')
    return parser.parse_args()

def start_mt5_terminals(terminal_ids):
    """
    Start MT5 terminals using the manage_mt5_terminals.py script.

    MINIMAL MT5 MODE: This function is disabled to preserve algorithmic trading.
    The script will attempt to call the terminal management functions but they
    will return disabled status.

    Args:
        terminal_ids: List of terminal IDs to start

    Returns:
        True if all terminals started successfully, False otherwise
    """
    try:
        logger.warning("=" * 80)
        logger.warning("MINIMAL MT5 MODE: Terminal startup is DISABLED")
        logger.warning("This preserves algorithmic trading settings in existing terminals")
        logger.warning("Please ensure MT5 terminals are started manually with algo trading enabled")
        logger.warning("=" * 80)

        logger.info(f"Attempting to start MT5 terminals: {terminal_ids}")
        logger.info("Note: This will fail in minimal mode - this is expected behavior")

        # Start each terminal individually
        for terminal_id in terminal_ids:
            # Build command for each terminal
            cmd = f"python scripts/manage_mt5_terminals.py --start --terminal {terminal_id} --wait 5"

            # Run command
            logger.info(f"Running command: {cmd}")
            result = subprocess.run(cmd, shell=True, check=False, capture_output=True, text=True)

            # In minimal mode, the script will show warnings but continue
            logger.info(f"Terminal {terminal_id} management result: {result.stdout}")

        # In minimal mode, we assume terminals are manually started
        logger.warning("MINIMAL MT5 MODE: Assuming terminals are manually started")
        logger.warning("Please verify that your MT5 terminals are running with algo trading enabled")
        return True

    except Exception as e:
        logger.warning(f"Terminal management error (expected in minimal mode): {str(e)}")
        logger.warning("Please ensure MT5 terminals are started manually")
        return True  # Continue anyway in minimal mode

def collect_data(args):
    """
    Collect historical data using collect_historical_data.py script.

    Args:
        args: Command line arguments

    Returns:
        True if data collection was successful, False otherwise
    """
    try:
        logger.info("Starting data collection")

        # Build command
        cmd = [
            "python", "collect_historical_data.py",
            "--terminal_ids", *[str(tid) for tid in args.terminal_ids],
            "--output_dir", args.raw_dir,
            "--years", str(args.years)
        ]

        if args.force:
            cmd.append("--force")

        # Run command
        logger.info(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)

        # Check output for success
        if "Data collection and verification complete" in result.stdout:
            logger.info("Data collection completed successfully")
            return True
        else:
            logger.warning("Data collection completed with warnings or errors")
            logger.warning(result.stdout)
            return True  # Continue with preparation even if there are warnings

    except subprocess.CalledProcessError as e:
        logger.error(f"Error during data collection: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Error during data collection: {str(e)}")
        return False

def prepare_data(args):
    """
    Prepare collected data using prepare_training_data.py script.

    Args:
        args: Command line arguments

    Returns:
        True if data preparation was successful, False otherwise
    """
    try:
        logger.info("Starting data preparation")

        # Build command
        cmd = [
            "python", "prepare_training_data.py",
            "--input_dir", args.raw_dir,
            "--output_dir", args.processed_dir
        ]

        if args.force:
            cmd.append("--force")

        # Run command
        logger.info(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)

        # Check output for success
        if "Data preparation complete" in result.stdout:
            logger.info("Data preparation completed successfully")
            return True
        else:
            logger.warning("Data preparation completed with warnings or errors")
            logger.warning(result.stdout)
            return True  # Continue even if there are warnings

    except subprocess.CalledProcessError as e:
        logger.error(f"Error during data preparation: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Error during data preparation: {str(e)}")
        return False

def verify_data_pipeline(args):
    """
    Verify that the data pipeline produced the expected output files.

    Args:
        args: Command line arguments

    Returns:
        True if verification was successful, False otherwise
    """
    try:
        logger.info("Verifying data pipeline output")

        # Check raw data directory
        raw_dir = Path(args.raw_dir)
        if not raw_dir.exists():
            logger.error(f"Raw data directory does not exist: {raw_dir}")
            return False

        # Check processed data directory
        processed_dir = Path(args.processed_dir)
        if not processed_dir.exists():
            logger.error(f"Processed data directory does not exist: {processed_dir}")
            return False

        # Check for raw data files
        raw_files = list(raw_dir.glob("BTCUSD.a_*.parquet"))
        if not raw_files:
            logger.error("No raw data files found")
            return False

        logger.info(f"Found {len(raw_files)} raw data files")

        # Check for processed data files
        train_dir = processed_dir / 'train'
        val_dir = processed_dir / 'validation'
        test_dir = processed_dir / 'test'

        train_files = list(train_dir.glob("BTCUSD.a_*.parquet"))
        val_files = list(val_dir.glob("BTCUSD.a_*.parquet"))
        test_files = list(test_dir.glob("BTCUSD.a_*.parquet"))

        if not train_files or not val_files or not test_files:
            logger.error("Missing processed data files")
            logger.error(f"Train files: {len(train_files)}")
            logger.error(f"Validation files: {len(val_files)}")
            logger.error(f"Test files: {len(test_files)}")
            return False

        logger.info(f"Found {len(train_files)} training files")
        logger.info(f"Found {len(val_files)} validation files")
        logger.info(f"Found {len(test_files)} test files")

        # Check for metadata files
        metadata_dir = processed_dir / 'metadata'
        metadata_files = list(metadata_dir.glob("BTCUSD.a_*_metadata.json"))

        if not metadata_files:
            logger.warning("No metadata files found")
        else:
            logger.info(f"Found {len(metadata_files)} metadata files")

        return True

    except Exception as e:
        logger.error(f"Error verifying data pipeline: {str(e)}")
        return False

def main():
    """Main function to collect and prepare data."""
    args = parse_arguments()

    logger.info("Starting data collection and preparation pipeline")
    logger.info(f"Terminal IDs: {args.terminal_ids}")
    logger.info(f"Raw data directory: {args.raw_dir}")
    logger.info(f"Processed data directory: {args.processed_dir}")
    logger.info(f"Years of historical data: {args.years}")

    # Create directories
    os.makedirs(args.raw_dir, exist_ok=True)
    os.makedirs(args.processed_dir, exist_ok=True)

    # Start MT5 terminals
    if not args.skip_collection:
        try:
            if not start_mt5_terminals(args.terminal_ids):
                logger.warning("Failed to start MT5 terminals. Will try to continue anyway.")

            # Wait for terminals to initialize
            logger.info("Waiting for MT5 terminals to initialize...")
            time.sleep(10)
        except Exception as e:
            logger.warning(f"Error starting MT5 terminals: {str(e)}. Will try to continue anyway.")

    # Collect data
    if not args.skip_collection:
        logger.info("Starting data collection phase")
        if not collect_data(args):
            logger.error("Data collection failed. Exiting.")
            return 1
    else:
        logger.info("Skipping data collection phase")

    # Prepare data
    if not args.skip_preparation:
        logger.info("Starting data preparation phase")
        if not prepare_data(args):
            logger.error("Data preparation failed. Exiting.")
            return 1
    else:
        logger.info("Skipping data preparation phase")

    # Verify data pipeline
    logger.info("Verifying data pipeline output")
    if not verify_data_pipeline(args):
        logger.warning("Data pipeline verification found issues")
    else:
        logger.info("Data pipeline verification successful")

    logger.info("Data collection and preparation pipeline completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
