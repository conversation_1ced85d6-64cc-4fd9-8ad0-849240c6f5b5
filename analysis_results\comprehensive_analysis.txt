===== MODEL RECOMMENDATIONS =====

Timeframe: H1
  Best model by RMSE: lstm (RMSE: 0.005637)
  Best model by R-squared: lstm (R-squared: -0.000027)
  Best model by MAE: gru (MAE: 0.003670)
  Best overall model: lstm (RMSE: 0.005637, Issue Score: 1)

Timeframe: H4
  Best model by RMSE: gru (RMSE: 0.011164)
  Best model by R-squared: gru (R-squared: -0.000066)
  Best model by MAE: lstm (MAE: 0.007318)
  Best overall model: gru (RMSE: 0.011164, Issue Score: 1)

Timeframe: M15
  Best model by RMSE: lstm (RMSE: 0.002811)
  Best model by R-squared: lstm (R-squared: -0.000009)
  Best model by MAE: lstm (MAE: 0.001846)
  Best overall model: lstm (RMSE: 0.002811, Issue Score: 1)

Timeframe: M30
  Best model by RMSE: gru (RMSE: 0.003985)
  Best model by R-squared: gru (R-squared: -0.000021)
  Best model by MAE: gru (MAE: 0.002595)
  Best overall model: gru (RMSE: 0.003985, Issue Score: 1)

Timeframe: M5
  Best model by RMSE: gru (RMSE: 0.001653)
  Best model by R-squared: gru (R-squared: -0.000002)
  Best model by MAE: gru (MAE: 0.001072)
  Best overall model: gru (RMSE: 0.001653, Issue Score: 1)


===== MODEL PERFORMANCE ANALYSIS =====

WARNING: 18 model-timeframe combinations have negative R-squared values.
This indicates that these models perform worse than a simple baseline model.
Models with negative R-squared values:
  - gru on H1 (R-squared: -0.000285)
  - lightgbm on H1 (R-squared: -0.000212)
  - lstm on H1 (R-squared: -0.000027)
  - xgboost on H1 (R-squared: -0.210404)
  - gru on H4 (R-squared: -0.000066)
  - lstm on H4 (R-squared: -0.000329)
  - gru on M15 (R-squared: -0.000099)
  - lightgbm on M15 (R-squared: -0.002049)
  - lstm on M15 (R-squared: -0.000009)
  - xgboost on M15 (R-squared: -0.150864)
  - gru on M30 (R-squared: -0.000021)
  - lightgbm on M30 (R-squared: -0.000217)
  - lstm on M30 (R-squared: -0.000522)
  - xgboost on M30 (R-squared: -0.158035)
  - gru on M5 (R-squared: -0.000002)
  - lightgbm on M5 (R-squared: -0.000197)
  - lstm on M5 (R-squared: -0.000448)
  - xgboost on M5 (R-squared: -0.108938)

