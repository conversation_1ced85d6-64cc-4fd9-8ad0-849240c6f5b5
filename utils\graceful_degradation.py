"""
Graceful Degradation System.

This module provides mechanisms for graceful degradation when system components
fail or become overloaded. It allows the system to continue operating with
reduced functionality rather than failing completely.

Key features:
1. Service level management (full, reduced, minimal, emergency)
2. Feature toggles for conditional functionality
3. Fallback mechanisms for critical operations
4. Automatic degradation based on system health
5. Priority-based resource allocation
"""

import time
import logging
import threading
import functools
from enum import Enum
from typing import Dict, Any, Optional, Callable, List, Tuple, Union, Set, TypeVar, Generic
from dataclasses import dataclass, field
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic return type
T = TypeVar('T')

class ServiceLevel(Enum):
    """Service levels for graceful degradation."""
    FULL = 4           # All features available
    REDUCED = 3        # Non-essential features disabled
    MINIMAL = 2        # Only core features available
    EMERGENCY = 1      # Absolute minimum functionality

    def __lt__(self, other):
        if self.__class__ is other.__class__:
            return self.value < other.value
        return NotImplemented

    def __gt__(self, other):
        if self.__class__ is other.__class__:
            return self.value > other.value
        return NotImplemented

class DegradationReason(Enum):
    """Reasons for service degradation."""
    NORMAL = "normal"               # No degradation
    HIGH_LOAD = "high_load"         # System under high load
    MEMORY_PRESSURE = "memory"      # Memory pressure
    EXTERNAL_DEPENDENCY = "external" # External dependency failure
    INTERNAL_ERROR = "internal"     # Internal system error
    MANUAL = "manual"               # Manually triggered degradation

@dataclass
class FeatureToggle:
    """Configuration for a feature toggle."""
    name: str
    description: str
    enabled: bool = True
    minimum_service_level: ServiceLevel = ServiceLevel.REDUCED
    priority: int = 0  # Higher priority = more important feature
    dependencies: List[str] = field(default_factory=list)

    # Function to check if feature should be enabled
    condition_func: Optional[Callable[[], bool]] = None

    # Fallback function if feature is disabled
    fallback_func: Optional[Callable] = None

class GracefulDegradation:
    """
    Graceful degradation system that allows the application to continue
    operating with reduced functionality when components fail.
    """

    _instance = None
    _lock = threading.RLock()

    @classmethod
    def get_instance(cls):
        """Get the singleton instance."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the graceful degradation system."""
        self._service_level = ServiceLevel.FULL
        self._degradation_reason = DegradationReason.NORMAL
        self._degradation_start_time = None
        self._features: Dict[str, FeatureToggle] = {}
        self._disabled_features: Set[str] = set()
        self._health_checks: Dict[str, Callable[[], bool]] = {}
        self._memory_manager = None
        self._circuit_breaker_registry = None

        # Degradation history
        self._history: List[Dict[str, Any]] = []

        # Callbacks for service level changes
        self._service_level_callbacks: Dict[ServiceLevel, List[Callable]] = {
            level: [] for level in ServiceLevel
        }

        logger.info("Graceful degradation system initialized")

    def set_memory_manager(self, memory_manager) -> None:
        """Set the memory manager for memory-aware degradation."""
        self._memory_manager = memory_manager
        logger.info("Memory manager integrated with graceful degradation system")

    def set_circuit_breaker_registry(self, circuit_breaker_registry) -> None:
        """Set the circuit breaker registry for dependency-aware degradation."""
        self._circuit_breaker_registry = circuit_breaker_registry
        logger.info("Circuit breaker registry integrated with graceful degradation system")

    def register_feature(self, feature: FeatureToggle) -> None:
        """
        Register a feature toggle.

        Args:
            feature: Feature toggle configuration
        """
        with self._lock:
            self._features[feature.name] = feature
            logger.info(f"Feature '{feature.name}' registered with priority {feature.priority}")

    def register_health_check(self, name: str, check_func: Callable[[], bool]) -> None:
        """
        Register a health check function.

        Args:
            name: Name of the health check
            check_func: Function that returns True if healthy, False otherwise
        """
        with self._lock:
            self._health_checks[name] = check_func
            logger.info(f"Health check '{name}' registered")

    def register_service_level_callback(self, level: ServiceLevel, callback: Callable) -> None:
        """
        Register a callback for service level changes.

        Args:
            level: Service level to trigger the callback
            callback: Function to call when service level changes
        """
        with self._lock:
            self._service_level_callbacks[level].append(callback)
            logger.info(f"Callback registered for service level {level.value}")

    def set_service_level(self, level: ServiceLevel, reason: DegradationReason = DegradationReason.MANUAL) -> None:
        """
        Set the service level.

        Args:
            level: New service level
            reason: Reason for the change
        """
        with self._lock:
            if level == self._service_level:
                return

            old_level = self._service_level
            self._service_level = level
            self._degradation_reason = reason

            if level != ServiceLevel.FULL:
                self._degradation_start_time = datetime.now()
            else:
                self._degradation_start_time = None

            # Update feature availability based on service level
            self._update_features()

            # Record in history
            self._history.append({
                'timestamp': datetime.now(),
                'old_level': old_level.value,
                'new_level': level.value,
                'reason': reason.value,
                'disabled_features': list(self._disabled_features)
            })

            # Call service level callbacks
            for callback in self._service_level_callbacks[level]:
                try:
                    callback(old_level, level, reason)
                except Exception as e:
                    logger.error(f"Error in service level callback: {str(e)}")

            logger.warning(
                f"Service level changed from {old_level.value} to {level.value} "
                f"due to {reason.value}"
            )

    def get_service_level(self) -> ServiceLevel:
        """Get the current service level."""
        return self._service_level

    def get_degradation_reason(self) -> DegradationReason:
        """Get the reason for the current degradation."""
        return self._degradation_reason

    def get_degradation_duration(self) -> Optional[float]:
        """
        Get the duration of the current degradation in seconds.

        Returns:
            float: Duration in seconds or None if not degraded
        """
        if self._degradation_start_time is None:
            return None

        return (datetime.now() - self._degradation_start_time).total_seconds()

    def is_feature_enabled(self, feature_name: str) -> bool:
        """
        Check if a feature is enabled.

        Args:
            feature_name: Name of the feature

        Returns:
            bool: True if the feature is enabled, False otherwise
        """
        with self._lock:
            # Check if feature exists
            if feature_name not in self._features:
                logger.warning(f"Feature '{feature_name}' not registered")
                return False

            # Check if feature is explicitly disabled
            if feature_name in self._disabled_features:
                return False

            feature = self._features[feature_name]

            # Check service level
            # Higher values are higher service levels (FULL=4, EMERGENCY=1)
            if feature.minimum_service_level > self._service_level:
                return False

            # Check dependencies
            for dependency in feature.dependencies:
                if dependency in self._features and not self.is_feature_enabled(dependency):
                    return False

            # Check condition function
            if feature.condition_func is not None:
                try:
                    return feature.condition_func()
                except Exception as e:
                    logger.error(f"Error in feature condition function: {str(e)}")
                    return False

            return feature.enabled

    def with_feature(self, feature_name: str):
        """
        Decorator for feature-gated functionality.

        Example:
            @graceful_degradation.with_feature("advanced_analytics")
            def analyze_data():
                # This will only run if the feature is enabled
                pass
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                if self.is_feature_enabled(feature_name):
                    return func(*args, **kwargs)

                # Use fallback if available
                feature = self._features.get(feature_name)
                if feature and feature.fallback_func:
                    return feature.fallback_func(*args, **kwargs)

                # No fallback available
                logger.warning(f"Feature '{feature_name}' is disabled, operation skipped")
                return None

            return wrapper

        return decorator

    def execute_if_enabled(self, feature_name: str, func: Callable[..., T], *args, **kwargs) -> Optional[T]:
        """
        Execute a function only if the feature is enabled.

        Args:
            feature_name: Name of the feature
            func: Function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Optional[T]: Result of the function or None if feature is disabled
        """
        if self.is_feature_enabled(feature_name):
            return func(*args, **kwargs)

        # Use fallback if available
        feature = self._features.get(feature_name)
        if feature and feature.fallback_func:
            return feature.fallback_func(*args, **kwargs)

        # No fallback available
        logger.warning(f"Feature '{feature_name}' is disabled, operation skipped")
        return None

    def enable_feature(self, feature_name: str) -> bool:
        """
        Enable a feature.

        Args:
            feature_name: Name of the feature

        Returns:
            bool: True if successful, False otherwise
        """
        with self._lock:
            if feature_name not in self._features:
                logger.warning(f"Feature '{feature_name}' not registered")
                return False

            self._features[feature_name].enabled = True
            if feature_name in self._disabled_features:
                self._disabled_features.remove(feature_name)

            logger.info(f"Feature '{feature_name}' enabled")
            return True

    def disable_feature(self, feature_name: str) -> bool:
        """
        Disable a feature.

        Args:
            feature_name: Name of the feature

        Returns:
            bool: True if successful, False otherwise
        """
        with self._lock:
            if feature_name not in self._features:
                logger.warning(f"Feature '{feature_name}' not registered")
                return False

            self._features[feature_name].enabled = False
            self._disabled_features.add(feature_name)

            logger.info(f"Feature '{feature_name}' disabled")
            return True

    def check_system_health(self) -> bool:
        """
        Check the health of the system and degrade if necessary.

        Returns:
            bool: True if system is healthy, False otherwise
        """
        with self._lock:
            # Check memory pressure if memory manager is available
            if self._memory_manager:
                memory_status = self._memory_manager.check_memory()
                if memory_status == "CRITICAL":
                    self.set_service_level(ServiceLevel.EMERGENCY, DegradationReason.MEMORY_PRESSURE)
                    return False
                elif memory_status == "HIGH":
                    self.set_service_level(ServiceLevel.MINIMAL, DegradationReason.MEMORY_PRESSURE)
                    return False
                elif memory_status == "WARNING":
                    self.set_service_level(ServiceLevel.REDUCED, DegradationReason.MEMORY_PRESSURE)
                    return False

            # Check circuit breakers if registry is available
            if self._circuit_breaker_registry:
                open_circuits = []
                for name, circuit in self._circuit_breaker_registry.get_all().items():
                    if circuit.state == "OPEN":
                        open_circuits.append(name)

                if len(open_circuits) > 3:
                    # Multiple critical dependencies are down
                    self.set_service_level(ServiceLevel.EMERGENCY, DegradationReason.EXTERNAL_DEPENDENCY)
                    return False
                elif len(open_circuits) > 0:
                    # Some dependencies are down
                    self.set_service_level(ServiceLevel.MINIMAL, DegradationReason.EXTERNAL_DEPENDENCY)
                    return False

            # Run registered health checks
            failed_checks = []
            for name, check_func in self._health_checks.items():
                try:
                    if not check_func():
                        failed_checks.append(name)
                except Exception as e:
                    logger.error(f"Error in health check '{name}': {str(e)}")
                    failed_checks.append(name)

            if len(failed_checks) >= 2:
                # Multiple health checks failed
                self.set_service_level(ServiceLevel.EMERGENCY, DegradationReason.INTERNAL_ERROR)
                return False
            elif len(failed_checks) > 0:
                # Some health checks failed
                self.set_service_level(ServiceLevel.MINIMAL, DegradationReason.INTERNAL_ERROR)
                return False

            # System is healthy, restore full service
            if self._service_level != ServiceLevel.FULL:
                self.set_service_level(ServiceLevel.FULL, DegradationReason.NORMAL)

            return True

    def _update_features(self) -> None:
        """Update feature availability based on service level."""
        with self._lock:
            # Reset disabled features
            self._disabled_features = set()

            # Disable features based on service level
            # Higher values are higher service levels (FULL=4, EMERGENCY=1)
            for name, feature in self._features.items():
                if feature.minimum_service_level > self._service_level:
                    self._disabled_features.add(name)

            logger.info(f"Updated features for service level {self._service_level.value}, "
                       f"{len(self._disabled_features)} features disabled")

    def get_disabled_features(self) -> List[str]:
        """Get the list of disabled features."""
        with self._lock:
            return list(self._disabled_features)

    def get_feature_status(self) -> Dict[str, bool]:
        """
        Get the status of all features.

        Returns:
            Dict[str, bool]: Dictionary of feature names and their enabled status
        """
        with self._lock:
            return {name: self.is_feature_enabled(name) for name in self._features}

    def get_degradation_history(self) -> List[Dict[str, Any]]:
        """Get the history of service level changes."""
        with self._lock:
            return self._history.copy()

# Create global instance
graceful_degradation = GracefulDegradation.get_instance()

# Convenience functions
def is_feature_enabled(feature_name: str) -> bool:
    """Check if a feature is enabled."""
    return graceful_degradation.is_feature_enabled(feature_name)

def with_feature(feature_name: str):
    """Decorator for feature-gated functionality."""
    return graceful_degradation.with_feature(feature_name)

def execute_if_enabled(feature_name: str, func: Callable[..., T], *args, **kwargs) -> Optional[T]:
    """Execute a function only if the feature is enabled."""
    return graceful_degradation.execute_if_enabled(feature_name, func, *args, **kwargs)

def get_service_level() -> ServiceLevel:
    """Get the current service level."""
    return graceful_degradation.get_service_level()

def set_service_level(level: ServiceLevel, reason: DegradationReason = DegradationReason.MANUAL) -> None:
    """Set the service level."""
    graceful_degradation.set_service_level(level, reason)

def check_system_health() -> bool:
    """Check the health of the system and degrade if necessary."""
    return graceful_degradation.check_system_health()

def register_feature(feature: FeatureToggle) -> None:
    """Register a feature toggle."""
    graceful_degradation.register_feature(feature)

def register_health_check(name: str, check_func: Callable[[], bool]) -> None:
    """Register a health check function."""
    graceful_degradation.register_health_check(name, check_func)

# Try to import dependencies for integration
try:
    from utils.enhanced_memory_manager import enhanced_memory_manager
    graceful_degradation.set_memory_manager(enhanced_memory_manager)
except ImportError:
    logger.debug("Enhanced memory manager not available for graceful degradation integration")

try:
    from utils.enhanced_circuit_breaker import EnhancedCircuitBreaker
    graceful_degradation.set_circuit_breaker_registry(EnhancedCircuitBreaker)
except ImportError:
    logger.debug("Enhanced circuit breaker not available for graceful degradation integration")
