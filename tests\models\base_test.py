"""
Base test class with common functionality for all model tests.
"""
import sys
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from pathlib import Path
from typing import Dict, Tuple
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import psutil

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# Import configuration with fallback
try:
    from config import ConfigurationManager
    config_manager = ConfigurationManager()
    model_config = config_manager.get_config().models
except ImportError:
    # Fallback configuration
    class ModelConfig:
        FEATURE_COLUMNS = ['open', 'high', 'low', 'close', 'tick_volume']
        SEQUENCE_LENGTH = 288

    model_config = ModelConfig()

class BaseModelTest:
    """Base class for model testing with common functionality."""

    def __init__(self, model_name: str):
        """Initialize base test class.

        Args:
            model_name: Name of the model being tested
        """
        self.model_name = model_name
        self.model = None
        self.all_metrics = []

        # Set up logging
        self.logger = logging.getLogger(f"{model_name}_test")
        self.logger.setLevel(logging.INFO)

        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = Path("test_results") / model_name / timestamp
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Add file handler
        log_file = self.output_dir / f"{model_name}_test.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(file_handler)

        # Add console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(console_handler)

        self.logger.info(f"Initialized {model_name} test with output directory: {self.output_dir}")

    def load_and_preprocess_data(self, data_path: Path) -> Tuple[np.ndarray, np.ndarray]:
        """Load and preprocess data for testing.

        Args:
            data_path: Path to the data file

        Returns:
            Tuple of (X, y) where X is the feature matrix and y is the target vector
        """
        try:
            # Load data
            df = pd.read_csv(data_path)
            self.logger.info(f"Loaded {len(df)} records from {data_path}")

            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)

            # Handle missing values
            nan_count = df.isna().sum().sum()
            if nan_count > 0:
                self.logger.warning(f"Found {nan_count} NaN values in the dataset")
                df = df.ffill().bfill().fillna(0)
                self.logger.info("Handled NaN values using forward fill, backward fill, and zero fill")

            # Select features and target
            X = df[model_config.FEATURE_COLUMNS].values
            y = df['close'].values

            self.logger.info(f"Preprocessed data - X shape: {X.shape}, y shape: {y.shape}")
            return X, y

        except Exception as e:
            self.logger.error(f"Error loading and preprocessing data: {str(e)}")
            raise

    def calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics.

        Args:
            y_true: True target values
            y_pred: Predicted target values

        Returns:
            Dictionary of metric names and values
        """
        try:
            # Handle potential division by zero in MAPE calculation
            def safe_mape(y_true, y_pred):
                mask = y_true != 0
                return np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100

            metrics = {
                'mae': mean_absolute_error(y_true, y_pred),
                'mse': mean_squared_error(y_true, y_pred),
                'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
                'r2': r2_score(y_true, y_pred),
                'mape': safe_mape(y_true, y_pred)
            }

            # Calculate Mean Directional Accuracy (MDA)
            true_direction = np.diff(y_true)
            pred_direction = np.diff(y_pred)
            mda = np.mean((true_direction * pred_direction) > 0)
            metrics['mda'] = mda

            # Add additional metrics
            metrics['median_ae'] = np.median(np.abs(y_true - y_pred))
            metrics['max_ae'] = np.max(np.abs(y_true - y_pred))
            metrics['std_ae'] = np.std(np.abs(y_true - y_pred))

            # Calculate prediction bias
            metrics['bias'] = np.mean(y_pred - y_true)

            # Calculate relative metrics
            metrics['relative_mae'] = metrics['mae'] / np.mean(np.abs(y_true))
            metrics['relative_rmse'] = metrics['rmse'] / np.mean(np.abs(y_true))

            return metrics

        except Exception as e:
            self.logger.error(f"Error calculating metrics: {str(e)}")
            raise

    def plot_predictions(self, y_true: np.ndarray, y_pred: np.ndarray,
                        title: str, save_path: Path):
        """Plot actual vs predicted values.

        Args:
            y_true: True target values
            y_pred: Predicted target values
            title: Plot title
            save_path: Path to save the plot
        """
        try:
            plt.figure(figsize=(15, 6))
            plt.plot(y_true, label='Actual', alpha=0.7)
            plt.plot(y_pred, label='Predicted', alpha=0.7)
            plt.title(title)
            plt.xlabel('Time')
            plt.ylabel('Price')
            plt.legend()
            plt.grid(True)

            plt.savefig(save_path)
            plt.close()
            self.logger.info(f"Saved prediction plot to {save_path}")

        except Exception as e:
            self.logger.error(f"Error plotting predictions: {str(e)}")
            raise

    def plot_residuals(self, y_true: np.ndarray, y_pred: np.ndarray,
                      title: str, save_path: Path):
        """Plot prediction residuals.

        Args:
            y_true: True target values
            y_pred: Predicted target values
            title: Plot title
            save_path: Path to save the plot
        """
        try:
            residuals = y_true - y_pred

            plt.figure(figsize=(15, 6))
            plt.scatter(range(len(residuals)), residuals, alpha=0.5)
            plt.axhline(y=0, color='r', linestyle='-')
            plt.title(title)
            plt.xlabel('Time')
            plt.ylabel('Residual')
            plt.grid(True)

            plt.savefig(save_path)
            plt.close()
            self.logger.info(f"Saved residuals plot to {save_path}")

        except Exception as e:
            self.logger.error(f"Error plotting residuals: {str(e)}")
            raise

    def plot_error_distribution(self, y_true: np.ndarray, y_pred: np.ndarray,
                              title: str, save_path: Path):
        """Plot error distribution.

        Args:
            y_true: True target values
            y_pred: Predicted target values
            title: Plot title
            save_path: Path to save the plot
        """
        try:
            errors = y_true - y_pred

            plt.figure(figsize=(10, 6))
            sns.histplot(errors, kde=True)
            plt.title(title)
            plt.xlabel('Error')
            plt.ylabel('Count')

            plt.savefig(save_path)
            plt.close()
            self.logger.info(f"Saved error distribution plot to {save_path}")

        except Exception as e:
            self.logger.error(f"Error plotting error distribution: {str(e)}")
            raise

    def plot_metrics(self, metrics: Dict[str, float], title: str, save_path: Path):
        """Plot evaluation metrics.

        Args:
            metrics: Dictionary of metric names and values
            title: Plot title
            save_path: Path to save the plot
        """
        try:
            plt.figure(figsize=(10, 6))
            sns.barplot(x=list(metrics.keys()), y=list(metrics.values()))
            plt.title(title)
            plt.xticks(rotation=45)
            plt.tight_layout()

            plt.savefig(save_path)
            plt.close()
            self.logger.info(f"Saved metrics plot to {save_path}")

        except Exception as e:
            self.logger.error(f"Error plotting metrics: {str(e)}")
            raise

    def plot_feature_importance(self, importance: Dict[str, float],
                              title: str, save_path: Path):
        """Plot feature importance.

        Args:
            importance: Dictionary of feature names and importance scores
            title: Plot title
            save_path: Path to save the plot
        """
        try:
            plt.figure(figsize=(10, 6))
            sns.barplot(x=list(importance.keys()), y=list(importance.values()))
            plt.title(title)
            plt.xticks(rotation=45)
            plt.tight_layout()

            plt.savefig(save_path)
            plt.close()
            self.logger.info(f"Saved feature importance plot to {save_path}")

        except Exception as e:
            self.logger.error(f"Error plotting feature importance: {str(e)}")
            raise

    def monitor_gpu_usage(self):
        """Monitor GPU usage during training."""
        try:
            # Try to import and use GPU monitoring libraries
            try:
                import torch
                if torch.cuda.is_available():
                    try:
                        import GPUtil
                        gpus = GPUtil.getGPUs()
                        for gpu in gpus:
                            self.logger.info(f"GPU {gpu.id}: {gpu.load*100:.1f}% load, "
                                           f"{gpu.memoryUtil*100:.1f}% memory used")
                    except ImportError:
                        # Fallback to torch GPU info
                        gpu_count = torch.cuda.device_count()
                        for i in range(gpu_count):
                            memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
                            memory_cached = torch.cuda.memory_reserved(i) / 1024**3
                            self.logger.info(f"GPU {i}: {memory_allocated:.2f}GB allocated, "
                                           f"{memory_cached:.2f}GB cached")
                else:
                    self.logger.info("No CUDA GPU available")
            except ImportError:
                self.logger.info("PyTorch not available for GPU monitoring")

        except Exception as e:
            self.logger.warning(f"Error monitoring GPU usage: {str(e)}")
            # Don't raise exception for monitoring failures

    def monitor_memory_usage(self):
        """Monitor memory usage during training."""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            self.logger.info(f"Memory usage: {memory_info.rss / 1024 / 1024:.1f} MB")

        except Exception as e:
            self.logger.error(f"Error monitoring memory usage: {str(e)}")
            raise

    def generate_report(self):
        """Generate comprehensive test report."""
        try:
            # Calculate average metrics
            avg_metrics = pd.DataFrame(self.all_metrics).mean().to_dict()

            # Create report dictionary
            report = {
                'model_name': self.model_name,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'average_metrics': avg_metrics,
                'fold_metrics': self.all_metrics
            }

            # Save report
            report_path = self.output_dir / "test_report.json"
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=4)

            self.logger.info(f"Generated test report at {report_path}")

        except Exception as e:
            self.logger.error(f"Error generating report: {str(e)}")
            raise