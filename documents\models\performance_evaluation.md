# Model Performance Evaluation

## Evaluation Framework

The system implements comprehensive model evaluation across multiple dimensions to ensure robust trading performance.

## Performance Metrics

### 1. Prediction Accuracy Metrics

#### Regression Metrics
```python
def calculate_regression_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    
    return {
        'mae': float(mean_absolute_error(y_true, y_pred)),
        'mse': float(mean_squared_error(y_true, y_pred)),
        'rmse': float(np.sqrt(mean_squared_error(y_true, y_pred))),
        'r2': float(r2_score(y_true, y_pred)),
        'mape': float(np.mean(np.abs((y_true - y_pred) / y_true)) * 100)
    }
```

#### Directional Accuracy
```python
def calculate_directional_accuracy(y_true: np.ndarray, y_pred: np.ndarray) -> float:
    """Calculate the percentage of correct directional predictions."""
    if len(y_true) <= 1:
        return 0.0
    
    true_direction = np.diff(y_true)
    pred_direction = np.diff(y_pred)
    
    correct_predictions = np.sum((true_direction * pred_direction) > 0)
    total_predictions = len(true_direction)
    
    return float(correct_predictions / total_predictions)
```

### 2. Trading Performance Metrics

#### Financial Metrics
```python
def calculate_trading_metrics(trades: List[Dict]) -> Dict[str, float]:
    if not trades:
        return {}
    
    pnl_values = [trade['pnl'] for trade in trades]
    returns = [trade['return'] for trade in trades]
    
    return {
        'total_trades': len(trades),
        'winning_trades': sum(1 for pnl in pnl_values if pnl > 0),
        'win_rate': sum(1 for pnl in pnl_values if pnl > 0) / len(trades),
        'total_pnl': sum(pnl_values),
        'average_pnl': np.mean(pnl_values),
        'profit_factor': calculate_profit_factor(pnl_values),
        'sharpe_ratio': calculate_sharpe_ratio(returns),
        'max_drawdown': calculate_max_drawdown(pnl_values),
        'calmar_ratio': calculate_calmar_ratio(returns)
    }
```

#### Risk Metrics
```python
def calculate_risk_metrics(returns: List[float]) -> Dict[str, float]:
    returns_array = np.array(returns)
    
    return {
        'volatility': float(np.std(returns_array) * np.sqrt(252)),  # Annualized
        'var_95': float(np.percentile(returns_array, 5)),  # Value at Risk
        'cvar_95': float(np.mean(returns_array[returns_array <= np.percentile(returns_array, 5)])),
        'skewness': float(scipy.stats.skew(returns_array)),
        'kurtosis': float(scipy.stats.kurtosis(returns_array))
    }
```

## Model Comparison Framework

### 1. Cross-Model Evaluation
```python
class ModelComparison:
    def __init__(self, models: Dict[str, BaseModel]):
        self.models = models
        self.results = {}
    
    def evaluate_all_models(self, test_data: Dict) -> Dict[str, Dict]:
        """Evaluate all models on the same test dataset."""
        results = {}
        
        for model_name, model in self.models.items():
            # Generate predictions
            predictions = model.predict(test_data['X_test'])
            
            # Calculate metrics
            regression_metrics = calculate_regression_metrics(
                test_data['y_test'], predictions
            )
            
            directional_accuracy = calculate_directional_accuracy(
                test_data['y_test'], predictions
            )
            
            results[model_name] = {
                **regression_metrics,
                'directional_accuracy': directional_accuracy,
                'prediction_time': self._measure_prediction_time(model, test_data['X_test'])
            }
        
        return results
```

### 2. Statistical Significance Testing
```python
def test_model_significance(model1_results: np.ndarray, model2_results: np.ndarray) -> Dict:
    """Test if performance difference between models is statistically significant."""
    from scipy import stats
    
    # Paired t-test for comparing model performances
    t_stat, p_value = stats.ttest_rel(model1_results, model2_results)
    
    # Wilcoxon signed-rank test (non-parametric alternative)
    w_stat, w_p_value = stats.wilcoxon(model1_results, model2_results)
    
    return {
        't_test': {
            'statistic': float(t_stat),
            'p_value': float(p_value),
            'significant': p_value < 0.05
        },
        'wilcoxon_test': {
            'statistic': float(w_stat),
            'p_value': float(w_p_value),
            'significant': w_p_value < 0.05
        }
    }
```

## Timeframe-Specific Evaluation

### 1. Multi-Timeframe Analysis
```python
def evaluate_across_timeframes(model: BaseModel, timeframes: List[str]) -> Dict:
    """Evaluate model performance across different timeframes."""
    results = {}
    
    for timeframe in timeframes:
        # Load timeframe-specific test data
        test_data = load_test_data(timeframe)
        
        # Generate predictions
        predictions = model.predict(test_data['X_test'])
        
        # Calculate metrics
        metrics = calculate_regression_metrics(test_data['y_test'], predictions)
        metrics['directional_accuracy'] = calculate_directional_accuracy(
            test_data['y_test'], predictions
        )
        
        results[timeframe] = metrics
    
    return results
```

### 2. Timeframe Performance Comparison
```python
def compare_timeframe_performance(results: Dict[str, Dict]) -> Dict:
    """Compare model performance across timeframes."""
    comparison = {}
    
    for metric in ['mae', 'rmse', 'r2', 'directional_accuracy']:
        comparison[metric] = {
            timeframe: results[timeframe].get(metric, 0)
            for timeframe in results.keys()
        }
        
        # Find best performing timeframe for each metric
        if metric in ['r2', 'directional_accuracy']:
            # Higher is better
            best_timeframe = max(comparison[metric], key=comparison[metric].get)
        else:
            # Lower is better
            best_timeframe = min(comparison[metric], key=comparison[metric].get)
        
        comparison[f'{metric}_best'] = best_timeframe
    
    return comparison
```

## Model Selection Criteria

### 1. Multi-Criteria Decision Analysis
```python
def select_best_model(evaluation_results: Dict[str, Dict]) -> Dict:
    """Select best model based on multiple criteria."""
    
    # Define weights for different criteria
    weights = {
        'r2': 0.25,
        'directional_accuracy': 0.30,
        'rmse': 0.20,  # Lower is better, so we'll invert
        'mae': 0.15,   # Lower is better, so we'll invert
        'prediction_time': 0.10  # Lower is better, so we'll invert
    }
    
    scores = {}
    
    for model_name, metrics in evaluation_results.items():
        score = 0
        
        # Normalize and weight metrics
        score += weights['r2'] * metrics.get('r2', 0)
        score += weights['directional_accuracy'] * metrics.get('directional_accuracy', 0)
        score += weights['rmse'] * (1 / (1 + metrics.get('rmse', 1)))  # Invert RMSE
        score += weights['mae'] * (1 / (1 + metrics.get('mae', 1)))    # Invert MAE
        score += weights['prediction_time'] * (1 / (1 + metrics.get('prediction_time', 1)))
        
        scores[model_name] = score
    
    best_model = max(scores, key=scores.get)
    
    return {
        'best_model': best_model,
        'scores': scores,
        'selection_criteria': weights
    }
```

### 2. Ensemble Model Evaluation
```python
def evaluate_ensemble_performance(
    individual_predictions: Dict[str, np.ndarray],
    ensemble_weights: Dict[str, float],
    y_true: np.ndarray
) -> Dict:
    """Evaluate ensemble model performance."""
    
    # Calculate weighted ensemble predictions
    ensemble_pred = np.zeros_like(y_true)
    for model_name, predictions in individual_predictions.items():
        weight = ensemble_weights.get(model_name, 0)
        ensemble_pred += weight * predictions
    
    # Calculate ensemble metrics
    ensemble_metrics = calculate_regression_metrics(y_true, ensemble_pred)
    ensemble_metrics['directional_accuracy'] = calculate_directional_accuracy(y_true, ensemble_pred)
    
    # Compare with individual models
    individual_metrics = {}
    for model_name, predictions in individual_predictions.items():
        individual_metrics[model_name] = calculate_regression_metrics(y_true, predictions)
    
    return {
        'ensemble_metrics': ensemble_metrics,
        'individual_metrics': individual_metrics,
        'ensemble_weights': ensemble_weights,
        'improvement_over_best': calculate_ensemble_improvement(
            ensemble_metrics, individual_metrics
        )
    }
```

## Performance Monitoring

### 1. Real-time Performance Tracking
```python
class PerformanceTracker:
    def __init__(self):
        self.prediction_history = []
        self.actual_history = []
        self.performance_metrics = {}
    
    def update_performance(self, prediction: float, actual: float):
        """Update performance metrics with new prediction/actual pair."""
        self.prediction_history.append(prediction)
        self.actual_history.append(actual)
        
        # Calculate rolling metrics (last 100 predictions)
        if len(self.prediction_history) >= 100:
            recent_pred = np.array(self.prediction_history[-100:])
            recent_actual = np.array(self.actual_history[-100:])
            
            self.performance_metrics = calculate_regression_metrics(recent_actual, recent_pred)
            self.performance_metrics['directional_accuracy'] = calculate_directional_accuracy(
                recent_actual, recent_pred
            )
```

### 2. Performance Degradation Detection
```python
def detect_performance_degradation(
    current_metrics: Dict[str, float],
    baseline_metrics: Dict[str, float],
    threshold: float = 0.1
) -> Dict[str, bool]:
    """Detect if model performance has degraded significantly."""
    
    degradation_flags = {}
    
    for metric, current_value in current_metrics.items():
        baseline_value = baseline_metrics.get(metric, 0)
        
        if metric in ['r2', 'directional_accuracy']:
            # Higher is better
            degradation = (baseline_value - current_value) / baseline_value > threshold
        else:
            # Lower is better (MAE, RMSE, etc.)
            degradation = (current_value - baseline_value) / baseline_value > threshold
        
        degradation_flags[metric] = degradation
    
    return degradation_flags
```
