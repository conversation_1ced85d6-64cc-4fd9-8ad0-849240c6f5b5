#!/usr/bin/env python3
"""
MT5 Terminal Setup Script
Helps users configure and enable algorithmic trading in MT5 terminals.
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import MetaTrader5 as mt5

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('setup_terminals.log')
    ]
)
logger = logging.getLogger(__name__)

def check_mt5_installation() -> bool:
    """Check if MT5 is properly installed."""
    try:
        import MetaTrader5 as mt5
        logger.info("[OK] MetaTrader5 Python package is installed")
        return True
    except ImportError:
        logger.error("[ERROR] MetaTrader5 Python package is not installed")
        logger.error("Please install it with: pip install MetaTrader5")
        return False

def load_terminal_config() -> Optional[Dict]:
    """Load terminal configuration from config files."""
    try:
        # Try to load from consolidated config
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from config.consolidated_config import ConfigurationManager
        
        config_manager = ConfigurationManager()
        mt5_config = config_manager.get_mt5_config()
        
        if not mt5_config or not hasattr(mt5_config, 'terminals'):
            logger.error("❌ No terminal configuration found")
            return None
            
        terminals = {}
        for terminal_id, terminal_config in mt5_config.terminals.items():
            if isinstance(terminal_config, dict):
                terminals[terminal_id] = terminal_config
            else:
                terminals[terminal_id] = {
                    'path': terminal_config.path,
                    'login': terminal_config.login,
                    'password': terminal_config.password,
                    'server': terminal_config.server,
                    'trade_mode': getattr(terminal_config, 'trade_mode', True),
                    'auto_trading': getattr(terminal_config, 'auto_trading', True)
                }
        
        logger.info(f"[OK] Loaded configuration for {len(terminals)} terminals")
        return terminals

    except Exception as e:
        logger.error(f"[ERROR] Failed to load terminal configuration: {str(e)}")
        return None

def check_terminal_executable(path: str) -> bool:
    """Check if terminal executable exists."""
    if not os.path.exists(path):
        logger.error(f"[ERROR] Terminal executable not found: {path}")
        return False
    logger.info(f"[OK] Terminal executable found: {path}")
    return True

def test_terminal_connection(terminal_id: str, config: Dict) -> bool:
    """Test connection to a specific terminal."""
    logger.info(f"[TESTING] Connection to terminal {terminal_id}...")
    
    try:
        # Check if executable exists
        if not check_terminal_executable(config['path']):
            return False
        
        # Try to initialize MT5 with this terminal
        if not mt5.initialize(
            path=config['path'],
            login=int(config['login']),
            password=config['password'],
            server=config['server'],
            portable=True,
            timeout=60000
        ):
            error_code = mt5.last_error()
            logger.error(f"[ERROR] Failed to connect to terminal {terminal_id}: {error_code}")
            return False

        # Check terminal info
        terminal_info = mt5.terminal_info()
        if not terminal_info:
            logger.error(f"[ERROR] Failed to get terminal info for terminal {terminal_id}")
            return False

        # Check algorithmic trading status
        if terminal_info.trade_allowed:
            logger.info(f"[OK] Terminal {terminal_id}: Connection successful, Algo Trading ENABLED")
        else:
            logger.warning(f"[WARNING] Terminal {terminal_id}: Connection successful, but Algo Trading DISABLED")
            logger.warning(f"   Please enable Algo Trading manually in the terminal")

        return True

    except Exception as e:
        logger.error(f"[ERROR] Error testing terminal {terminal_id}: {str(e)}")
        return False

def open_terminal(path: str) -> bool:
    """Open MT5 terminal executable."""
    try:
        logger.info(f"[OPENING] Terminal: {path}")
        subprocess.Popen([path], shell=False)
        time.sleep(3)  # Give terminal time to start
        logger.info(f"[OK] Terminal opened successfully")
        return True
    except Exception as e:
        logger.error(f"[ERROR] Failed to open terminal: {str(e)}")
        return False

def print_algo_trading_instructions():
    """Print detailed instructions for enabling algorithmic trading."""
    print("\n" + "="*80)
    print("HOW TO ENABLE ALGORITHMIC TRADING IN MT5 TERMINALS")
    print("="*80)
    print()
    print("For EACH terminal that shows 'Algo Trading DISABLED':")
    print()
    print("1. [OPEN] Open the MT5 terminal (if not already open)")
    print("   - The terminal window should appear on your screen")
    print()
    print("2. [FIND] Look for the 'Algo Trading' button in the toolbar")
    print("   - It's usually in the top toolbar")
    print("   - It looks like a small robot or 'AT' icon")
    print("   - If disabled, it will have a RED light or be grayed out")
    print()
    print("3. [CLICK] Click the 'Algo Trading' button")
    print("   - The button should turn GREEN when enabled")
    print("   - You should see 'Algo Trading enabled' in the status bar")
    print()
    print("4. [VERIFY] Verify the status")
    print("   - Check the bottom status bar of the terminal")
    print("   - It should show 'Algo Trading enabled'")
    print("   - The button should have a GREEN light")
    print()
    print("5. [REPEAT] Repeat for ALL terminals")
    print("   - Each terminal needs to be enabled individually")
    print("   - Don't forget any terminal!")
    print()
    print("[WARNING] IMPORTANT NOTES:")
    print("   - This MUST be done manually for security reasons")
    print("   - MT5 prevents programmatic enablement of algo trading")
    print("   - The setting may reset if you restart the terminal")
    print("   - Always verify the status before running trading bots")
    print()
    print("="*80)

def main():
    """Main setup function."""
    print("MT5 Terminal Setup Script")
    print("="*50)

    # Check MT5 installation
    if not check_mt5_installation():
        return False

    # Load terminal configuration
    terminals = load_terminal_config()
    if not terminals:
        return False

    print(f"\n[INFO] Found {len(terminals)} terminals to test:")
    for terminal_id in terminals.keys():
        print(f"   - Terminal {terminal_id}")

    # Test each terminal
    results = {}
    algo_trading_disabled_terminals = []

    for terminal_id, config in terminals.items():
        print(f"\n[TESTING] Terminal {terminal_id}...")
        success = test_terminal_connection(terminal_id, config)
        results[terminal_id] = success

        if success:
            # Check if algo trading is disabled
            terminal_info = mt5.terminal_info()
            if terminal_info and not terminal_info.trade_allowed:
                algo_trading_disabled_terminals.append(terminal_id)

    # Print summary
    print("\n" + "="*50)
    print("TERMINAL TEST SUMMARY")
    print("="*50)

    successful_terminals = [tid for tid, success in results.items() if success]
    failed_terminals = [tid for tid, success in results.items() if not success]

    print(f"[OK] Successful connections: {len(successful_terminals)}")
    for tid in successful_terminals:
        print(f"   - Terminal {tid}")

    if failed_terminals:
        print(f"[ERROR] Failed connections: {len(failed_terminals)}")
        for tid in failed_terminals:
            print(f"   - Terminal {tid}")

    if algo_trading_disabled_terminals:
        print(f"[WARNING] Algo Trading DISABLED: {len(algo_trading_disabled_terminals)}")
        for tid in algo_trading_disabled_terminals:
            print(f"   - Terminal {tid}")

        print("\n[ACTION REQUIRED]:")
        print("The following terminals need Algo Trading enabled manually:")
        for tid in algo_trading_disabled_terminals:
            config = terminals[tid]
            print(f"   - Terminal {tid}: {config['path']}")

        # Print detailed instructions
        print_algo_trading_instructions()

        # Offer to open terminals
        response = input("\n[QUESTION] Would you like to open the terminals now? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            for tid in algo_trading_disabled_terminals:
                config = terminals[tid]
                open_terminal(config['path'])
                time.sleep(2)  # Delay between opening terminals

    # Final status
    if not failed_terminals and not algo_trading_disabled_terminals:
        print("\n[SUCCESS] ALL TERMINALS ARE READY!")
        print("[OK] All connections successful")
        print("[OK] Algo Trading enabled on all terminals")
        print("[READY] You can now run the trading bot!")
    else:
        print("\n[WARNING] SETUP NOT COMPLETE")
        if failed_terminals:
            print("[ERROR] Some terminals failed to connect")
        if algo_trading_disabled_terminals:
            print("[ERROR] Some terminals have Algo Trading disabled")
        print("[ACTION] Please resolve the issues above before running the trading bot")

    return len(failed_terminals) == 0 and len(algo_trading_disabled_terminals) == 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n[INTERRUPTED] Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"[ERROR] Unexpected error: {str(e)}")
        sys.exit(1)
