"""
MT5 connection manager for handling all MT5-related operations.
Provides centralized connection management with proper error handling and resource cleanup.
Ensures Algo Trading functionality is preserved by avoiding unnecessary shutdowns.
"""
import logging
import time
import MetaTrader5 as mt5
from typing import Dict, Optional, Tu<PERSON>, Any, Union
from threading import Lock, RLock
from dataclasses import dataclass
from datetime import datetime, timedelta
import os
import psutil

# FULL MODE: Enable MT5 Terminal Launcher for multi-terminal support
# Terminal launcher is required for managing multiple terminals
HAS_MT5_LAUNCHER = True
logging.info("Using full MT5 mode - terminal launcher enabled for multi-terminal support")

logger = logging.getLogger(__name__)

@dataclass
class MT5Connection:
    """Represents an MT5 connection with its state and metadata."""
    terminal_id: str  # Using string for consistency
    path: str
    login: Union[str, int]
    password: str
    server: str
    portable: bool = True     # CRITICAL: Must be True to preserve algorithmic trading in other terminals
    trade_mode: bool = True   # Enable trading
    auto_trading: bool = True # Enable auto trading
    is_connected: bool = False
    last_health_check: Optional[datetime] = None
    retry_count: int = 0
    last_error: Optional[str] = None
    connection_time: Optional[datetime] = None
    process_id: Optional[int] = None
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    last_trade_time: Optional[datetime] = None
    connection_attempts: int = 0
    successful_connections: int = 0
    failed_connections: int = 0
    total_uptime: timedelta = timedelta(0)
    last_reconnect: Optional[datetime] = None
    algo_trading_disabled: bool = False  # Flag to track if algo trading is disabled
    validated_only: bool = False  # Flag to indicate terminal is validated but not actively connected

    def __post_init__(self):
        """Convert login to int if it's a string and validate path."""
        if isinstance(self.login, str) and self.login.isdigit():
            self.login = int(self.login)
        if not os.path.exists(self.path):
            raise ValueError(f"MT5 terminal path does not exist: {self.path}")

    def update_resource_usage(self):
        """Update resource usage metrics."""
        if self.process_id:
            try:
                process = psutil.Process(self.process_id)
                self.memory_usage = process.memory_info().rss / (1024 * 1024)  # MB
                self.cpu_usage = process.cpu_percent()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                self.process_id = None
                self.memory_usage = 0.0
                self.cpu_usage = 0.0

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            'terminal_id': self.terminal_id,
            'is_connected': self.is_connected,
            'connection_time': self.connection_time,
            'last_health_check': self.last_health_check,
            'retry_count': self.retry_count,
            'last_error': self.last_error,
            'process_id': self.process_id,
            'memory_usage_mb': self.memory_usage,
            'cpu_usage_percent': self.cpu_usage,
            'last_trade_time': self.last_trade_time,
            'connection_attempts': self.connection_attempts,
            'successful_connections': self.successful_connections,
            'failed_connections': self.failed_connections,
            'total_uptime': str(self.total_uptime),
            'last_reconnect': self.last_reconnect,
            'algo_trading_disabled': self.algo_trading_disabled,
            'validated_only': self.validated_only
        }

class MT5ConnectionManager:
    """
    Manages MT5 connections with proper error handling and resource management.
    Implements connection pooling and ensures Algo Trading functionality is preserved.
    """

    # Singleton instance
    _instance = None
    _lock = RLock()

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of MT5ConnectionManager."""
        with cls._lock:
            if cls._instance is None:
                raise RuntimeError("MT5ConnectionManager not initialized")
            return cls._instance

    def __init__(self, config_manager, max_retries: int = 3, retry_delay: int = 5, health_check_interval: int = 60):
        """
        Initialize the MT5 connection manager.

        Args:
            config_manager: Configuration manager instance
            max_retries: Maximum number of connection retries
            retry_delay: Delay between retries in seconds
            health_check_interval: Interval between health checks in seconds
        """
        with self._lock:
            if MT5ConnectionManager._instance is not None:
                raise RuntimeError("MT5ConnectionManager already initialized")
            MT5ConnectionManager._instance = self

            self.config_manager = config_manager
            self._connections: Dict[str, MT5Connection] = {}
            self._terminal_locks: Dict[str, Lock] = {}
            self._max_retries = max_retries
            self._retry_delay = retry_delay
            self._health_check_interval = health_check_interval
            self._active_connection: Optional[str] = None
            self._last_cleanup = datetime.now()
            self._cleanup_interval = 300  # 5 minutes

            # FULL MODE: Enable auto_start for multi-terminal support
            # Auto-starting terminals is required for managing multiple terminals
            self.auto_start = True  # ENABLED in full mode for multi-terminal support
            logger.info("Auto-start terminals enabled in full MT5 mode for multi-terminal support")

            # Check if MT5 is already initialized (minimal mode)
            try:
                # Try to get terminal info to check if MT5 is already initialized
                terminal_info = mt5.terminal_info()
                if terminal_info:
                    logger.info("MT5 already initialized, using existing connection")
                else:
                    # Initialize MT5 with portable=True to preserve algo trading
                    if not mt5.initialize(portable=True):
                        logger.error("Failed to initialize MT5")
                        raise RuntimeError("Failed to initialize MT5")
                    logger.info("MT5 initialized with portable=True")
            except Exception as e:
                logger.warning(f"Error checking MT5 status: {str(e)}")
                # Try to initialize anyway
                if not mt5.initialize(portable=True):
                    logger.error("Failed to initialize MT5")
                    raise RuntimeError("Failed to initialize MT5")

            logger.info("MT5ConnectionManager initialized")

    def _cleanup_inactive_connections(self):
        """Clean up inactive connections to free resources."""
        current_time = datetime.now()
        if (current_time - self._last_cleanup).total_seconds() < self._cleanup_interval:
            return

        with self._lock:
            for terminal_id, conn in list(self._connections.items()):
                if not conn.is_connected:
                    if conn.last_health_check and (current_time - conn.last_health_check).total_seconds() > 3600:  # 1 hour
                        logger.info(f"Cleaning up inactive connection for terminal {terminal_id}")
                        self.disconnect(terminal_id)
                        del self._connections[terminal_id]
                        if terminal_id in self._terminal_locks:
                            del self._terminal_locks[terminal_id]

            self._last_cleanup = current_time

    def get_connection(self, terminal_id: Union[str, int], force_new: bool = False) -> Optional[MT5Connection]:
        """
        Get or create an MT5 connection with proper error handling.

        Args:
            terminal_id: ID of the MT5 terminal (string or int)
            force_new: Whether to force a new connection even if one exists

        Returns:
            Optional[MT5Connection]: Connection object if successful, None otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            # Clean up inactive connections
            self._cleanup_inactive_connections()

            # Check if connection exists and is healthy
            if not force_new and terminal_id_str in self._connections:
                conn = self._connections[terminal_id_str]
                if self._is_connection_healthy(conn):
                    # For validated-only connections, don't set as active to preserve current connection
                    if not (hasattr(conn, 'validated_only') and conn.validated_only):
                        self._active_connection = terminal_id_str
                    return conn

            # Get terminal configuration
            mt5_config = self.config_manager.get_mt5_config()
            if not mt5_config or not hasattr(mt5_config, 'terminals'):
                logger.error(f"MT5 configuration not found for terminal {terminal_id_str}")
                return None

            terminal_config = mt5_config.terminals.get(terminal_id_str)
            if not terminal_config:
                logger.error(f"Terminal configuration not found for terminal {terminal_id_str}")
                return None

            # Initialize or reinitialize connection
            success, conn = self._initialize_connection(terminal_id_str, terminal_config)
            if success:
                self._active_connection = terminal_id_str
                return conn
            return None

    def _is_connection_healthy(self, conn: MT5Connection) -> bool:
        """
        Check if a connection is healthy and if Algo Trading is enabled.

        Args:
            conn: MT5Connection object

        Returns:
            bool: True if connection is healthy and Algo Trading is enabled, False otherwise
        """
        # For validated-only connections, consider them healthy if they exist
        if hasattr(conn, 'validated_only') and conn.validated_only:
            # Check if terminal executable still exists
            if os.path.exists(conn.path):
                return True
            else:
                logger.warning(f"Terminal executable no longer exists: {conn.path}")
                return False

        if not conn.is_connected:
            return False

        current_time = datetime.now()
        if (conn.last_health_check is None or
            (current_time - conn.last_health_check).total_seconds() > self._health_check_interval):
            # Perform health check
            try:
                # Check if MT5 is already initialized
                terminal_info = mt5.terminal_info()
                if not terminal_info:
                    conn.is_connected = False
                    conn.last_error = "Terminal info check failed"
                    logger.warning(f"MT5 connection health check failed for terminal {conn.terminal_id}")
                    return False

                # Check if Algo Trading is enabled
                if not terminal_info.trade_allowed:
                    conn.is_connected = True  # Connection is still active
                    logger.error(f"[CRITICAL] Algorithmic trading is DISABLED for terminal {conn.terminal_id}")
                    logger.error(f"[CRITICAL] Trading operations will FAIL until this is resolved!")
                    logger.error(f"[SOLUTION] To enable Algorithmic trading for terminal {conn.terminal_id}:")
                    logger.error(f"1. Open the terminal at {conn.path}")
                    logger.error(f"2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
                    logger.error(f"3. Verify that 'Algo Trading enabled' appears in the status bar")
                    logger.error(f"4. The button should show a green light when enabled")
                    logger.error(f"[NOTE] This MUST be done manually - MT5 security prevents programmatic enablement")

                    # Set a flag to indicate algo trading is disabled
                    conn.algo_trading_disabled = True
                    return False  # Return False to indicate the connection is not fully functional
                else:
                    # Algo trading is enabled - reset the flag and log success
                    if conn.algo_trading_disabled:
                        logger.info(f"✅ Algorithmic trading is now ENABLED for terminal {conn.terminal_id}")
                        conn.algo_trading_disabled = False

                # Update resource usage
                conn.update_resource_usage()

                # Update health check timestamp
                conn.last_health_check = current_time
                return True
            except Exception as e:
                conn.is_connected = False
                conn.last_error = str(e)
                logger.error(f"Error during MT5 connection health check for terminal {conn.terminal_id}: {str(e)}")
                return False
        return True

    def _initialize_connection(self, terminal_id: str, terminal_config: Any) -> Tuple[bool, Optional[MT5Connection]]:
        """
        Initialize a new MT5 connection with retry logic.
        CRITICAL: Only connects to one terminal at a time to preserve algorithmic trading.

        Args:
            terminal_id: ID of the MT5 terminal
            terminal_config: Terminal configuration object

        Returns:
            Tuple[bool, Optional[MT5Connection]]: (success, connection)
        """
        try:
            # Validate terminal_id
            if not terminal_id:
                logger.error("Terminal ID cannot be empty")
                return False, None

            # Validate terminal_config
            if not terminal_config:
                logger.error(f"Terminal configuration is empty for terminal {terminal_id}")
                return False, None

            # Validate required fields in terminal_config
            required_fields = ['path', 'login', 'password', 'server']
            for field in required_fields:
                if not hasattr(terminal_config, field) and not (isinstance(terminal_config, dict) and field in terminal_config):
                    logger.error(f"Missing required field '{field}' in terminal configuration for terminal {terminal_id}")
                    return False, None

            # Get path, login, password, and server from terminal_config
            if isinstance(terminal_config, dict):
                path = terminal_config['path']
                login = terminal_config['login']
                password = terminal_config['password']
                server = terminal_config['server']
                trade_mode = terminal_config.get('trade_mode', True)
                auto_trading = terminal_config.get('auto_trading', True)
            else:
                path = terminal_config.path
                login = terminal_config.login
                password = terminal_config.password
                server = terminal_config.server
                trade_mode = getattr(terminal_config, 'trade_mode', True)
                auto_trading = getattr(terminal_config, 'auto_trading', True)

            # Ensure login is an integer for MT5
            if isinstance(login, str) and login.isdigit():
                login = int(login)
            elif not isinstance(login, int):
                logger.error(f"Invalid login format for terminal {terminal_id}: {login}")
                return False, None

            # Get or create connection object
            conn = self._connections.get(terminal_id)
            if not conn:
                conn = MT5Connection(
                    terminal_id=terminal_id,
                    path=path,
                    login=login,
                    password=password,
                    server=server,
                    portable=True,     # CRITICAL: Must be True to preserve algorithmic trading in other terminals
                    trade_mode=trade_mode,
                    auto_trading=auto_trading
                )

            # Check if we've exceeded max retries
            if conn.retry_count >= self._max_retries:
                error_msg = f"Max retries ({self._max_retries}) exceeded for terminal {terminal_id}"
                logger.error(error_msg)
                return False, None

            # FULL MODE: Support multiple terminals by ensuring each terminal process is running
            # Each terminal runs as a separate process, not through MT5 Python API
            # The MT5 Python API will connect to the primary terminal (usually terminal 1)

            # Import terminal launcher for multi-terminal support
            try:
                from utils.mt5_launcher import is_terminal_running, start_terminal

                # Check if terminal process is running
                if not is_terminal_running(terminal_id):
                    logger.info(f"Terminal {terminal_id} process not running, attempting to start...")
                    if not start_terminal(terminal_id):
                        logger.error(f"Failed to start terminal {terminal_id} process")
                        conn.last_error = f"Failed to start terminal {terminal_id} process"
                        conn.retry_count += 1
                        conn.failed_connections += 1
                        return False, None
                    else:
                        logger.info(f"Terminal {terminal_id} process started successfully")

                # For terminals other than the primary (terminal 1), mark as process-validated
                if terminal_id != '1':
                    # Validate terminal process is running but don't connect via MT5 API
                    if not os.path.exists(path):
                        logger.error(f"Terminal executable not found: {path}")
                        conn.last_error = f"Terminal executable not found: {path}"
                        conn.retry_count += 1
                        conn.failed_connections += 1
                        return False, None

                    # Mark as process-connected (terminal is running as separate process)
                    conn.is_connected = True  # Process is running
                    conn.connection_time = datetime.now()
                    conn.last_health_check = datetime.now()
                    conn.retry_count = 0
                    conn.last_error = None
                    conn.successful_connections += 1
                    conn.connection_attempts += 1

                    # Add a flag to indicate this is a process-based connection
                    conn.validated_only = True

                    # Store connection as process-based
                    self._connections[terminal_id] = conn
                    logger.info(f"Terminal {terminal_id} connected as separate process (multi-terminal mode)")
                    logger.info(f"Terminal {terminal_id} is available for independent trading operations")
                    return True, conn

            except ImportError as e:
                logger.error(f"Could not import terminal launcher: {str(e)}")
                # Fall back to validation-only mode
                pass

            # PRIMARY TERMINAL INITIALIZATION - Connect via MT5 API (usually terminal 1)
            logger.info(f"Using MT5 API initialization for primary terminal {terminal_id}")

            # Initialize MT5 connection with portable=True to preserve algo trading
            if not mt5.initialize(
                path=path,
                login=login,
                password=password,
                server=server,
                portable=True,  # CRITICAL: Preserves algo trading
                timeout=60000
            ):
                error_code = mt5.last_error()
                conn.last_error = f"Failed to initialize MT5: {error_code}"
                conn.retry_count += 1
                conn.failed_connections += 1
                logger.error(f"Failed to initialize MT5 for terminal {terminal_id}: {error_code}")
                return False, None

            # Update connection state
            conn.is_connected = True
            conn.connection_time = datetime.now()
            conn.last_health_check = datetime.now()
            conn.retry_count = 0
            conn.last_error = None
            conn.successful_connections += 1
            conn.connection_attempts += 1

            # Get process ID
            terminal_info = mt5.terminal_info()
            if terminal_info:
                # Check if pid attribute exists (not available in all MT5 versions)
                if hasattr(terminal_info, 'pid'):
                    conn.process_id = terminal_info.pid
                else:
                    # Try to find the process ID by matching the terminal path
                    try:
                        import psutil
                        for proc in psutil.process_iter(['pid', 'name', 'exe']):
                            try:
                                if proc.info['name'] and 'terminal64.exe' in proc.info['name'].lower():
                                    if proc.info['exe'] and path in proc.info['exe']:
                                        conn.process_id = proc.info['pid']
                                        break
                            except (psutil.AccessDenied, psutil.NoSuchProcess):
                                continue
                    except Exception as e:
                        logger.debug(f"Could not determine process ID for terminal {terminal_id}: {str(e)}")
                        conn.process_id = None

            # Store connection
            self._connections[terminal_id] = conn

            logger.info(f"Successfully initialized MT5 connection for terminal {terminal_id}")
            return True, conn

        except Exception as e:
            logger.error(f"Error initializing connection for terminal {terminal_id}: {str(e)}")
            if conn:
                conn.last_error = str(e)
                conn.retry_count += 1
                conn.failed_connections += 1
            return False, None

    def shutdown(self, terminal_id: Union[str, int]) -> bool:
        """
        Shutdown an MT5 connection with warnings about Algo Trading impact.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            bool: True if shutdown successful, False otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            if terminal_id_str in self._connections:
                conn = self._connections[terminal_id_str]
                try:
                    if conn.is_connected:
                        # Only shutdown if this is the active connection
                        if self._active_connection == terminal_id_str:
                            # Check if Algo Trading is enabled before shutting down
                            try:
                                terminal_info = mt5.terminal_info()
                                if terminal_info and terminal_info.trade_allowed:
                                    logger.warning(f"[WARNING] Terminal {terminal_id_str} has Algo Trading ENABLED")
                                    logger.warning(f"[WARNING] Shutting down will disable Algo Trading in this terminal")
                                    logger.warning(f"Consider using disconnect() instead if you need to maintain Algo Trading")
                            except Exception:
                                # Ignore errors during check
                                pass

                            logger.info(f"Disconnecting MT5 connection for terminal {terminal_id_str} without shutdown")
                            # DO NOT call mt5.shutdown() as it disables Algo Trading
                            # Just reinitialize with portable=True to preserve settings
                            mt5.initialize(portable=True)
                            self._active_connection = None

                            # Provide instructions for re-enabling Algo Trading
                            logger.warning(f"[WARNING] To re-enable Algo Trading for terminal {terminal_id_str}:")
                            logger.warning(f"1. Open the terminal at {conn.path}")
                            logger.warning(f"2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
                            logger.warning(f"3. Verify that 'Algo Trading enabled' appears in the status bar")
                        conn.is_connected = False
                    del self._connections[terminal_id_str]
                    logger.info(f"MT5 connection removed for terminal {terminal_id_str}")
                    return True
                except Exception as e:
                    logger.error(f"Error shutting down MT5 connection for terminal {terminal_id_str}: {str(e)}")
                    return False
            return True

    def disconnect(self, terminal_id: Union[str, int]) -> bool:
        """
        Disconnect from an MT5 connection without shutting down MT5.
        This preserves Algo Trading settings in the terminal.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            bool: True if disconnection successful, False otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            if terminal_id_str in self._connections:
                conn = self._connections[terminal_id_str]
                try:
                    # Just mark as disconnected without shutting down MT5
                    conn.is_connected = False

                    # Reset active connection if this was the active one
                    if self._active_connection == terminal_id_str:
                        self._active_connection = None

                    # Remove from connections dictionary
                    del self._connections[terminal_id_str]
                    logger.info(f"MT5 connection to terminal {terminal_id_str} disconnected (without shutdown)")
                    logger.info(f"Algo Trading settings in terminal {terminal_id_str} are preserved")
                    return True
                except Exception as e:
                    logger.error(f"Error disconnecting from terminal {terminal_id_str}: {str(e)}")
                    return False
            return True

    def shutdown_all(self) -> None:
        """Shutdown all MT5 connections with minimal impact on Algo Trading."""
        with self._lock:
            if self._active_connection:
                try:
                    # IMPORTANT: We need to be extremely careful with shutting down MT5
                    # This can disable algorithmic trading in all terminals
                    # Only do this at the end of the program or when absolutely necessary
                    logger.info("Shutting down all MT5 connections")
                    logger.warning("[WARNING] This operation may disable algorithmic trading in all terminals")
                    logger.warning("Only proceed if you're exiting the application completely")

                    # Check if any terminal has Algo Trading enabled
                    algo_trading_enabled = False
                    for terminal_id_str, conn in self._connections.items():
                        try:
                            # Only check if the connection is active
                            if conn.is_connected:
                                terminal_info = mt5.terminal_info()
                                if terminal_info and terminal_info.trade_allowed:
                                    algo_trading_enabled = True
                                    logger.warning(f"[WARNING] Terminal {terminal_id_str} has Algo Trading ENABLED")
                                    logger.warning(f"[WARNING] Shutting down will disable Algo Trading in this terminal")
                        except Exception:
                            # Ignore errors during check
                            pass

                    if algo_trading_enabled:
                        logger.warning("[WARNING] CRITICAL WARNING: Algo Trading is currently ENABLED in at least one terminal")
                        logger.warning("[WARNING] Shutting down will disable Algo Trading and require manual re-enabling")
                        logger.warning("[WARNING] Consider using disconnect_all() instead if you need to maintain Algo Trading")

                    # Instead of immediately shutting down, we'll mark connections as disconnected first
                    for terminal_id_str, conn in self._connections.items():
                        conn.is_connected = False

                    # DO NOT call mt5.shutdown() as it disables Algo Trading
                    # Just reinitialize with portable=True to preserve settings
                    logger.info("Reinitializing with portable=True to preserve Algo Trading")
                    mt5.initialize(portable=True)
                    self._active_connection = None

                    # Provide instructions for re-enabling Algo Trading
                    if algo_trading_enabled:
                        logger.warning("[WARNING] To re-enable Algo Trading after shutdown:")
                        logger.warning("1. Open each MT5 terminal manually")
                        logger.warning("2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
                        logger.warning("3. Verify that 'Algo Trading enabled' appears in the status bar")

                    # MINIMAL MODE: Skip terminal stopping to preserve algo trading
                    # Terminals will remain running with algo trading enabled
                    logger.info("Minimal mode: Terminals remain running to preserve algo trading")

                except Exception as e:
                    logger.error(f"Error shutting down all MT5 connections: {str(e)}")

            # Clear connections dictionary
            self._connections.clear()

    def disconnect_all(self) -> None:
        """Disconnect from all MT5 connections without shutting down MT5.
        This preserves Algo Trading settings in all terminals."""
        with self._lock:
            logger.info("Disconnecting from all MT5 connections without shutdown")
            logger.info("This preserves Algo Trading settings in all terminals")

            # Mark all connections as disconnected
            for terminal_id_str, conn in self._connections.items():
                conn.is_connected = False
                logger.info(f"Marked connection to terminal {terminal_id_str} as disconnected")

            # Reset active connection
            self._active_connection = None

            # Clear connections dictionary
            self._connections.clear()

    def get_connection_status(self, terminal_id: Union[str, int]) -> Optional[MT5Connection]:
        """
        Get the status of an MT5 connection.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            Optional[MT5Connection]: Connection status if exists, None otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            return self._connections.get(terminal_id_str)

    def get_all_connections(self) -> Dict[str, MT5Connection]:
        """
        Get all MT5 connections.

        Returns:
            Dict[str, MT5Connection]: Dictionary of all connections
        """
        with self._lock:
            return self._connections.copy()

    def get_active_connection_id(self) -> Optional[str]:
        """
        Get the ID of the active MT5 connection.

        Returns:
            Optional[str]: ID of the active connection if any, None otherwise
        """
        with self._lock:
            return self._active_connection

    def _get_terminal_lock(self, terminal_id: str) -> Lock:
        """
        Get a lock for a specific terminal.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            Lock: Lock for the terminal
        """
        with self._lock:
            if terminal_id not in self._terminal_locks:
                self._terminal_locks[terminal_id] = Lock()
            return self._terminal_locks[terminal_id]

    def execute_mt5_operation(self, terminal_id: Union[str, int], operation_func, *args, **kwargs):
        """
        Thread-safe execution of MT5 operations.

        Args:
            terminal_id: ID of the MT5 terminal
            operation_func: Function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Result of the operation or None if failed
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        # Get connection
        conn = self.get_connection(terminal_id_str)
        if not conn or not conn.is_connected:
            logger.error(f"Cannot execute operation: No connection for terminal {terminal_id_str}")
            return None

        # Acquire lock for this terminal
        terminal_lock = self._get_terminal_lock(terminal_id_str)
        with terminal_lock:
            try:
                return operation_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"MT5 operation failed: {str(e)}")
                return None

    def thread_safe_mt5_operation(self, terminal_id: Union[str, int]):
        """
        Decorator for thread-safe MT5 operations.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            Decorated function
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                return self.execute_mt5_operation(terminal_id, func, *args, **kwargs)
            return wrapper
        return decorator

    def reconnect(self, terminal_id: Union[str, int]) -> bool:
        """
        Reconnect to an MT5 terminal while preserving Algo Trading settings.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            bool: True if reconnection successful, False otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            # Check if terminal exists in configuration
            mt5_config = self.config_manager.get_mt5_config()
            if not mt5_config or not hasattr(mt5_config, 'terminals'):
                logger.error(f"MT5 configuration not found for terminal {terminal_id_str}")
                return False

            terminal_config = mt5_config.terminals.get(terminal_id_str)
            if not terminal_config:
                logger.error(f"Terminal configuration not found for terminal {terminal_id_str}")
                return False

            # Check if Algo Trading is enabled before reconnecting
            algo_trading_enabled = False
            if terminal_id_str in self._connections and self._connections[terminal_id_str].is_connected:
                try:
                    terminal_info = mt5.terminal_info()
                    if terminal_info and terminal_info.trade_allowed:
                        algo_trading_enabled = True
                        logger.info(f"Algo Trading is currently ENABLED for terminal {terminal_id_str}")
                        logger.info(f"Will attempt to preserve this setting during reconnection")
                except Exception:
                    # Ignore errors during check
                    pass

            # Use disconnect instead of shutdown to preserve Algo Trading
            if algo_trading_enabled:
                logger.info(f"Using disconnect instead of shutdown to preserve Algo Trading")
                self.disconnect(terminal_id_str)
            else:
                # Shutdown existing connection
                self.shutdown(terminal_id_str)

            # Wait before reconnecting
            time.sleep(self._retry_delay)

            # Initialize new connection with portable=True to preserve Algo Trading
            success, _ = self._initialize_connection(terminal_id_str, terminal_config)

            if success:
                logger.info(f"Successfully reconnected to terminal {terminal_id_str}")

                # Check if Algo Trading was preserved
                if algo_trading_enabled:
                    try:
                        terminal_info = mt5.terminal_info()
                        if terminal_info and terminal_info.trade_allowed:
                            logger.info(f"✅ Algo Trading was successfully preserved for terminal {terminal_id_str}")
                        else:
                            logger.warning(f"⚠️ Algo Trading was DISABLED during reconnection for terminal {terminal_id_str}")
                            logger.warning(f"Please manually re-enable Algo Trading in the terminal")
                    except Exception:
                        # Ignore errors during check
                        pass

                return True
            else:
                logger.error(f"Failed to reconnect to terminal {terminal_id_str}")
                return False
