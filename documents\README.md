# Multi-Terminal Algorithmic Trading Bot Documentation

This documentation is organized into 5 logical subfolders, each containing no more than 8 documents that reflect the actual multi-terminal PyTorch-based codebase implementation with advanced time series forecasting capabilities.

## 🎯 System Overview

**Multi-Terminal Trading Architecture**: 5 MT5 terminals running simultaneously with 9 different ML models providing trading signals for BTCUSD across multiple timeframes (M5, M15, M30, H1, H4).

**Key Features**:
- ✅ **5 MT5 Terminals**: Parallel trading execution with algorithmic trading enabled
- ✅ **9 ML Models**: LSTM, GRU, Transformer, TFT, XGBoost, LightGBM, ARIMA, LSTM+ARIMA, TFT+ARIMA
- ✅ **GPU Acceleration**: NVIDIA RTX 4070 with 12.88GB VRAM support
- ✅ **Advanced Memory Management**: Adaptive thresholds and intelligent cleanup
- ✅ **Circuit Breakers**: Failure prevention and automatic recovery
- ✅ **Real-time Monitoring**: Performance tracking and visualization

## 📁 Documentation Structure

### 1. `/setup/` - Installation and Configuration
- **Installation Guide**: Complete setup instructions with dependencies and requirements
- **Configuration Guide**: Multi-terminal ConfigurationManager and config.json setup
- **Data Collection Setup**: Historical data collection and preprocessing across all terminals

### 2. `/architecture/` - System Architecture and Design
- **System Overview**: Complete multi-terminal architecture with all components and relationships
- **Data Flow**: Real-time data processing pipeline from MT5 to ML model predictions

### 3. `/models/` - Machine Learning Models
- **Model Implementations**: All 9 model types with PyTorch/Scikit-learn implementations
- **Training Procedures**: Multi-timeframe training workflows and optimization procedures
- **Performance Evaluation**: Comprehensive metrics, validation, and model comparison

### 4. `/trading/` - Trading Operations
- **MT5 Integration**: Multi-terminal connection management and data collection
- **Trading Operations**: Signal generation, execution strategies, and risk management

### 5. `/api/` - API Reference and Utilities
- **Utilities Reference**: Enhanced memory manager, error handler, and utility functions
- **Error Handling**: Advanced error handling, circuit breakers, and recovery mechanisms
- **Monitoring & Logging**: Real-time performance monitoring and comprehensive logging

## 🚀 Quick Start Reference

### Start Multi-Terminal Trading System
```bash
# Start all 5 terminals with all 9 models
python main.py

# Monitor system performance
python monitor_bot_performance.py
```

### Train Models for All Timeframes
```bash
# Train all models for all timeframes (M5, M15, M30, H1, H4)
python train_all_timeframes.py

# Train specific models
python train_models.py --models lstm gru transformer
python train_models.py --models tft xgboost lightgbm
python train_models.py --models arima lstm_arima tft_arima
```

### Data Collection Across All Terminals
```bash
# Collect data from all 5 terminals
python collect_from_all_terminals.py

# Collect historical data for training
python collect_historical_data.py
```

### Individual Model Training with Custom Parameters
```bash
# PyTorch models with GPU acceleration
python train_models.py --models lstm --epochs 200 --batch-size 128 --learning-rate 0.0005
python train_models.py --models tft --epochs 50 --max-samples-per-dataset 50000 --batch-size 64
python train_models.py --models transformer --epochs 100 --sequence-length 120 --dropout-rate 0.1

# Gradient boosting models
python train_models.py --models xgboost --epochs 300 --learning-rate 0.01
python train_models.py --models lightgbm --epochs 200 --learning-rate 0.05

# Ensemble models
python train_models.py --models lstm_arima tft_arima --epochs 150
```

### Available Models (9 Total)
- **lstm**: Long Short-Term Memory (PyTorch) - Terminal 1 primary
- **gru**: Gated Recurrent Unit (PyTorch) - Terminal 2 primary
- **transformer**: Transformer Neural Network (PyTorch) - Advanced attention mechanism
- **tft**: Temporal Fusion Transformer (PyTorch Forecasting) - Terminal 3 primary
- **xgboost**: XGBoost Gradient Boosting - Terminal 4 primary
- **lightgbm**: LightGBM Gradient Boosting - Terminal 5 primary
- **arima**: ARIMA Time Series Model - Statistical baseline
- **lstm_arima**: LSTM + ARIMA Ensemble - Hybrid approach
- **tft_arima**: TFT + ARIMA Ensemble - Advanced hybrid

### System Requirements
- **Python**: 3.8+ with PyTorch, TensorFlow, and scikit-learn
- **GPU**: NVIDIA RTX 4070 (12.88GB VRAM) for optimal performance
- **Memory**: 16GB+ RAM recommended for multi-terminal operation
- **MT5**: 5 MetaTrader 5 terminals with algorithmic trading enabled

### 📖 Complete Documentation
For comprehensive guides, see the respective documentation sections:
- **[`/setup/`](setup/)** - Installation and configuration
- **[`/architecture/`](architecture/)** - System design and data flow
- **[`/models/`](models/)** - ML model implementations and training
- **[`/trading/`](trading/)** - Trading operations and MT5 integration
- **[`/api/`](api/)** - Utilities, error handling, and monitoring

## Documentation Standards

- ✅ All documentation reflects actual multi-terminal codebase implementation
- ✅ No speculative or planned features - only working components
- ✅ Maximum 8 documents per subfolder for logical organization
- ✅ Clear, concise, and accurate content based on real code analysis
- ✅ Regular updates to match codebase changes and improvements
