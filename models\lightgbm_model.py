"""
LightGBM model implementation for time series prediction.
"""
# Standard library imports
import logging
from typing import Dict, Optional, Any
import os
from pathlib import Path

# Third-party imports
import numpy as np
import lightgbm as lgb

# Local imports
from .base_model import BaseModel

logger = logging.getLogger(__name__)

class LightGBMModel(BaseModel):
    def __init__(self, config: Dict[str, Any]):
        """Initialize LightGBM model with a configuration dictionary."""
        super().__init__(config)

        # Initialize model parameters from config using dictionary access
        # Use .get() with defaults for robustness
        self.num_leaves = self.config.get('num_leaves', 31) # Default LightGBM value
        self.learning_rate = self.config.get('learning_rate', 0.05)
        self.n_estimators = self.config.get('n_estimators', 100)
        self.min_child_samples = self.config.get('min_child_samples', 20)
        self.subsample = self.config.get('subsample', 1.0)
        self.colsample_bytree = self.config.get('colsample_bytree', 1.0)

        # Model is initialized in build()
        self.model = None

    def build(self) -> None:
        """Build the LightGBM model architecture using parameters from config."""
        try:
            # Check for GPU support
            use_gpu = self.config.get('use_gpu', False)
            device_type = 'cpu'

            if use_gpu:
                try:
                    # Test if GPU is available by checking CUDA
                    import torch
                    if torch.cuda.is_available():
                        device_type = 'gpu'
                        logger.info(f"LightGBM GPU support enabled for '{self.model_name}'")
                    else:
                        logger.warning(f"CUDA not available for LightGBM '{self.model_name}', using CPU")
                        device_type = 'cpu'
                except Exception as e:
                    logger.warning(f"GPU not available for LightGBM '{self.model_name}', falling back to CPU: {e}")
                    device_type = 'cpu'

            self.model = lgb.LGBMRegressor(
                num_leaves=self.num_leaves,
                learning_rate=self.learning_rate,
                n_estimators=self.n_estimators,
                min_child_samples=self.min_child_samples,
                subsample=self.subsample,
                colsample_bytree=self.colsample_bytree,
                objective=self.config.get('objective', 'regression'),
                random_state=self.config.get('random_state', 42),
                device_type=device_type,
                n_jobs=self.config.get('n_jobs', -1)  # Use all available cores
            )
            logger.info(f"LightGBM model ('{self.model_name}') built successfully with params: {self.model.get_params()}")

        except Exception as e:
            logger.error(f"Error building LightGBM model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def train(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[tuple] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Train the LightGBM model."""
        try:
            # Handle epochs parameter override
            epochs = kwargs.get('epochs')
            if epochs is not None:
                logger.info(f"Overriding n_estimators with epochs parameter: {epochs}")
                self.n_estimators = epochs
                # Rebuild model with new n_estimators
                self.build()

            # Input validation
            expected_input_dim = self.config.get('input_dim')
            if expected_input_dim is not None and X.shape[1] != expected_input_dim:
                # Check if model uses flattened sequences
                uses_flattened_sequence = self.config.get('uses_flattened_sequence', False)
                if uses_flattened_sequence:
                    # For flattened sequences, the input dimension is sequence_length * feature_dim
                    sequence_length = self.config.get('sequence_length', 60)
                    # Calculate feature dimension for debugging purposes
                    _ = expected_input_dim // sequence_length
                    if X.shape[1] == expected_input_dim:
                        # Input is already correctly shaped
                        pass
                    else:
                        raise ValueError(
                            f"Input shape mismatch for model '{self.model_name}'. Expected (batch_size, {expected_input_dim}), "
                            f"got {X.shape}"
                        )
                else:
                    raise ValueError(
                        f"Input shape mismatch for model '{self.model_name}'. Expected (batch_size, {expected_input_dim}), "
                        f"got {X.shape}"
                    )

            # LightGBM handles 1D target
            if y.ndim == 2 and y.shape[1] == 1:
                y_processed = y.ravel()
            elif y.ndim == 1:
                y_processed = y
            else:
                # Handle multi-output? LightGBM needs wrapper like MultiOutputRegressor
                raise ValueError(f"LightGBM currently handles 1D target, but got shape {y.shape}")

            # Build model if not already built
            if self.model is None:
                logger.info(f"Model '{self.model_name}' not built. Building now...")
                self.build()
            if self.model is None: # Check build success
                raise RuntimeError(f"Failed to build model '{self.model_name}' before training.")

            # Setup evaluation data and early stopping callback
            eval_set = None
            callbacks = []
            if validation_data:
                X_val, y_val = validation_data
                if y_val.ndim == 2 and y_val.shape[1] == 1:
                    y_val_processed = y_val.ravel()
                elif y_val.ndim == 1:
                    y_val_processed = y_val
                else:
                    raise ValueError(f"Unsupported validation target shape {y_val.shape}")
                eval_set = [(X_val, y_val_processed)]

                # Add early stopping callback if validation data is provided
                early_stopping_rounds = self.config.get('early_stopping_rounds', 10)
                if early_stopping_rounds and early_stopping_rounds > 0:
                    callbacks.append(lgb.early_stopping(stopping_rounds=early_stopping_rounds,
                                                      verbose=self.config.get('verbose', False)))

            eval_metric = self.config.get('eval_metric', ['l2', 'l1']) # Default lgbm metrics

            # Extract training-specific args from config or kwargs
            fit_params = {
                'eval_set': eval_set,
                'eval_metric': eval_metric,
                'callbacks': callbacks
            }
            # Add specific LightGBM training params from config if needed
            # e.g., fit_params['feature_name'] = self.config.get('FEATURE_COLUMNS', 'auto')
            # e.g., fit_params['categorical_feature'] = self.config.get('categorical_features', 'auto')

            # Filter out parameters that LightGBM doesn't accept
            lgb_invalid_params = ['epochs', 'batch_size', 'patience', 'verbose']
            filtered_kwargs = {k: v for k, v in kwargs.items() if k not in lgb_invalid_params}
            fit_params.update(filtered_kwargs) # Allow overriding via filtered kwargs

            logger.info(f"Starting training for LightGBM model '{self.model_name}'...")
            # Train model
            self.model.fit(X, y_processed, **fit_params)

            # Store training history (if applicable, LightGBM stores in model.evals_result_)
            self.history = self.model.evals_result_ if hasattr(self.model, 'evals_result_') else {}
            logger.info(f"LightGBM model '{self.model_name}' trained successfully. Best iteration: {self.model.best_iteration_}")
            return self.history

        except Exception as e:
            logger.error(f"Error training LightGBM model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def predict(self, X: np.ndarray) -> np.ndarray:
        try:
            # Input validation
            expected_input_dim = self.config.get('input_dim')
            if expected_input_dim is not None and X.shape[1] != expected_input_dim:
                # For LightGBM trained with flattened sequences, we need to check if the input matches
                # the expected flattened dimension (sequence_length * input_dim)
                sequence_length = self.config.get('sequence_length', 60)
                flattened_dim = sequence_length * expected_input_dim

                # Try to adapt the input using ModelInputAdapter
                try:
                    # If X is 3D, flatten it to 2D
                    if len(X.shape) == 3:
                        batch_size, seq_len, features = X.shape
                        X_flat = X.reshape(batch_size, seq_len * features)

                        # If the flattened shape is still not correct, pad or truncate
                        if X_flat.shape[1] < flattened_dim:
                            # Pad with zeros
                            padding = np.zeros((batch_size, flattened_dim - X_flat.shape[1]))
                            X = np.concatenate([X_flat, padding], axis=1)
                        elif X_flat.shape[1] > flattened_dim:
                            # Truncate
                            X = X_flat[:, :flattened_dim]
                        else:
                            X = X_flat
                    elif len(X.shape) == 2 and X.shape[1] != flattened_dim:
                        # If X is already 2D but wrong size, pad or truncate
                        batch_size = X.shape[0]
                        if X.shape[1] < flattened_dim:
                            # Pad with zeros
                            padding = np.zeros((batch_size, flattened_dim - X.shape[1]))
                            X = np.concatenate([X, padding], axis=1)
                        elif X.shape[1] > flattened_dim:
                            # Truncate
                            X = X[:, :flattened_dim]
                except Exception as adapt_error:
                    logger.error(f"Error adapting input for LightGBM model '{self.model_name}': {str(adapt_error)}")
                    raise ValueError(
                        f"Input shape mismatch for prediction. Expected (batch_size, {expected_input_dim}) or "
                        f"(batch_size, {flattened_dim}), got {X.shape}"
                    )

            if self.model is None:
                raise RuntimeError(f"Model '{self.model_name}' must be loaded or trained before calling predict.")

            # Check if model is a Booster (loaded from file) or LGBMRegressor (trained in memory)
            if isinstance(self.model, lgb.Booster):
                # For Booster, we need to use predict directly
                return self.model.predict(X)
            else:
                # For LGBMRegressor, we use the sklearn interface
                return self.model.predict(X)

        except Exception as e:
            logger.error(f"Error making predictions with LightGBM model '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def save_model(self, path: str) -> None:
        """Save the LightGBM model to the specified full path."""
        try:
            if self.model is None:
                raise ValueError("Model has not been built or trained yet. Cannot save.")

            # Ensure directory exists
            Path(path).parent.mkdir(parents=True, exist_ok=True)

            # Check if model is a Booster (loaded from file) or LGBMRegressor (trained in memory)
            if isinstance(self.model, lgb.Booster):
                # For Booster, we can save directly
                self.model.save_model(path)
            else:
                # For LGBMRegressor, we need to access the booster_
                self.model.booster_.save_model(path)

            logger.info(f"LightGBM model '{self.model_name}' saved to {path}")

        except Exception as e:
            logger.error(f"Error saving LightGBM model '{self.model_name}' to {path}: {str(e)}", exc_info=True)
            raise

    def load_model(self, path: str) -> None:
        """Load the LightGBM model from the specified full path. Sets self.model."""
        try:
            if not os.path.exists(path):
                raise FileNotFoundError(f"LightGBM model file not found: {path}")

            # For LightGBM, we'll use the Booster directly instead of trying to set it on LGBMRegressor
            # This is a workaround for the 'can't set attribute booster_' error
            self.model = lgb.Booster(model_file=path)

            logger.info(f"LightGBM model '{self.model_name}' loaded from {path}")

        except Exception as e:
            logger.error(f"Error loading LightGBM model '{self.model_name}' from {path}: {str(e)}", exc_info=True)
            raise

    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """
        Get feature importance scores.

        Returns:
            Optional[Dict[str, float]]: Dictionary mapping feature names to importance scores, or None.
        """
        try:
            if self.model is None:
                logger.warning(f"Model '{self.model_name}' not trained yet.")
                return None

            # Get feature importance based on model type
            if isinstance(self.model, lgb.Booster):
                # For Booster, use feature_importance method
                importance_scores = self.model.feature_importance()
            elif hasattr(self.model, 'feature_importances_'):
                # For LGBMRegressor, use feature_importances_ attribute
                importance_scores = self.model.feature_importances_
            else:
                logger.warning(f"Model '{self.model_name}' doesn't have feature importances.")
                return None

            feature_names = self.config.get('FEATURE_COLUMNS')

            if feature_names is None:
                logger.warning("FEATURE_COLUMNS not found in config. Cannot map importance scores.")
                return {f"feature_{i}": score for i, score in enumerate(importance_scores)}
            elif len(feature_names) != len(importance_scores):
                logger.warning(f"Mismatch between number of features in config ({len(feature_names)}) and importance scores ({len(importance_scores)}). Returning raw scores.")
                return {f"feature_{i}": score for i, score in enumerate(importance_scores)}
            else:
                return dict(zip(feature_names, importance_scores))

        except Exception as e:
            logger.error(f"Error getting feature importance for '{self.model_name}': {str(e)}", exc_info=True)
            raise # Or return None

    def get_model_params(self) -> Dict:
        """Get current model parameters."""
        try:
            if self.model is None:
                logger.warning(f"Model '{self.model_name}' not built yet. Building to get default parameters.")
                self.build()
                if self.model is None: raise ValueError("Model cannot be built to get parameters.")

            # Get parameters based on model type
            if isinstance(self.model, lgb.Booster):
                # For Booster, return parameters from config
                return {
                    'num_leaves': self.config.get('num_leaves', 31),
                    'learning_rate': self.config.get('learning_rate', 0.05),
                    'n_estimators': self.config.get('n_estimators', 100),
                    'min_child_samples': self.config.get('min_child_samples', 20),
                    'subsample': self.config.get('subsample', 1.0),
                    'colsample_bytree': self.config.get('colsample_bytree', 1.0),
                }
            else:
                # For LGBMRegressor, use get_params method
                return self.model.get_params()

        except Exception as e:
            logger.error(f"Error getting model parameters for '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def set_model_params(self, params: Dict) -> None:
        """Set model parameters."""
        try:
            if self.model is None:
                logger.warning(f"Model '{self.model_name}' not built yet. Building before setting parameters.")
                self.build()
                if self.model is None: raise ValueError("Model cannot be built to set parameters.")

            # Set parameters based on model type
            if isinstance(self.model, lgb.Booster):
                # For Booster, we can't set parameters after loading
                logger.warning(f"Cannot set parameters for loaded Booster model '{self.model_name}'. Parameters are fixed.")
                return
            else:
                # For LGBMRegressor, use set_params method
                self.model.set_params(**params)
                logger.info(f"Successfully updated model parameters for '{self.model_name}'. Current params: {self.model.get_params()}")

        except Exception as e:
            logger.error(f"Error setting model parameters for '{self.model_name}': {str(e)}", exc_info=True)
            raise

    def get_feature_importance_type(self, importance_type: str = 'gain') -> Dict[str, float]:
        """
        Get feature importance scores by type.

        Args:
            importance_type: Type of importance ('gain' or 'split')

        Returns:
            Dict[str, float]: Dictionary mapping feature names to importance scores
        """
        try:
            if self.model is None:
                raise ValueError("Model not trained yet")

            # Get feature importance based on model type
            if isinstance(self.model, lgb.Booster):
                # For Booster, use feature_importance method with importance_type
                importance = self.model.feature_importance(importance_type=importance_type)
            else:
                # For LGBMRegressor, we can only get feature_importances_
                logger.warning(f"LGBMRegressor doesn't support importance_type. Using default importance.")
                importance = self.model.feature_importances_

            feature_names = self.config.get('FEATURE_COLUMNS')

            if feature_names is None:
                logger.warning("FEATURE_COLUMNS not found in config. Cannot map importance scores.")
                return {f"feature_{i}": score for i, score in enumerate(importance)}
            elif len(feature_names) != len(importance):
                logger.warning(f"Mismatch between number of features in config ({len(feature_names)}) and importance scores ({len(importance)}). Returning raw scores.")
                return {f"feature_{i}": score for i, score in enumerate(importance)}
            else:
                return dict(zip(feature_names, importance))

        except Exception as e:
            logger.error(f"Error getting feature importance by type: {str(e)}")
            raise