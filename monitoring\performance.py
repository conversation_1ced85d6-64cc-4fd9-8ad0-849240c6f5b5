"""
Performance monitoring module for the trading bot.
Tracks and analyzes trading performance metrics.
"""
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from pathlib import Path
from typing import Dict
import matplotlib.pyplot as plt
import seaborn as sns
import json
from config import config_manager

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    def __init__(self, terminal_id: int):
        """
        Initialize the performance monitor.

        Args:
            terminal_id: ID of the MT5 terminal
        """
        self.terminal_id = terminal_id

        # Get configuration
        self.monitoring_config = config_manager.get_monitoring_config()

        # Create output directory
        output_dir = getattr(self.monitoring_config, 'output_dir', 'monitoring')
        self.output_dir = Path(output_dir) / f"terminal_{terminal_id}"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.metrics_history = []
        self.trade_history = []
        self.model_performance = {}

    def update_metrics(self, metrics: Dict) -> None:
        """
        Update performance metrics.

        Args:
            metrics: Dictionary containing performance metrics
        """
        try:
            metrics['timestamp'] = datetime.now()
            self.metrics_history.append(metrics)

            # Keep only last 1000 metrics
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]

            # Save metrics to file
            self._save_metrics()

        except Exception as e:
            logger.error(f"Error updating metrics: {str(e)}")

    def update_trade_history(self, trade: Dict) -> None:
        """
        Update trade history.

        Args:
            trade: Dictionary containing trade details
        """
        try:
            self.trade_history.append(trade)

            # Keep only last 1000 trades
            if len(self.trade_history) > 1000:
                self.trade_history = self.trade_history[-1000:]

            # Save trade history to file
            self._save_trade_history()

        except Exception as e:
            logger.error(f"Error updating trade history: {str(e)}")

    def update_model_performance(self, model_name: str, metrics: Dict) -> None:
        """
        Update model performance metrics.

        Args:
            model_name: Name of the model
            metrics: Dictionary containing model performance metrics
        """
        try:
            if model_name not in self.model_performance:
                self.model_performance[model_name] = []

            metrics['timestamp'] = datetime.now()
            self.model_performance[model_name].append(metrics)

            # Keep only last 1000 metrics per model
            if len(self.model_performance[model_name]) > 1000:
                self.model_performance[model_name] = self.model_performance[model_name][-1000:]

            # Save model performance to file
            self._save_model_performance()

        except Exception as e:
            logger.error(f"Error updating model performance: {str(e)}")

    def generate_performance_report(self) -> Dict:
        """
        Generate comprehensive performance report.

        Returns:
            Dict: Performance report containing various metrics
        """
        try:
            if not self.trade_history:
                return {}

            # Convert trade history to DataFrame
            df = pd.DataFrame(self.trade_history)

            # Calculate basic metrics
            total_trades = len(df)
            winning_trades = len(df[df['profit'] > 0])
            losing_trades = len(df[df['profit'] < 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Calculate profit metrics
            total_profit = df['profit'].sum()
            avg_profit = df['profit'].mean()
            max_profit = df['profit'].max()
            max_loss = df['profit'].min()

            # Calculate risk metrics
            profits = df['profit'].values
            cumulative_profits = np.cumsum(profits)
            running_max = np.maximum.accumulate(cumulative_profits)
            drawdowns = running_max - cumulative_profits
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0

            # Calculate Sharpe ratio (assuming risk-free rate of 0)
            returns = df['profit'].pct_change().dropna()
            sharpe_ratio = np.sqrt(252) * returns.mean() / returns.std() if len(returns) > 0 else 0

            # Calculate model performance
            model_metrics = {}
            for model_name, metrics in self.model_performance.items():
                if metrics:
                    latest = metrics[-1]
                    model_metrics[model_name] = {
                        'total_profit': latest.get('total_profit', 0),
                        'win_rate': latest.get('win_rate', 0),
                        'avg_profit': latest.get('avg_profit', 0),
                        'max_drawdown': latest.get('max_drawdown', 0)
                    }

            report = {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_profit': total_profit,
                'avg_profit': avg_profit,
                'max_profit': max_profit,
                'max_loss': max_loss,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'model_performance': model_metrics,
                'timestamp': datetime.now()
            }

            # Save report to file
            self._save_report(report)

            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {str(e)}")
            return {}

    def plot_performance_charts(self) -> None:
        """Generate and save performance visualization charts."""
        try:
            if not self.trade_history:
                return

            # Convert trade history to DataFrame
            df = pd.DataFrame(self.trade_history)

            # Set style
            plt.style.use('seaborn')

            # Create figure with subplots
            fig = plt.figure(figsize=(15, 10))
            gs = fig.add_gridspec(3, 2)

            # 1. Cumulative Profit
            ax1 = fig.add_subplot(gs[0, :])
            cumulative_profit = df['profit'].cumsum()
            ax1.plot(df['timestamp'], cumulative_profit)
            ax1.set_title('Cumulative Profit')
            ax1.set_xlabel('Date')
            ax1.set_ylabel('Profit')

            # 2. Drawdown
            ax2 = fig.add_subplot(gs[1, :])
            profits = df['profit'].values
            cumulative = np.cumsum(profits)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = running_max - cumulative
            ax2.plot(df['timestamp'], drawdown)
            ax2.set_title('Drawdown')
            ax2.set_xlabel('Date')
            ax2.set_ylabel('Drawdown')

            # 3. Profit Distribution
            ax3 = fig.add_subplot(gs[2, 0])
            sns.histplot(data=df, x='profit', bins=50, ax=ax3)
            ax3.set_title('Profit Distribution')
            ax3.set_xlabel('Profit')
            ax3.set_ylabel('Count')

            # 4. Win Rate Over Time
            ax4 = fig.add_subplot(gs[2, 1])
            window_size = 20
            rolling_win_rate = df['profit'].rolling(window=window_size).apply(
                lambda x: (x > 0).mean()
            )
            ax4.plot(df['timestamp'], rolling_win_rate)
            ax4.set_title(f'Rolling Win Rate (Window={window_size})')
            ax4.set_xlabel('Date')
            ax4.set_ylabel('Win Rate')

            # Adjust layout and save
            plt.tight_layout()
            plt.savefig(self.output_dir / 'performance_charts.png')
            plt.close()

        except Exception as e:
            logger.error(f"Error generating performance charts: {str(e)}")

    def plot_model_performance(self) -> None:
        """Generate and save model performance comparison charts."""
        try:
            if not self.model_performance:
                return

            # Set style
            plt.style.use('seaborn')

            # Create figure with subplots
            fig = plt.figure(figsize=(15, 10))
            gs = fig.add_gridspec(2, 2)

            # 1. Total Profit by Model
            ax1 = fig.add_subplot(gs[0, 0])
            model_profits = {
                model: metrics[-1]['total_profit']
                for model, metrics in self.model_performance.items()
                if metrics
            }
            ax1.bar(model_profits.keys(), model_profits.values())
            ax1.set_title('Total Profit by Model')
            ax1.set_xlabel('Model')
            ax1.set_ylabel('Total Profit')

            # 2. Win Rate by Model
            ax2 = fig.add_subplot(gs[0, 1])
            model_win_rates = {
                model: metrics[-1]['win_rate']
                for model, metrics in self.model_performance.items()
                if metrics
            }
            ax2.bar(model_win_rates.keys(), model_win_rates.values())
            ax2.set_title('Win Rate by Model')
            ax2.set_xlabel('Model')
            ax2.set_ylabel('Win Rate')

            # 3. Average Profit by Model
            ax3 = fig.add_subplot(gs[1, 0])
            model_avg_profits = {
                model: metrics[-1]['avg_profit']
                for model, metrics in self.model_performance.items()
                if metrics
            }
            ax3.bar(model_avg_profits.keys(), model_avg_profits.values())
            ax3.set_title('Average Profit by Model')
            ax3.set_xlabel('Model')
            ax3.set_ylabel('Average Profit')

            # 4. Maximum Drawdown by Model
            ax4 = fig.add_subplot(gs[1, 1])
            model_drawdowns = {
                model: metrics[-1]['max_drawdown']
                for model, metrics in self.model_performance.items()
                if metrics
            }
            ax4.bar(model_drawdowns.keys(), model_drawdowns.values())
            ax4.set_title('Maximum Drawdown by Model')
            ax4.set_xlabel('Model')
            ax4.set_ylabel('Maximum Drawdown')

            # Adjust layout and save
            plt.tight_layout()
            plt.savefig(self.output_dir / 'model_performance_charts.png')
            plt.close()

        except Exception as e:
            logger.error(f"Error generating model performance charts: {str(e)}")

    def _save_metrics(self) -> None:
        """Save metrics history to file."""
        try:
            metrics_file = self.output_dir / 'metrics_history.json'
            with open(metrics_file, 'w') as f:
                json.dump(
                    [{
                        **m,
                        'timestamp': m['timestamp'].isoformat()
                    } for m in self.metrics_history],
                    f,
                    indent=4
                )
        except Exception as e:
            logger.error(f"Error saving metrics: {str(e)}")

    def _save_trade_history(self) -> None:
        """Save trade history to file."""
        try:
            trades_file = self.output_dir / 'trade_history.json'
            with open(trades_file, 'w') as f:
                json.dump(
                    [{
                        **t,
                        'timestamp': t['timestamp'].isoformat()
                    } for t in self.trade_history],
                    f,
                    indent=4
                )
        except Exception as e:
            logger.error(f"Error saving trade history: {str(e)}")

    def _save_model_performance(self) -> None:
        """Save model performance metrics to file."""
        try:
            performance_file = self.output_dir / 'model_performance.json'
            with open(performance_file, 'w') as f:
                json.dump(
                    {
                        model: [{
                            **m,
                            'timestamp': m['timestamp'].isoformat()
                        } for m in metrics]
                        for model, metrics in self.model_performance.items()
                    },
                    f,
                    indent=4
                )
        except Exception as e:
            logger.error(f"Error saving model performance: {str(e)}")

    def _save_report(self, report: Dict) -> None:
        """Save performance report to file."""
        try:
            report_file = self.output_dir / 'performance_report.json'
            with open(report_file, 'w') as f:
                json.dump(
                    {
                        **report,
                        'timestamp': report['timestamp'].isoformat()
                    },
                    f,
                    indent=4
                )
        except Exception as e:
            logger.error(f"Error saving performance report: {str(e)}")

    def get_metrics(self) -> Dict:
        """
        Get the latest performance metrics.

        Returns:
            Dict: Latest performance metrics
        """
        try:
            if not self.metrics_history:
                return {}
            return self.metrics_history[-1]
        except Exception as e:
            logger.error(f"Error getting metrics: {str(e)}")
            return {}