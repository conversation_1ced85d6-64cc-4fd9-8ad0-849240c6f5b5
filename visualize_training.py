#!/usr/bin/env python3
"""
Training Visualization Script
============================

This script generates comprehensive visualizations of model training progress and results.
It can monitor training in real-time or generate static visualizations from log files.

Usage:
    # Generate all visualizations
    python visualize_training.py --log_file model_training.log

    # Monitor training in real-time
    python visualize_training.py --log_file model_training.log --monitor --interval 10

    # Visualize specific models
    python visualize_training.py --log_file model_training.log --models lstm gru xgboost
"""

import argparse
import json
import logging
import re
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingVisualizer:
    """Visualizes model training progress and results."""
    
    def __init__(self, output_dir: str = "visualizations"):
        """Initialize the visualizer."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Training data storage
        self.training_data = {}
        self.model_metrics = {}
        
        logger.info(f"Training visualizer initialized. Output directory: {self.output_dir}")
    
    def parse_log_file(self, log_file: str, models: Optional[List[str]] = None) -> Dict:
        """Parse training log file to extract metrics."""
        try:
            log_path = Path(log_file)
            if not log_path.exists():
                logger.error(f"Log file not found: {log_file}")
                return {}
            
            training_data = {}
            
            with open(log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse training completion messages
            completion_pattern = r'Training completed for (\w+) in ([\d.]+) seconds'
            completions = re.findall(completion_pattern, content)
            
            # Parse performance metrics
            metrics_pattern = r'(\w+):\s+Duration: ([\d.]+)s\s+RMSE: ([\d.]+)\s+MAE: ([\d.]+)\s+R²: ([-\d.]+)\s+Dir\. Acc: ([\d.]+)'
            metrics = re.findall(metrics_pattern, content)
            
            for model, duration, rmse, mae, r2, dir_acc in metrics:
                if models and model not in models:
                    continue
                    
                training_data[model] = {
                    'duration': float(duration),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2),
                    'directional_accuracy': float(dir_acc),
                    'timestamp': datetime.now().isoformat()
                }
            
            logger.info(f"Parsed training data for {len(training_data)} models")
            return training_data
            
        except Exception as e:
            logger.error(f"Error parsing log file: {str(e)}")
            return {}
    
    def generate_training_progress_chart(self, data: Dict) -> None:
        """Generate training progress visualization."""
        try:
            if not data:
                logger.warning("No training data available for progress chart")
                return
            
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Training Duration', 'RMSE Comparison', 'R² Score', 'Directional Accuracy'),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            models = list(data.keys())
            durations = [data[m]['duration'] for m in models]
            rmse_values = [data[m]['rmse'] for m in models]
            r2_values = [data[m]['r2'] for m in models]
            dir_acc_values = [data[m]['directional_accuracy'] for m in models]
            
            # Training Duration
            fig.add_trace(
                go.Bar(x=models, y=durations, name='Duration (s)', marker_color='lightblue'),
                row=1, col=1
            )
            
            # RMSE Comparison
            fig.add_trace(
                go.Bar(x=models, y=rmse_values, name='RMSE', marker_color='lightcoral'),
                row=1, col=2
            )
            
            # R² Score
            fig.add_trace(
                go.Bar(x=models, y=r2_values, name='R² Score', marker_color='lightgreen'),
                row=2, col=1
            )
            
            # Directional Accuracy
            fig.add_trace(
                go.Bar(x=models, y=dir_acc_values, name='Dir. Accuracy', marker_color='gold'),
                row=2, col=2
            )
            
            # Update layout
            fig.update_layout(
                title_text="Model Training Performance Comparison",
                showlegend=False,
                height=800
            )
            
            # Save interactive HTML
            html_path = self.output_dir / 'training_progress.html'
            fig.write_html(str(html_path))
            
            # Save static PNG
            png_path = self.output_dir / 'training_progress.png'
            fig.write_image(str(png_path))
            
            logger.info(f"Training progress charts saved to {html_path} and {png_path}")
            
        except Exception as e:
            logger.error(f"Error generating training progress chart: {str(e)}")
    
    def generate_model_comparison_chart(self, data: Dict) -> None:
        """Generate model comparison visualization."""
        try:
            if not data:
                logger.warning("No training data available for model comparison")
                return
            
            # Create comparison chart
            fig = go.Figure()
            
            models = list(data.keys())
            rmse_values = [data[m]['rmse'] for m in models]
            
            fig.add_trace(go.Bar(
                x=models,
                y=rmse_values,
                text=[f"{val:.2f}" for val in rmse_values],
                textposition='auto',
                marker_color='steelblue'
            ))
            
            fig.update_layout(
                title="Model Performance Comparison (RMSE)",
                xaxis_title="Model",
                yaxis_title="RMSE",
                template="plotly_white"
            )
            
            # Save files
            html_path = self.output_dir / 'model_comparison.html'
            png_path = self.output_dir / 'model_comparison.png'
            
            fig.write_html(str(html_path))
            fig.write_image(str(png_path))
            
            logger.info(f"Model comparison charts saved to {html_path} and {png_path}")
            
        except Exception as e:
            logger.error(f"Error generating model comparison chart: {str(e)}")
    
    def generate_training_time_comparison(self, data: Dict) -> None:
        """Generate training time comparison visualization."""
        try:
            if not data:
                logger.warning("No training data available for time comparison")
                return
            
            fig = go.Figure()
            
            models = list(data.keys())
            durations = [data[m]['duration'] for m in models]
            
            fig.add_trace(go.Bar(
                x=models,
                y=durations,
                text=[f"{val:.1f}s" for val in durations],
                textposition='auto',
                marker_color='orange'
            ))
            
            fig.update_layout(
                title="Training Time Comparison",
                xaxis_title="Model",
                yaxis_title="Training Time (seconds)",
                template="plotly_white"
            )
            
            # Save files
            html_path = self.output_dir / 'training_time_comparison.html'
            png_path = self.output_dir / 'training_time_comparison.png'
            
            fig.write_html(str(html_path))
            fig.write_image(str(png_path))
            
            logger.info(f"Training time comparison charts saved to {html_path} and {png_path}")
            
        except Exception as e:
            logger.error(f"Error generating training time comparison: {str(e)}")
    
    def monitor_training(self, log_file: str, interval: int = 10, models: Optional[List[str]] = None) -> None:
        """Monitor training progress in real-time."""
        logger.info(f"Starting real-time monitoring of {log_file} (interval: {interval}s)")
        
        try:
            while True:
                # Parse latest data
                data = self.parse_log_file(log_file, models)
                
                if data:
                    # Generate updated visualizations
                    self.generate_training_progress_chart(data)
                    self.generate_model_comparison_chart(data)
                    self.generate_training_time_comparison(data)
                    
                    logger.info(f"Updated visualizations for {len(data)} models")
                else:
                    logger.info("No training data found yet, waiting...")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Error during monitoring: {str(e)}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Visualize model training progress")
    parser.add_argument('--log_file', default='model_training.log', help='Training log file path')
    parser.add_argument('--output_dir', default='visualizations', help='Output directory for visualizations')
    parser.add_argument('--models', nargs='*', help='Specific models to visualize')
    parser.add_argument('--monitor', action='store_true', help='Monitor training in real-time')
    parser.add_argument('--interval', type=int, default=10, help='Update interval for monitoring (seconds)')
    
    args = parser.parse_args()
    
    # Initialize visualizer
    visualizer = TrainingVisualizer(args.output_dir)
    
    if args.monitor:
        # Real-time monitoring
        visualizer.monitor_training(args.log_file, args.interval, args.models)
    else:
        # Generate static visualizations
        data = visualizer.parse_log_file(args.log_file, args.models)
        
        if data:
            visualizer.generate_training_progress_chart(data)
            visualizer.generate_model_comparison_chart(data)
            visualizer.generate_training_time_comparison(data)
            logger.info("Visualization generation completed")
        else:
            logger.error("No training data found in log file")

if __name__ == "__main__":
    main()
