import pytest
import MetaTrader5 as mt5
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

# Try to import from credentials first, fall back to test config if not available
try:
    from config.credentials import MT5_TERMINALS as mt5_configs
except ImportError:
    from tests.test_config import mt5_configs
import logging
from typing import Dict, Any
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test configuration
TEST_SYMBOL = "BTCUSD.a"
TEST_VOLUME = 0.01  # Small volume for testing
TEST_SLIPPAGE = 3  # Maximum allowed slippage in points
TEST_DEVIATION = 20  # Maximum allowed deviation in points

@pytest.fixture(scope="session")
def mt5_connection():
    """Fixture to initialize and maintain MT5 connection throughout the test session"""
    terminal_id = 1  # Use terminal 1 where algo trading is enabled
    config = mt5_configs[terminal_id]

    logger.info(f"\nTesting terminal {terminal_id}...")
    logger.info(f"Path: {config['path']}")
    logger.info(f"Login: {config['login']}")
    logger.info(f"Server: {config['server']}")

    # Initialize MT5 with minimal configuration
    if not mt5.initialize(path=config['path']):
        error = mt5.last_error()
        logger.error(f"Failed to initialize. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Failed to initialize MT5")

    # Give MT5 a moment to stabilize
    time.sleep(2)

    # Get terminal info
    terminal_info = mt5.terminal_info()
    if terminal_info is not None:
        logger.info("\nTerminal Info:")
        logger.info(f"Connected: {terminal_info.connected}")
        logger.info(f"Terminal Name: {terminal_info.name}")
        logger.info(f"Terminal Path: {terminal_info.path}")
        logger.info(f"Data Path: {terminal_info.data_path}")
        logger.info(f"Terminal Build: {terminal_info.build}")
    else:
        logger.error("Failed to get terminal info")
        pytest.fail("Failed to get terminal info")

    # Get account info
    account_info = mt5.account_info()
    if account_info is None:
        error = mt5.last_error()
        logger.error(f"Failed to get account info. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Failed to get account info")

    logger.info(f"\nAccount Info:")
    logger.info(f"Login: {account_info.login}")
    logger.info(f"Server: {account_info.server}")
    logger.info(f"Balance: {account_info.balance}")
    logger.info(f"Equity: {account_info.equity}")
    logger.info(f"Margin: {account_info.margin}")
    logger.info(f"Free Margin: {account_info.margin_free}")
    logger.info(f"Leverage: {account_info.leverage}")

    # Select symbol
    logger.info(f"\nSelecting symbol {TEST_SYMBOL}...")
    if not mt5.symbol_select(TEST_SYMBOL, True):
        error = mt5.last_error()
        logger.error(f"Failed to select symbol. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail(f"Failed to select symbol {TEST_SYMBOL}")

    yield terminal_id

    # Cleanup - close any open positions
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    if positions is not None:
        for position in positions:
            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                "position": position.ticket,
                "price": mt5.symbol_info_tick(position.symbol).bid if position.type == mt5.POSITION_TYPE_BUY else mt5.symbol_info_tick(position.symbol).ask,
                "deviation": TEST_DEVIATION,
                "magic": 0,
                "comment": "Test cleanup",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            mt5.order_send(close_request)

    # Only shutdown MT5 once all tests are complete
    mt5.shutdown()

def test_market_order_placement(mt5_connection):
    """Test placing market orders"""
    terminal_id = mt5_connection

    # Get current symbol info
    symbol_info = mt5.symbol_info(TEST_SYMBOL)
    assert symbol_info is not None, f"Failed to get symbol info for {TEST_SYMBOL}"

    logger.info(f"\nSymbol Info:")
    logger.info(f"Bid: {symbol_info.bid}")
    logger.info(f"Ask: {symbol_info.ask}")
    logger.info(f"Spread: {symbol_info.spread} points")
    logger.info(f"Trade Mode: {symbol_info.trade_mode}")

    # Prepare buy order
    buy_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": TEST_SYMBOL,
        "volume": TEST_VOLUME,
        "type": mt5.ORDER_TYPE_BUY,
        "price": symbol_info.ask,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test buy order",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    # Send buy order
    logger.info("\nAttempting to place test order...")
    result = mt5.order_send(buy_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Order failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Order failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Order failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Order failed")

    logger.info("\nTest order placed successfully")
    logger.info(f"Order ticket: {result.order}")
    logger.info(f"Execution price: {result.price}")

    # Wait for order to be processed
    time.sleep(2)

    # Verify position
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    assert positions is not None and len(positions) > 0, "No positions found after buy order"

    # Close position
    logger.info("\nAttempting to close test order...")
    position = positions[0]
    close_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": position.symbol,
        "volume": position.volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": position.ticket,
        "price": mt5.symbol_info_tick(position.symbol).bid,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test close position",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(close_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Close order failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Close order failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Failed to close test order. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Close order failed")

    logger.info("\nTest order closed successfully")

def test_pending_orders(mt5_connection):
    """Test placing and managing pending orders"""
    terminal_id = mt5_connection

    # Get current symbol info
    symbol_info = mt5.symbol_info(TEST_SYMBOL)
    assert symbol_info is not None, f"Failed to get symbol info for {TEST_SYMBOL}"

    # Calculate prices for pending orders
    current_price = symbol_info.ask
    buy_limit_price = current_price * 0.99  # 1% below current price
    sell_limit_price = current_price * 1.01  # 1% above current price

    # Place buy limit order
    buy_limit_request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": TEST_SYMBOL,
        "volume": TEST_VOLUME,
        "type": mt5.ORDER_TYPE_BUY_LIMIT,
        "price": buy_limit_price,
        "sl": buy_limit_price * 0.98,  # 2% below limit price
        "tp": buy_limit_price * 1.02,  # 2% above limit price
        "deviation": TEST_DEVIATION,
        "magic": 234000,  # Updated magic number
        "comment": "Test buy limit",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(buy_limit_request)
    assert result.retcode == mt5.TRADE_RETCODE_DONE, f"Buy limit order failed: {result.comment}"
    logger.info(f"Buy limit order placed successfully. Ticket: {result.order}")

    # Place sell limit order
    sell_limit_request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": TEST_SYMBOL,
        "volume": TEST_VOLUME,
        "type": mt5.ORDER_TYPE_SELL_LIMIT,
        "price": sell_limit_price,
        "sl": sell_limit_price * 1.02,  # 2% above limit price
        "tp": sell_limit_price * 0.98,  # 2% below limit price
        "deviation": TEST_DEVIATION,
        "magic": 234000,  # Updated magic number
        "comment": "Test sell limit",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(sell_limit_request)
    assert result.retcode == mt5.TRADE_RETCODE_DONE, f"Sell limit order failed: {result.comment}"
    logger.info(f"Sell limit order placed successfully. Ticket: {result.order}")

    # Verify pending orders
    orders = mt5.orders_get(symbol=TEST_SYMBOL)
    assert orders is not None and len(orders) == 2, "Pending orders not found"

    # Clean up pending orders
    for order in orders:
        delete_request = {
            "action": mt5.TRADE_ACTION_REMOVE,
            "order": order.ticket,
            "symbol": order.symbol,
            "volume": order.volume_initial,
            "type": order.type,
            "position": order.position_id,
            "price": order.price_open,
            "deviation": TEST_DEVIATION,
            "magic": 234000,  # Updated magic number
            "comment": "Test cleanup",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        result = mt5.order_send(delete_request)
        assert result.retcode == mt5.TRADE_RETCODE_DONE, f"Failed to delete order {order.ticket}: {result.comment}"
        logger.info(f"Deleted pending order {order.ticket}")

def test_order_modification(mt5_connection):
    """Test modifying existing orders"""
    terminal_id = mt5_connection

    # Get current symbol info
    symbol_info = mt5.symbol_info(TEST_SYMBOL)
    assert symbol_info is not None, f"Failed to get symbol info for {TEST_SYMBOL}"

    # Place initial pending order
    current_price = symbol_info.ask
    initial_price = current_price * 0.99  # 1% below current price
    initial_sl = initial_price * 0.98     # 2% below initial price
    initial_tp = initial_price * 1.02     # 2% above initial price

    order_request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": TEST_SYMBOL,
        "volume": TEST_VOLUME,
        "type": mt5.ORDER_TYPE_BUY_LIMIT,
        "price": initial_price,
        "sl": initial_sl,
        "tp": initial_tp,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test order modification",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    logger.info("\nPlacing initial order...")
    logger.info(f"Initial price: {initial_price}")
    logger.info(f"Initial SL: {initial_sl}")
    logger.info(f"Initial TP: {initial_tp}")

    result = mt5.order_send(order_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Initial order failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Initial order failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Initial order failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Initial order failed")

    order_ticket = result.order
    logger.info(f"Initial order placed successfully. Ticket: {order_ticket}")

    # Wait for order to be processed
    time.sleep(2)

    # Verify initial order
    orders = mt5.orders_get(symbol=TEST_SYMBOL)
    assert orders is not None and len(orders) == 1, "Initial order not found"
    initial_order = orders[0]
    logger.info(f"\nInitial order details:")
    logger.info(f"Price: {initial_order.price_open}")
    logger.info(f"SL: {initial_order.sl}")
    logger.info(f"TP: {initial_order.tp}")

    # Calculate new prices as percentages of current values
    new_price = initial_order.price_open * 0.995  # 0.5% lower
    new_sl = new_price * 0.99                     # 1% below new price
    new_tp = new_price * 1.01                     # 1% above new price

    logger.info(f"\nModifying order...")
    logger.info(f"New price: {new_price}")
    logger.info(f"New SL: {new_sl}")
    logger.info(f"New TP: {new_tp}")

    modify_request = {
        "action": mt5.TRADE_ACTION_MODIFY,
        "order": order_ticket,
        "price": new_price,
        "sl": new_sl,
        "tp": new_tp,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
    }

    # Try modification up to 3 times
    max_attempts = 3
    for attempt in range(max_attempts):
        result = mt5.order_send(modify_request)
        if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
            break
        logger.warning(f"Modification attempt {attempt + 1} failed, retrying...")
        time.sleep(1)

    if result is None:
        error = mt5.last_error()
        logger.error(f"Order modification failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Order modification failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Order modification failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Order modification failed")

    logger.info(f"Order {order_ticket} modified successfully")

    # Wait for modification to be processed
    time.sleep(2)

    # Verify modification
    orders = mt5.orders_get(symbol=TEST_SYMBOL)
    assert orders is not None and len(orders) == 1, "Modified order not found"
    modified_order = orders[0]

    logger.info(f"\nModified order details:")
    logger.info(f"Price: {modified_order.price_open}")
    logger.info(f"SL: {modified_order.sl}")
    logger.info(f"TP: {modified_order.tp}")

    # Calculate percentage differences
    price_diff_pct = abs((modified_order.price_open - new_price) / new_price * 100)
    sl_diff_pct = abs((modified_order.sl - new_sl) / new_sl * 100)
    tp_diff_pct = abs((modified_order.tp - new_tp) / new_tp * 100)

    # Allow 0.1% tolerance for price differences
    max_diff_pct = 0.1

    logger.info(f"\nVerification results:")
    logger.info(f"Price difference: {price_diff_pct:.3f}%")
    logger.info(f"SL difference: {sl_diff_pct:.3f}%")
    logger.info(f"TP difference: {tp_diff_pct:.3f}%")

    assert price_diff_pct < max_diff_pct, f"Order price not modified correctly. Difference: {price_diff_pct:.3f}%"
    assert sl_diff_pct < max_diff_pct, f"Stop loss not modified correctly. Difference: {sl_diff_pct:.3f}%"
    assert tp_diff_pct < max_diff_pct, f"Take profit not modified correctly. Difference: {tp_diff_pct:.3f}%"

    # Clean up
    delete_request = {
        "action": mt5.TRADE_ACTION_REMOVE,
        "order": order_ticket,
        "symbol": TEST_SYMBOL,
    }

    result = mt5.order_send(delete_request)
    if result is None or result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.warning(f"Failed to delete order {order_ticket}")
    else:
        logger.info(f"Order {order_ticket} deleted successfully")

def test_position_management(mt5_connection):
    """Test position management functionality"""
    terminal_id = mt5_connection

    # Get current symbol info
    symbol_info = mt5.symbol_info(TEST_SYMBOL)
    assert symbol_info is not None, f"Failed to get symbol info for {TEST_SYMBOL}"

    # Open position
    current_price = symbol_info.ask
    initial_sl = current_price * 0.99  # 1% below current price
    initial_tp = current_price * 1.01  # 1% above current price

    logger.info("\nOpening position...")
    logger.info(f"Current price: {current_price}")
    logger.info(f"Initial SL: {initial_sl}")
    logger.info(f"Initial TP: {initial_tp}")

    open_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": TEST_SYMBOL,
        "volume": TEST_VOLUME,
        "type": mt5.ORDER_TYPE_BUY,
        "price": current_price,
        "sl": initial_sl,
        "tp": initial_tp,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test position management",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(open_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Open position failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Open position failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Open position failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Open position failed")

    position_ticket = result.order
    logger.info(f"Position opened successfully. Ticket: {position_ticket}")

    # Wait for position to be opened
    time.sleep(2)

    # Verify position
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    assert positions is not None and len(positions) == 1, "Position not found"
    position = positions[0]

    logger.info(f"\nInitial position details:")
    logger.info(f"Open price: {position.price_open}")
    logger.info(f"Initial SL: {position.sl}")
    logger.info(f"Initial TP: {position.tp}")

    # Calculate new levels as percentages of current position price
    new_sl = position.price_open * 0.995  # 0.5% below position price
    new_tp = position.price_open * 1.005  # 0.5% above position price

    logger.info(f"\nModifying position...")
    logger.info(f"New SL: {new_sl}")
    logger.info(f"New TP: {new_tp}")

    modify_request = {
        "action": mt5.TRADE_ACTION_SLTP,
        "position": position.ticket,
        "sl": new_sl,
        "tp": new_tp,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
    }

    # Try modification up to 3 times
    max_attempts = 3
    for attempt in range(max_attempts):
        result = mt5.order_send(modify_request)
        if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
            break
        logger.warning(f"Modification attempt {attempt + 1} failed, retrying...")
        time.sleep(1)

    if result is None:
        error = mt5.last_error()
        logger.error(f"Position modification failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Position modification failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Position modification failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Position modification failed")

    logger.info(f"Position {position.ticket} modified successfully")

    # Wait for modification to be processed
    time.sleep(2)

    # Verify modification
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    assert positions is not None and len(positions) == 1, "Modified position not found"
    modified_position = positions[0]

    logger.info(f"\nModified position details:")
    logger.info(f"SL: {modified_position.sl}")
    logger.info(f"TP: {modified_position.tp}")

    # Calculate percentage differences
    sl_diff_pct = abs((modified_position.sl - new_sl) / new_sl * 100)
    tp_diff_pct = abs((modified_position.tp - new_tp) / new_tp * 100)

    # Allow 0.1% tolerance for price differences
    max_diff_pct = 0.1

    logger.info(f"\nVerification results:")
    logger.info(f"SL difference: {sl_diff_pct:.3f}%")
    logger.info(f"TP difference: {tp_diff_pct:.3f}%")

    assert sl_diff_pct < max_diff_pct, f"Stop loss not modified correctly. Difference: {sl_diff_pct:.3f}%"
    assert tp_diff_pct < max_diff_pct, f"Take profit not modified correctly. Difference: {tp_diff_pct:.3f}%"

    # Close position
    close_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": position.symbol,
        "volume": position.volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": position.ticket,
        "price": mt5.symbol_info_tick(position.symbol).bid,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test cleanup",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(close_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Close position failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Close position failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Close position failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Close position failed")

    logger.info(f"Position {position.ticket} closed successfully")

def test_stop_orders(mt5_connection):
    """Test placing and managing stop orders"""
    terminal_id = mt5_connection

    # Get current symbol info
    symbol_info = mt5.symbol_info(TEST_SYMBOL)
    assert symbol_info is not None, f"Failed to get symbol info for {TEST_SYMBOL}"

    # Calculate prices for stop orders
    current_price = symbol_info.ask
    buy_stop_price = current_price * 1.01   # 1% above current price
    sell_stop_price = current_price * 0.99  # 1% below current price

    logger.info("\nPlacing stop orders...")
    logger.info(f"Current price: {current_price}")
    logger.info(f"Buy stop price: {buy_stop_price}")
    logger.info(f"Sell stop price: {sell_stop_price}")

    # Place buy stop order
    buy_stop_request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": TEST_SYMBOL,
        "volume": TEST_VOLUME,
        "type": mt5.ORDER_TYPE_BUY_STOP,
        "price": buy_stop_price,
        "sl": buy_stop_price * 0.99,  # 1% below stop price
        "tp": buy_stop_price * 1.02,  # 2% above stop price
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test buy stop",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(buy_stop_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Buy stop order failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Buy stop order failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Buy stop order failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Buy stop order failed")

    buy_ticket = result.order
    logger.info(f"Buy stop order placed successfully. Ticket: {buy_ticket}")

    # Place sell stop order
    sell_stop_request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": TEST_SYMBOL,
        "volume": TEST_VOLUME,
        "type": mt5.ORDER_TYPE_SELL_STOP,
        "price": sell_stop_price,
        "sl": sell_stop_price * 1.01,  # 1% above stop price
        "tp": sell_stop_price * 0.98,  # 2% below stop price
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test sell stop",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(sell_stop_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Sell stop order failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Sell stop order failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Sell stop order failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Sell stop order failed")

    sell_ticket = result.order
    logger.info(f"Sell stop order placed successfully. Ticket: {sell_ticket}")

    # Wait for orders to be processed
    time.sleep(2)

    # Verify pending orders
    orders = mt5.orders_get(symbol=TEST_SYMBOL)
    assert orders is not None and len(orders) == 2, "Stop orders not found"

    # Verify order details
    for order in orders:
        logger.info(f"\nOrder {order.ticket} details:")
        logger.info(f"Type: {order.type}")
        logger.info(f"Price: {order.price_open}")
        logger.info(f"SL: {order.sl}")
        logger.info(f"TP: {order.tp}")

        # Verify order type
        assert order.type in [mt5.ORDER_TYPE_BUY_STOP, mt5.ORDER_TYPE_SELL_STOP], f"Incorrect order type: {order.type}"

        # Verify price levels with 0.1% tolerance
        if order.type == mt5.ORDER_TYPE_BUY_STOP:
            price_diff_pct = abs((order.price_open - buy_stop_price) / buy_stop_price * 100)
            sl_diff_pct = abs((order.sl - buy_stop_price * 0.99) / (buy_stop_price * 0.99) * 100)
            tp_diff_pct = abs((order.tp - buy_stop_price * 1.02) / (buy_stop_price * 1.02) * 100)
        else:
            price_diff_pct = abs((order.price_open - sell_stop_price) / sell_stop_price * 100)
            sl_diff_pct = abs((order.sl - sell_stop_price * 1.01) / (sell_stop_price * 1.01) * 100)
            tp_diff_pct = abs((order.tp - sell_stop_price * 0.98) / (sell_stop_price * 0.98) * 100)

        max_diff_pct = 0.1
        assert price_diff_pct < max_diff_pct, f"Order price difference too large: {price_diff_pct:.3f}%"
        assert sl_diff_pct < max_diff_pct, f"Stop loss difference too large: {sl_diff_pct:.3f}%"
        assert tp_diff_pct < max_diff_pct, f"Take profit difference too large: {tp_diff_pct:.3f}%"

    # Clean up - delete pending orders
    for order in orders:
        delete_request = {
            "action": mt5.TRADE_ACTION_REMOVE,
            "order": order.ticket,
            "symbol": TEST_SYMBOL,
        }

        result = mt5.order_send(delete_request)
        if result is None or result.retcode != mt5.TRADE_RETCODE_DONE:
            logger.warning(f"Failed to delete order {order.ticket}")
        else:
            logger.info(f"Order {order.ticket} deleted successfully")

def test_partial_position_closing(mt5_connection):
    """Test partial position closing functionality"""
    terminal_id = mt5_connection

    # Get current symbol info
    symbol_info = mt5.symbol_info(TEST_SYMBOL)
    assert symbol_info is not None, f"Failed to get symbol info for {TEST_SYMBOL}"

    # Open a larger position that we can split
    initial_volume = TEST_VOLUME * 3  # 3 times our test volume
    current_price = symbol_info.ask

    logger.info("\nOpening initial position...")
    logger.info(f"Volume: {initial_volume}")
    logger.info(f"Price: {current_price}")

    open_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": TEST_SYMBOL,
        "volume": initial_volume,
        "type": mt5.ORDER_TYPE_BUY,
        "price": current_price,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test partial close",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(open_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Open position failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Open position failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Open position failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Open position failed")

    position_ticket = result.order
    logger.info(f"Position opened successfully. Ticket: {position_ticket}")

    # Wait for position to be opened
    time.sleep(2)

    # Verify initial position
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    assert positions is not None and len(positions) == 1, "Initial position not found"
    position = positions[0]
    assert position.volume == initial_volume, f"Position volume mismatch: {position.volume} != {initial_volume}"

    logger.info(f"\nInitial position details:")
    logger.info(f"Volume: {position.volume}")
    logger.info(f"Open price: {position.price_open}")

    # Close first part (1/3)
    close_volume = initial_volume / 3
    logger.info(f"\nClosing first part ({close_volume})...")

    close_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": position.symbol,
        "volume": close_volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": position.ticket,
        "price": mt5.symbol_info_tick(position.symbol).bid,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test partial close 1",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(close_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Partial close failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Partial close failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Partial close failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Partial close failed")

    logger.info("First partial close successful")

    # Wait for partial close to be processed
    time.sleep(2)

    # Verify remaining position
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    assert positions is not None and len(positions) == 1, "Remaining position not found"
    remaining_position = positions[0]
    expected_volume = initial_volume - close_volume

    logger.info(f"\nRemaining position details:")
    logger.info(f"Volume: {remaining_position.volume}")
    logger.info(f"Expected volume: {expected_volume}")

    # Allow small volume difference due to floating point precision
    volume_diff = abs(remaining_position.volume - expected_volume)
    assert volume_diff < 0.0001, f"Volume difference too large: {volume_diff}"

    # Close second part (1/3)
    logger.info(f"\nClosing second part ({close_volume})...")

    close_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": remaining_position.symbol,
        "volume": close_volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": remaining_position.ticket,
        "price": mt5.symbol_info_tick(remaining_position.symbol).bid,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test partial close 2",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(close_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Partial close failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Partial close failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Partial close failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Partial close failed")

    logger.info("Second partial close successful")

    # Wait for partial close to be processed
    time.sleep(2)

    # Verify final position
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    assert positions is not None and len(positions) == 1, "Final position not found"
    final_position = positions[0]
    expected_volume = initial_volume - (close_volume * 2)

    logger.info(f"\nFinal position details:")
    logger.info(f"Volume: {final_position.volume}")
    logger.info(f"Expected volume: {expected_volume}")

    # Allow small volume difference due to floating point precision
    volume_diff = abs(final_position.volume - expected_volume)
    assert volume_diff < 0.0001, f"Volume difference too large: {volume_diff}"

    # Close final part
    logger.info(f"\nClosing final part ({final_position.volume})...")

    close_request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": final_position.symbol,
        "volume": final_position.volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": final_position.ticket,
        "price": mt5.symbol_info_tick(final_position.symbol).bid,
        "deviation": TEST_DEVIATION,
        "magic": 234000,
        "comment": "Test final close",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(close_request)
    if result is None:
        error = mt5.last_error()
        logger.error(f"Final close failed. Error code: {error[0]}, Description: {error[1]}")
        pytest.fail("Final close failed")

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Final close failed. Return code: {result.retcode}")
        if hasattr(result, 'comment'):
            logger.error(f"Comment: {result.comment}")
        pytest.fail("Final close failed")

    logger.info("Final close successful")

    # Verify all positions are closed
    time.sleep(2)
    positions = mt5.positions_get(symbol=TEST_SYMBOL)
    assert positions is None or len(positions) == 0, "Not all positions were closed"

def test_partial_position_closing_all_terminals():
    """Test partial position closing functionality across all terminals"""
    results = {}

    for terminal_id, config in mt5_configs.items():
        logger.info(f"\nTesting terminal {terminal_id}...")
        logger.info(f"Path: {config['path']}")
        logger.info(f"Login: {config['login']}")
        logger.info(f"Server: {config['server']}")

        try:
            # Initialize MT5 with minimal configuration
            if not mt5.initialize(path=config['path']):
                error = mt5.last_error()
                logger.error(f"Failed to initialize. Error code: {error[0]}, Description: {error[1]}")
                results[terminal_id] = False
                continue

            # Give MT5 a moment to stabilize
            time.sleep(2)

            # Get terminal info
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                logger.error("Failed to get terminal info")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            if not terminal_info.trade_allowed:
                logger.error(f"\nAutoTrading is disabled on terminal {terminal_id}!")
                logger.error("Please enable AutoTrading by:")
                logger.error("1. Opening the terminal")
                logger.error("2. Clicking the 'AutoTrading' button (it should turn green)")
                logger.error("3. Ensuring 'AutoTrading enabled' appears in the status bar")
                logger.error("4. Running the test again")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            # Get current symbol info
            symbol_info = mt5.symbol_info(TEST_SYMBOL)
            if symbol_info is None:
                logger.error(f"Failed to get symbol info for {TEST_SYMBOL}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            # Open a larger position that we can split
            initial_volume = TEST_VOLUME * 3  # 3 times our test volume
            current_price = symbol_info.ask

            logger.info("\nOpening initial position...")
            logger.info(f"Volume: {initial_volume}")
            logger.info(f"Price: {current_price}")

            open_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": TEST_SYMBOL,
                "volume": initial_volume,
                "type": mt5.ORDER_TYPE_BUY,
                "price": current_price,
                "deviation": TEST_DEVIATION,
                "magic": 234000,
                "comment": "Test partial close",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(open_request)
            if result is None:
                error = mt5.last_error()
                logger.error(f"Open position failed. Error code: {error[0]}, Description: {error[1]}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Open position failed. Return code: {result.retcode}")
                if hasattr(result, 'comment'):
                    logger.error(f"Comment: {result.comment}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            position_ticket = result.order
            logger.info(f"Position opened successfully. Ticket: {position_ticket}")

            # Wait for position to be opened
            time.sleep(2)

            # Verify initial position
            positions = mt5.positions_get(symbol=TEST_SYMBOL)
            if positions is None or len(positions) != 1:
                logger.error("Initial position not found")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            position = positions[0]
            if position.volume != initial_volume:
                logger.error(f"Position volume mismatch: {position.volume} != {initial_volume}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            logger.info(f"\nInitial position details:")
            logger.info(f"Volume: {position.volume}")
            logger.info(f"Open price: {position.price_open}")

            # Close first part (1/3)
            close_volume = initial_volume / 3
            logger.info(f"\nClosing first part ({close_volume})...")

            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": close_volume,
                "type": mt5.ORDER_TYPE_SELL,
                "position": position.ticket,
                "price": mt5.symbol_info_tick(position.symbol).bid,
                "deviation": TEST_DEVIATION,
                "magic": 234000,
                "comment": "Test partial close 1",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(close_request)
            if result is None:
                error = mt5.last_error()
                logger.error(f"Partial close failed. Error code: {error[0]}, Description: {error[1]}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Partial close failed. Return code: {result.retcode}")
                if hasattr(result, 'comment'):
                    logger.error(f"Comment: {result.comment}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            logger.info("First partial close successful")

            # Wait for partial close to be processed
            time.sleep(2)

            # Verify remaining position
            positions = mt5.positions_get(symbol=TEST_SYMBOL)
            if positions is None or len(positions) != 1:
                logger.error("Remaining position not found")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            remaining_position = positions[0]
            expected_volume = initial_volume - close_volume

            logger.info(f"\nRemaining position details:")
            logger.info(f"Volume: {remaining_position.volume}")
            logger.info(f"Expected volume: {expected_volume}")

            # Allow small volume difference due to floating point precision
            volume_diff = abs(remaining_position.volume - expected_volume)
            if volume_diff >= 0.0001:
                logger.error(f"Volume difference too large: {volume_diff}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            # Close second part (1/3)
            logger.info(f"\nClosing second part ({close_volume})...")

            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": remaining_position.symbol,
                "volume": close_volume,
                "type": mt5.ORDER_TYPE_SELL,
                "position": remaining_position.ticket,
                "price": mt5.symbol_info_tick(remaining_position.symbol).bid,
                "deviation": TEST_DEVIATION,
                "magic": 234000,
                "comment": "Test partial close 2",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(close_request)
            if result is None:
                error = mt5.last_error()
                logger.error(f"Partial close failed. Error code: {error[0]}, Description: {error[1]}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Partial close failed. Return code: {result.retcode}")
                if hasattr(result, 'comment'):
                    logger.error(f"Comment: {result.comment}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            logger.info("Second partial close successful")

            # Wait for partial close to be processed
            time.sleep(2)

            # Verify final position
            positions = mt5.positions_get(symbol=TEST_SYMBOL)
            if positions is None or len(positions) != 1:
                logger.error("Final position not found")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            final_position = positions[0]
            expected_volume = initial_volume - (close_volume * 2)

            logger.info(f"\nFinal position details:")
            logger.info(f"Volume: {final_position.volume}")
            logger.info(f"Expected volume: {expected_volume}")

            # Allow small volume difference due to floating point precision
            volume_diff = abs(final_position.volume - expected_volume)
            if volume_diff >= 0.0001:
                logger.error(f"Volume difference too large: {volume_diff}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            # Close final part
            logger.info(f"\nClosing final part ({final_position.volume})...")

            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": final_position.symbol,
                "volume": final_position.volume,
                "type": mt5.ORDER_TYPE_SELL,
                "position": final_position.ticket,
                "price": mt5.symbol_info_tick(final_position.symbol).bid,
                "deviation": TEST_DEVIATION,
                "magic": 234000,
                "comment": "Test final close",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(close_request)
            if result is None:
                error = mt5.last_error()
                logger.error(f"Final close failed. Error code: {error[0]}, Description: {error[1]}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Final close failed. Return code: {result.retcode}")
                if hasattr(result, 'comment'):
                    logger.error(f"Comment: {result.comment}")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            logger.info("Final close successful")

            # Verify all positions are closed
            time.sleep(2)
            positions = mt5.positions_get(symbol=TEST_SYMBOL)
            if positions is not None and len(positions) > 0:
                logger.error("Not all positions were closed")
                results[terminal_id] = False
                mt5.shutdown()
                continue

            results[terminal_id] = True
            logger.info(f"Terminal {terminal_id} passed all tests!")

        except Exception as e:
            logger.error(f"Error testing terminal {terminal_id}: {str(e)}")
            results[terminal_id] = False

        finally:
            # NOTE: We're calling mt5.shutdown() here because this is a test file
            # In production code, avoid using mt5.shutdown() as it disables Algo Trading
            # Instead, use the disconnect() method from MT5ConnectionManager
            mt5.shutdown()
            time.sleep(1)  # Wait for clean shutdown

    # Verify all terminals passed
    failed_terminals = [tid for tid, passed in results.items() if not passed]
    if failed_terminals:
        pytest.fail(f"Partial position closing failed on terminals: {failed_terminals}")
    else:
        logger.info("\nAll terminals passed the partial position closing test!")