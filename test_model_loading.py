#!/usr/bin/env python3
"""
Test script to verify model loading functionality without MT5 initialization.
This script tests the fixes for model path inconsistencies and scaler issues.
"""

import logging
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_model_loading():
    """Test model loading for all terminals and models."""
    try:
        # Import required modules
        from config.consolidated_config import ConfigurationManager
        from utils.enhanced_error_handler import EnhancedErrorHandler
        from utils.model_manager import ModelManager
        
        logger.info("=== TESTING MODEL LOADING FUNCTIONALITY ===")
        
        # Initialize configuration manager
        config_manager = ConfigurationManager()
        logger.info("Configuration manager initialized")
        
        # Initialize error handler
        error_handler = EnhancedErrorHandler()
        logger.info("Error handler initialized")
        
        # Test model loading for each terminal
        terminals = ["1", "2", "3", "4", "5"]
        timeframe = "M5"
        
        for terminal_id in terminals:
            logger.info(f"\n--- Testing Terminal {terminal_id} ---")
            
            try:
                # Create model manager for this terminal
                model_manager = ModelManager(
                    config_manager=config_manager,
                    error_handler=error_handler,
                    terminal_id=terminal_id,
                    timeframe=timeframe,
                    check_model_health=True
                )
                
                logger.info(f"Model manager created for terminal {terminal_id}")
                
                # Load models
                load_results = model_manager.load_all_models()
                logger.info(f"Models loaded for terminal {terminal_id}: {load_results}")

                # Get model health status
                health_status = model_manager.get_model_health_status()
                logger.info(f"Model health status for terminal {terminal_id}: {health_status}")

                # Count successful models
                successful_models = sum(1 for status in health_status.values() if status)
                total_models = len(health_status)
                logger.info(f"Terminal {terminal_id}: {successful_models}/{total_models} models healthy")

                # Show which models were loaded successfully
                successful_model_names = [name for name, status in load_results.items() if status]
                failed_model_names = [name for name, status in load_results.items() if not status]
                logger.info(f"Terminal {terminal_id} - Successful: {successful_model_names}")
                if failed_model_names:
                    logger.info(f"Terminal {terminal_id} - Failed: {failed_model_names}")
                
            except Exception as e:
                logger.error(f"Error testing terminal {terminal_id}: {str(e)}", exc_info=True)
        
        logger.info("\n=== MODEL LOADING TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error in test_model_loading: {str(e)}", exc_info=True)

def test_path_resolution():
    """Test path resolution functionality."""
    try:
        from config import get_model_path
        
        logger.info("\n=== TESTING PATH RESOLUTION ===")
        
        models = ["lstm", "gru", "transformer", "tft", "xgboost", "lightgbm", "arima"]
        terminals = ["1", "2", "3", "4", "5"]
        timeframe = "M5"
        
        for terminal_id in terminals:
            logger.info(f"\nTerminal {terminal_id} paths:")
            for model_name in models:
                try:
                    model_path = get_model_path(model_name, terminal_id, timeframe)
                    logger.info(f"  {model_name}: {model_path}")
                except Exception as e:
                    logger.error(f"  {model_name}: ERROR - {str(e)}")
        
        logger.info("\n=== PATH RESOLUTION TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error in test_path_resolution: {str(e)}", exc_info=True)

def check_model_files():
    """Check which model files actually exist."""
    try:
        logger.info("\n=== CHECKING MODEL FILE EXISTENCE ===")
        
        models_base = Path("models")
        
        for terminal_dir in models_base.glob("terminal_*"):
            if terminal_dir.is_dir():
                logger.info(f"\n{terminal_dir.name}:")
                
                timeframe_dir = terminal_dir / "M5"
                if timeframe_dir.exists():
                    for model_dir in timeframe_dir.glob("*_model"):
                        if model_dir.is_dir():
                            model_files = list(model_dir.glob("*"))
                            logger.info(f"  {model_dir.name}: {len(model_files)} files")
                            for file in model_files:
                                if file.is_file():
                                    logger.info(f"    - {file.name}")
                else:
                    logger.info(f"  No M5 directory found")
        
        logger.info("\n=== MODEL FILE CHECK COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error in check_model_files: {str(e)}", exc_info=True)

if __name__ == "__main__":
    logger.info("Starting model loading tests...")
    
    # Test path resolution first
    test_path_resolution()
    
    # Check which model files exist
    check_model_files()
    
    # Test model loading
    test_model_loading()
    
    logger.info("All tests completed.")
