"""
Trading strategy module for generating trading signals based on model predictions.
"""
import logging
from typing import Dict, Optional, Any
from datetime import datetime
from config import StrategyConfig

logger = logging.getLogger(__name__)

class TradingStrategy:
    def __init__(self, config: StrategyConfig, signal_generator=None, error_handler=None, allocation_factor=1.0):
        """
        Initialize the trading strategy.

        Args:
            config: Strategy configuration
            signal_generator: Optional signal generator instance
            error_handler: Optional error handler
            allocation_factor: Capital allocation factor for this strategy (0.0-1.0)
            **kwargs: Additional keyword arguments
        """
        self.config = config
        self.signal_generator = signal_generator
        self.error_handler = error_handler
        self.allocation_factor = allocation_factor
        self.trade_history = []  # Initialize trade history

        # Get model weights from signal generator if available
        if signal_generator and hasattr(signal_generator, 'model_manager') and hasattr(signal_generator.model_manager, 'get_model_weights'):
            try:
                self.model_weights = signal_generator.model_manager.get_model_weights()
                logger.info(f"Using model weights from signal generator: {self.model_weights}")
            except Exception as e:
                logger.warning(f"Error getting model weights from signal generator: {str(e)}")
                # Fallback to default weights
                self.model_weights = {
                    'lstm': 0.25,
                    'gru': 0.25,
                    'xgboost': 0.25,
                    'lightgbm': 0.25
                }
        else:
            # Default model weights
            self.model_weights = {
                'lstm': 0.25,
                'gru': 0.25,
                'xgboost': 0.25,
                'lightgbm': 0.25
            }

        # Strategy parameters
        self.min_confidence = getattr(config, 'min_confidence', 0.7)  # Increased for crypto
        self.max_spread = getattr(config, 'max_spread_pips', 50)  # Increased for BTCUSD.a
        self.stop_loss = getattr(config, 'stop_loss_pips', 200)  # Adjusted for BTCUSD.a volatility
        self.take_profit = getattr(config, 'take_profit_pips', 400)  # Adjusted for BTCUSD.a volatility

        # BTCUSD.a specific parameters
        self.volatility_threshold = getattr(config, 'volatility_threshold', 2.0)
        self.trend_threshold = getattr(config, 'trend_threshold', 0.7)
        self.position_sizing_factor = getattr(config, 'position_sizing_factor', 0.5) * self.allocation_factor
        self.volatility_lookback = 20  # Increased for crypto
        self.trend_lookback = 50  # Increased for crypto

        # Risk management
        self.max_daily_loss = getattr(config, 'max_daily_loss', 50.0) * self.allocation_factor
        self.max_daily_trades = int(getattr(config, 'max_daily_trades', 5) * self.allocation_factor)
        self.cooldown_period = getattr(config, 'cooldown_period', 600)

        logger.info(f"Trading strategy initialized with allocation factor {self.allocation_factor:.2f}, "
                   f"position sizing factor {self.position_sizing_factor:.2f}, "
                   f"max daily loss {self.max_daily_loss:.2f}, "
                   f"max daily trades {self.max_daily_trades}")

    def update_model_weights(self, predictions: Dict[str, float], metrics: Dict) -> None:
        """
        Update model weights based on performance metrics.

        Args:
            predictions: Dictionary of model predictions
            metrics: Dictionary of performance metrics
        """
        try:
            if not predictions or not metrics:
                logger.warning("Invalid predictions or metrics for weight update")
                return

            # Calculate performance scores for each model
            performance_scores = {}
            total_score = 0.0

            for model_name, pred in predictions.items():
                if model_name not in self.model_weights:
                    continue

                # Calculate score based on prediction accuracy and metrics
                score = 0.0

                # Add points for correct predictions
                if metrics['total_trades'] > 0:
                    win_rate = metrics['winning_trades'] / metrics['total_trades']
                    if (pred > 0 and win_rate > 0.5) or (pred < 0 and win_rate < 0.5):
                        score += 1.0

                # Add points for profit contribution
                if metrics['total_profit'] != 0:
                    profit_contribution = abs(pred) * metrics['total_profit']
                    score += profit_contribution / abs(metrics['total_profit'])

                # Add points for risk-adjusted performance
                if metrics['sharpe_ratio'] > 0:
                    score += metrics['sharpe_ratio'] * 0.5

                performance_scores[model_name] = max(0.1, score)  # Ensure minimum weight
                total_score += performance_scores[model_name]

            if total_score > 0:
                # Normalize weights
                for model_name in self.model_weights:
                    if model_name in performance_scores:
                        self.model_weights[model_name] = performance_scores[model_name] / total_score
                    else:
                        self.model_weights[model_name] = 0.0

                logger.info(f"Updated model weights: {self.model_weights}")

        except Exception as e:
            logger.error(f"Error updating model weights: {str(e)}")

    def generate_trading_signal(self, predictions: Dict[str, float], metrics: Dict) -> Optional[Dict]:
        """
        Generate trading signal based on model predictions and metrics.

        Args:
            predictions: Dictionary of model predictions
            metrics: Dictionary of performance metrics

        Returns:
            Optional[Dict]: Trading signal or None if no valid signal
        """
        try:
            if not predictions or not metrics:
                logger.error("Invalid predictions or metrics for signal generation")
                return None

            # Calculate weighted prediction
            weighted_pred = 0.0
            total_weight = 0.0

            for model_name, pred in predictions.items():
                if model_name in self.model_weights:
                    weight = self.model_weights[model_name]
                    weighted_pred += pred * weight
                    total_weight += weight

            if total_weight == 0:
                logger.error("No valid model weights for signal generation")
                return None

            weighted_pred /= total_weight

            # Calculate confidence level
            confidence = abs(weighted_pred)

            # Check if confidence meets minimum threshold
            if confidence < self.min_confidence:
                logger.info(f"Confidence {confidence} below minimum threshold {self.min_confidence}")
                return {'action': 'hold', 'confidence': confidence}

            # Generate signal
            if weighted_pred > 0:
                action = 'buy'
            else:
                action = 'sell'

            # Calculate position size based on confidence and metrics
            position_size = self._calculate_position_size(confidence, metrics)

            return {
                'action': action,
                'confidence': confidence,
                'position_size': position_size,
                'stop_loss': self.stop_loss,
                'take_profit': self.take_profit
            }

        except Exception as e:
            logger.error(f"Error generating trading signal: {str(e)}")
            return None

    def _calculate_position_size(self, confidence: float, metrics: Dict) -> float:
        """
        Calculate position size based on confidence, metrics, and terminal allocation.
        Adjusted for BTCUSD.a volatility and risk.

        Args:
            confidence: Signal confidence
            metrics: Performance metrics

        Returns:
            float: Position size
        """
        try:
            # Base position size (more conservative for BTCUSD.a)
            base_size = getattr(self.config, 'lot_size', 0.01)

            # Adjust based on confidence
            confidence_multiplier = min(confidence / self.min_confidence, 1.5)

            # Adjust based on volatility
            volatility_multiplier = 1.0
            if metrics.get('volatility', 0) > self.volatility_threshold:
                volatility_multiplier = 0.5  # Reduce size in high volatility

            # Adjust based on performance
            performance_multiplier = 1.0
            if metrics.get('total_trades', 0) > 0:
                win_rate = metrics.get('winning_trades', 0) / metrics.get('total_trades', 1)
                if win_rate > 0.6:
                    performance_multiplier = 1.1
                elif win_rate < 0.4:
                    performance_multiplier = 0.7

            # Calculate final position size with allocation factor
            position_size = (
                base_size *
                confidence_multiplier *
                volatility_multiplier *
                performance_multiplier *
                self.position_sizing_factor  # Already includes allocation_factor
            )

            # Log the position size calculation
            logger.debug(f"Position size calculation: base={base_size}, confidence_mult={confidence_multiplier:.2f}, "
                        f"volatility_mult={volatility_multiplier:.2f}, performance_mult={performance_multiplier:.2f}, "
                        f"allocation_factor={self.allocation_factor:.2f}, position_sizing_factor={self.position_sizing_factor:.2f}")
            logger.debug(f"Final position size before limits: {position_size:.4f}")

            # Ensure position size is within BTCUSD.a limits
            min_size = 0.01  # Minimum lot size for BTCUSD.a
            max_size = 0.5 * self.allocation_factor  # Maximum lot size adjusted by allocation

            final_size = max(min_size, min(position_size, max_size))
            logger.info(f"Final position size: {final_size:.4f} (min={min_size}, max={max_size:.4f})")

            return final_size

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            # Return a safe default value
            safe_default = getattr(self.config, 'lot_size', 0.01) * self.allocation_factor
            logger.warning(f"Using safe default position size: {safe_default:.4f}")
            return safe_default

    def calculate_position_size(
        self,
        signal: Dict,
        account_balance: float,
        risk_per_trade: float = 0.02
    ) -> float:
        """
        Calculate position size based on signal confidence, risk parameters, and terminal allocation.

        Args:
            signal: Trading signal
            account_balance: Current account balance
            risk_per_trade: Risk per trade as fraction of account balance

        Returns:
            float: Position size in lots
        """
        try:
            if signal['action'] == 'hold':
                return 0.0

            # Apply terminal allocation to risk per trade
            adjusted_risk_per_trade = risk_per_trade * self.allocation_factor

            # Calculate base position size
            risk_amount = account_balance * adjusted_risk_per_trade

            # Adjust position size based on signal confidence
            base_lot_size = getattr(self.config, 'lot_size', 0.1)  # Use consistent parameter name
            adjusted_size = base_lot_size * signal['confidence'] * self.allocation_factor

            # Ensure position size doesn't exceed maximum allowed
            max_size = min(
                base_lot_size * self.allocation_factor,
                risk_amount / (signal.get('price', 1.0) * 100)  # Convert risk amount to lots
            )

            final_size = min(adjusted_size, max_size)

            # Log the calculation
            logger.debug(f"Position size calculation: account_balance={account_balance:.2f}, "
                        f"risk_per_trade={adjusted_risk_per_trade:.4f}, risk_amount={risk_amount:.2f}, "
                        f"base_lot_size={base_lot_size}, confidence={signal['confidence']:.2f}, "
                        f"allocation_factor={self.allocation_factor:.2f}")
            logger.info(f"Final position size: {final_size:.4f} (adjusted_size={adjusted_size:.4f}, max_size={max_size:.4f})")

            return final_size

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            # Return a safe default value
            safe_default = getattr(self.config, 'lot_size', 0.01) * self.allocation_factor
            logger.warning(f"Using safe default position size: {safe_default:.4f}")
            return safe_default

    def calculate_stop_loss(self, signal: Dict, current_price: float, volatility: float) -> float:
        """
        Calculate dynamic stop-loss level for BTCUSD.a.

        Args:
            signal: Trading signal
            current_price: Current market price
            volatility: Current market volatility

        Returns:
            float: Stop-loss price
        """
        try:
            # Base stop-loss distance based on volatility
            base_distance = volatility * 2.5  # Increased for crypto

            # Adjust distance based on signal confidence
            adjusted_distance = base_distance * (1 - signal['confidence'])

            # Add additional buffer for BTCUSD.a
            buffer = volatility * 0.5

            if signal['action'] == 'buy':
                stop_loss = current_price * (1 - (adjusted_distance + buffer))
            elif signal['action'] == 'sell':
                stop_loss = current_price * (1 + (adjusted_distance + buffer))
            else:
                stop_loss = current_price

            return stop_loss

        except Exception as e:
            logger.error(f"Error calculating stop-loss: {str(e)}")
            raise

    def calculate_take_profit(
        self,
        signal: Dict,
        current_price: float,
        stop_loss: float
    ) -> float:
        """
        Calculate dynamic take-profit level.

        Args:
            signal: Trading signal
            current_price: Current market price
            stop_loss: Stop-loss price
            volatility: Current market volatility

        Returns:
            float: Take-profit price
        """
        try:
            # Calculate risk-reward ratio based on volatility
            risk_reward_ratio = 2.0  # Base ratio

            # Adjust ratio based on signal confidence
            adjusted_ratio = risk_reward_ratio * (1 + signal['confidence'])

            if signal['action'] == 'buy':
                risk = current_price - stop_loss
                take_profit = current_price + (risk * adjusted_ratio)
            elif signal['action'] == 'sell':
                risk = stop_loss - current_price
                take_profit = current_price - (risk * adjusted_ratio)
            else:
                take_profit = current_price

            return take_profit

        except Exception as e:
            logger.error(f"Error calculating take-profit: {str(e)}")
            raise

    def decide_trade(self, signal) -> Optional[Any]:
        """
        Decide whether to execute a trade based on the signal.

        Args:
            signal: Trading signal (dict or object)

        Returns:
            Optional[Any]: Trade order or None if no trade should be executed
        """
        try:
            if not signal:
                logger.info("No trade: Signal is None")
                return None

            # Handle both dictionary and object formats
            if isinstance(signal, dict):
                action = signal.get('action', 'hold')
                confidence = signal.get('confidence', 0.0)
            else:
                # Object format
                if not hasattr(signal, 'action'):
                    logger.info("No trade: Signal has no action attribute")
                    return None
                action = signal.action
                confidence = getattr(signal, 'confidence', 0.0)

            # Check if it's a hold signal
            if action == 'hold':
                logger.info("No trade: Signal action is 'hold'")
                return None

            # Check confidence threshold
            if confidence < self.min_confidence:
                logger.info(f"No trade: Confidence {confidence:.4f} below threshold {self.min_confidence}")
                return None

            # Return the signal as the trade order
            return signal

        except Exception as e:
            logger.error(f"Error deciding trade: {str(e)}")
            return None

    def record_trade(self, trade_details: Dict) -> None:
        """
        Record trade details in history.

        Args:
            trade_details: Dictionary with trade details
        """
        try:
            trade_details['timestamp'] = datetime.now()
            self.trade_history.append(trade_details)

            # Keep only last 1000 trades
            if len(self.trade_history) > 1000:
                self.trade_history = self.trade_history[-1000:]

            logger.info(f"Recorded trade: {trade_details}")

        except Exception as e:
            logger.error(f"Error recording trade: {str(e)}")
            raise