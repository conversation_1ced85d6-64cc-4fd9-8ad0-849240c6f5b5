#!/usr/bin/env python
"""
Main training script for all models in the trading bot system.

This script orchestrates the training of all configured models using the
prepared data and standardized training procedures. It provides:

1. Unified training interface for all model types
2. Comprehensive logging and monitoring
3. Model validation and performance tracking
4. Automated model saving and versioning
5. Error handling and recovery mechanisms

Usage:
    python train_models.py [--config CONFIG_PATH] [--models MODEL_NAMES] [--force]
"""

import sys
import logging
import argparse
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
# Removed unused concurrent.futures imports since we switched to sequential training
import numpy as np
import pandas as pd

# Fix Unicode encoding issues in logging - Enhanced version
import codecs
import os
try:
    # Set environment variables for UTF-8 encoding
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    # Set UTF-8 encoding for stdout and stderr to prevent Unicode errors
    if hasattr(sys.stdout, 'detach'):
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    if hasattr(sys.stderr, 'detach'):
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

    # Additional fallback for Windows console encoding
    if os.name == 'nt':  # Windows
        try:
            import locale
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        except:
            pass

except Exception:
    # Fallback if encoding setup fails
    pass

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Configure PyTorch GPU settings early
def configure_pytorch_gpu():
    """Configure PyTorch GPU settings before any model initialization."""
    try:
        import torch

        # Enhanced GPU detection and configuration
        logger = logging.getLogger(__name__)
        logger.info("Configuring PyTorch GPU settings...")

        # Check PyTorch version and CUDA availability
        try:
            torch_version = torch.__version__
            logger.info(f"PyTorch version: {torch_version}")
        except:
            logger.info("PyTorch version: Unknown")

        # Check CUDA availability
        cuda_available = torch.cuda.is_available()
        device_count = torch.cuda.device_count() if cuda_available else 0

        logger.info("System-wide PyTorch GPU Detection:")
        logger.info(f"  - CUDA available: {cuda_available}")
        logger.info(f"  - GPU devices: {device_count}")

        if cuda_available and device_count > 0:
            logger.info("PyTorch **IS** using the GPU")
            for i in range(device_count):
                gpu_name = torch.cuda.get_device_name(i)
                logger.info(f"  - GPU {i}: {gpu_name}")
        else:
            logger.info("PyTorch **IS NOT** using the GPU")
            logger.error("CRITICAL: No GPU devices found!")
            logger.error("To fix GPU support:")
            logger.error("  1. Install PyTorch with CUDA: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
            logger.error("  2. Verify CUDA installation")
            logger.error("  3. Check GPU drivers")
            logger.warning("Continuing with CPU-only training...")
            return False

        # Test GPU functionality
        try:
            if cuda_available and device_count > 0:
                # Test GPU functionality using PyTorch
                device = torch.device('cuda')
                # Matrix multiplication test
                a = torch.tensor([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], device=device)
                b = torch.tensor([[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]], device=device)
                c = torch.matmul(a, b)
                logger.info(f"System-wide GPU test successful: {c.cpu().numpy()}")

                # Log GPU memory info
                if hasattr(torch.cuda, 'get_device_properties'):
                    props = torch.cuda.get_device_properties(0)
                    logger.info(f"GPU Memory: {props.total_memory / 1024**3:.1f} GB total")

                logger.info(f"System-wide: Configured {device_count} GPU(s) for PyTorch")
                return True
            else:
                logger.info("No GPU available for testing")
                return False

        except Exception as gpu_error:
            logger.error(f"System-wide GPU configuration error: {gpu_error}")
            logger.warning("System-wide: Falling back to CPU")
            return False

    except ImportError:
        logger.info("PyTorch not available for GPU configuration")
        return False
    except Exception as e:
        logger.warning(f"Error in system-wide PyTorch GPU configuration: {str(e)}")
        return False

# Configure PyTorch GPU early
configure_pytorch_gpu()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('model_training')

# Import project modules with error handling
try:
    from config.consolidated_config import ConfigurationManager
    from model_adapter import ModelInputAdapter
except ImportError as e:
    logger.error(f"Failed to import required modules: {str(e)}")
    logger.error("Please ensure all dependencies are installed and paths are correct")
    sys.exit(1)

class ModelTrainer:
    """Main class for orchestrating model training."""

    def __init__(self, config_path: Optional[str] = None, custom_params: Optional[Dict[str, Any]] = None):
        """Initialize the model trainer.

        Args:
            config_path: Optional path to configuration file
            custom_params: Optional dictionary of custom training parameters
        """
        self.config_manager = ConfigurationManager(config_path) if config_path else ConfigurationManager()
        self.config = self.config_manager.get_config()
        self.training_results = {}
        self.failed_models = []

        # Store custom parameters for overriding defaults
        self.custom_params = custom_params or {}

        # Create output directories
        self.output_dir = Path("training_results") / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Model trainer initialized with output directory: {self.output_dir}")
        if self.custom_params:
            logger.info(f"Custom parameters: {self.custom_params}")

    def load_training_data(self, symbol: str = "BTCUSD.a", timeframe: str = "M5") -> Dict[str, Any]:
        """Load and prepare training data.

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe

        Returns:
            Dictionary containing training, validation, and test data
        """
        try:
            logger.info(f"Loading training data for {symbol} {timeframe}")

            # Load processed data
            data_dir = Path(self.config.data_base_path) / "processed"
            train_path = data_dir / "train" / f"{symbol}_{timeframe}.parquet"
            val_path = data_dir / "validation" / f"{symbol}_{timeframe}.parquet"
            test_path = data_dir / "test" / f"{symbol}_{timeframe}.parquet"

            if not all(path.exists() for path in [train_path, val_path, test_path]):
                logger.error(f"Missing data files for {symbol} {timeframe}")
                return None

            # Load data
            train_df = pd.read_parquet(train_path)
            val_df = pd.read_parquet(val_path)
            test_df = pd.read_parquet(test_path)

            logger.info(f"Loaded data - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")

            return {
                'train': train_df,
                'validation': val_df,
                'test': test_df,
                'symbol': symbol,
                'timeframe': timeframe
            }

        except Exception as e:
            logger.error(f"Error loading training data: {str(e)}")
            return None

    def prepare_model_data(self, data: Dict[str, Any], model_config: Any) -> Dict[str, Any]:
        """Prepare data for specific model training.

        Args:
            data: Raw training data
            model_config: Model configuration

        Returns:
            Prepared data for model training
        """
        try:
            logger.info(f"Preparing data for model: {model_config.model_path}")

            # Extract features and targets
            feature_columns = model_config.FEATURE_COLUMNS
            if not feature_columns:
                logger.error("No feature columns specified in model configuration")
                return None

            # Map column names to handle case sensitivity issues
            def map_column_names(df, requested_columns):
                """Map requested column names to actual column names in the dataframe."""
                available_columns = df.columns.tolist()
                mapped_columns = []

                # Create a case-insensitive mapping
                column_mapping = {col.lower(): col for col in available_columns}

                for req_col in requested_columns:
                    if req_col in available_columns:
                        # Exact match
                        mapped_columns.append(req_col)
                    elif req_col.lower() in column_mapping:
                        # Case-insensitive match
                        actual_col = column_mapping[req_col.lower()]
                        mapped_columns.append(actual_col)
                        logger.info(f"Mapped column '{req_col}' to '{actual_col}'")
                    else:
                        logger.warning(f"Column '{req_col}' not found in data. Available columns: {available_columns}")
                        # Use the requested column anyway (will result in NaN)
                        mapped_columns.append(req_col)

                return mapped_columns

            # Map feature columns for each dataset
            train_feature_columns = map_column_names(data['train'], feature_columns)
            val_feature_columns = map_column_names(data['validation'], feature_columns)
            test_feature_columns = map_column_names(data['test'], feature_columns)

            # Prepare training data
            X_train = data['train'][train_feature_columns].values
            y_train = data['train']['close'].values

            X_val = data['validation'][val_feature_columns].values
            y_val = data['validation']['close'].values

            X_test = data['test'][test_feature_columns].values
            y_test = data['test']['close'].values

            # Handle NaN values
            for dataset_name, X, y in [('train', X_train, y_train), ('val', X_val, y_val), ('test', X_test, y_test)]:
                nan_count = np.isnan(X).sum() + np.isnan(y).sum()
                if nan_count > 0:
                    logger.warning(f"Found {nan_count} NaN values in {dataset_name} data")
                    X = np.nan_to_num(X, nan=0.0)
                    y = np.nan_to_num(y, nan=0.0)

            # Adapt input shapes for model based on model type
            model_type_lower = model_config.model_type.lower()

            # Tree-based models don't use sequences, just raw features
            if model_type_lower in ['xgboost', 'lightgbm']:
                target_shape = (len(feature_columns),)  # Just feature count for tree models
            else:
                target_shape = (model_config.sequence_length, len(feature_columns))  # Sequence models

            X_train_adapted = ModelInputAdapter.adapt_input(X_train, target_shape, model_config.model_type)
            X_val_adapted = ModelInputAdapter.adapt_input(X_val, target_shape, model_config.model_type)
            X_test_adapted = ModelInputAdapter.adapt_input(X_test, target_shape, model_config.model_type)

            if any(x is None for x in [X_train_adapted, X_val_adapted, X_test_adapted]):
                logger.error("Failed to adapt input data for model")
                return None

            # Handle logging for both dictionary (TFT) and array (other models) types
            if isinstance(X_train_adapted, dict):
                # For TFT models that return dictionaries
                train_info = f"dict with keys: {list(X_train_adapted.keys())}"
                val_info = f"dict with keys: {list(X_val_adapted.keys())}"
                if 'input_1' in X_train_adapted:
                    train_info += f", input_1 shape: {X_train_adapted['input_1'].shape}"
                    val_info += f", input_1 shape: {X_val_adapted['input_1'].shape}"
                elif 'encoder_cont' in X_train_adapted:
                    train_info += f", encoder_cont shape: {X_train_adapted['encoder_cont'].shape}"
                    val_info += f", encoder_cont shape: {X_val_adapted['encoder_cont'].shape}"
            else:
                # For regular numpy arrays
                train_info = str(X_train_adapted.shape)
                val_info = str(X_val_adapted.shape)

            logger.info(f"Data preparation complete - Train: {train_info}, Val: {val_info}")

            return {
                'X_train': X_train_adapted,
                'y_train': y_train,
                'X_val': X_val_adapted,
                'y_val': y_val,
                'X_test': X_test_adapted,
                'y_test': y_test,
                'feature_columns': feature_columns
            }

        except Exception as e:
            logger.error(f"Error preparing model data: {str(e)}")
            return None

    def train_single_model(self, model_name: str, model_config: Any, data: Dict[str, Any]) -> Dict[str, Any]:
        """Train a single model.

        Args:
            model_name: Name of the model
            model_config: Model configuration
            data: Training data

        Returns:
            Training results dictionary
        """
        try:
            logger.info(f"Starting training for model: {model_name}")
            start_time = datetime.now()

            # Prepare model-specific data
            model_data = self.prepare_model_data(data, model_config)
            if model_data is None:
                return {'success': False, 'error': 'Failed to prepare model data'}

            # Validate data shapes and content
            required_keys = ['X_train', 'y_train', 'X_val', 'y_val', 'X_test', 'y_test']
            for key in required_keys:
                if key not in model_data:
                    logger.error(f"Missing required data key: {key}")
                    return {'success': False, 'error': f'Missing required data key: {key}'}

                if model_data[key] is None:
                    logger.error(f"None data for key: {key}")
                    return {'success': False, 'error': f'None data for key: {key}'}

                # Check for empty data (handle both arrays and dictionaries)
                if isinstance(model_data[key], dict):
                    if not model_data[key] or all(len(v) == 0 for v in model_data[key].values()):
                        logger.error(f"Empty data for key: {key}")
                        return {'success': False, 'error': f'Empty data for key: {key}'}
                elif hasattr(model_data[key], '__len__') and len(model_data[key]) == 0:
                    logger.error(f"Empty data for key: {key}")
                    return {'success': False, 'error': f'Empty data for key: {key}'}

            # Import and initialize model
            model_class = self._get_model_class(model_config.model_type)
            if model_class is None:
                return {'success': False, 'error': f'Unknown model type: {model_config.model_type}'}

            # Create model configuration dictionary
            # Handle input_dim calculation for different model types
            if isinstance(model_data['X_train'], dict):
                # For TFT models that return dictionaries
                if 'input_1' in model_data['X_train']:
                    input_dim = model_data['X_train']['input_1'].shape[-1]
                elif 'encoder_cont' in model_data['X_train']:
                    input_dim = model_data['X_train']['encoder_cont'].shape[-1]
                else:
                    input_dim = len(model_data['feature_columns'])
            else:
                # For regular numpy arrays
                input_dim = model_data['X_train'].shape[-1] if len(model_data['X_train'].shape) > 1 else 1

            config_dict = {
                'model_name': model_name,
                'model_type': model_config.model_type,
                'timeframe': data['timeframe'],
                'terminal_id': '1',  # Default terminal
                'models_base_path': self.config.models_base_path,
                'model_filename': f"{model_name}_model",
                'input_dim': input_dim,
                'output_dim': 1,
                'FEATURE_COLUMNS': model_data['feature_columns'],
                **{k: v for k, v in model_config.__dict__.items() if not k.startswith('_')}
            }

            # Apply custom parameter overrides
            if self.custom_params:
                for param_name, param_value in self.custom_params.items():
                    if param_name in ['epochs', 'batch_size', 'learning_rate', 'patience',
                                    'sequence_length', 'dropout_rate', 'max_samples_per_dataset',
                                    'enable_memory_optimization']:
                        config_dict[param_name] = param_value
                        logger.info(f"Overriding {param_name} for {model_name}: {param_value}")

            # Initialize model
            model = model_class(config_dict)

            # Only call build() for models that can build without additional data
            # TFT model needs TimeSeriesDataSet to build, so it builds automatically during training
            if model_config.model_type.lower() != 'tft':
                try:
                    model.build()
                    logger.info(f"Model {model_name} built successfully")
                except Exception as build_error:
                    logger.warning(f"Failed to build model {model_name} during initialization: {build_error}")
                    logger.info(f"Model {model_name} will attempt to build during training")

            # Train model using standardized interface with custom parameters
            training_params = {
                'X_train': model_data['X_train'],
                'y_train': model_data['y_train'],
                'X_val': model_data['X_val'],
                'y_val': model_data['y_val'],
                'epochs': self.custom_params.get('epochs', getattr(model_config, 'epochs', 100)),
                'batch_size': self.custom_params.get('batch_size', getattr(model_config, 'batch_size', 64)),
                'patience': self.custom_params.get('patience', getattr(model_config, 'patience', 10)),
                'verbose': 1
            }

            training_result = model.train_with_validation(**training_params)

            # Evaluate on test data
            test_predictions = model.predict(model_data['X_test'])
            test_metrics = self._calculate_test_metrics(model_data['y_test'], test_predictions)

            # Save model
            save_success = model.save()

            end_time = datetime.now()
            training_duration = (end_time - start_time).total_seconds()

            result = {
                'success': True,
                'model_name': model_name,
                'training_metrics': training_result,
                'test_metrics': test_metrics,
                'training_duration': training_duration,
                'model_saved': save_success,
                'model_path': str(model.model_path) if hasattr(model, 'model_path') else None,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }

            logger.info(f"Training completed for {model_name} in {training_duration:.2f} seconds")
            return result

        except Exception as e:
            logger.error(f"Error training model {model_name}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'model_name': model_name,
                'error': str(e),
                'end_time': datetime.now().isoformat()
            }

    def _get_model_class(self, model_type: str):
        """Get model class based on model type.

        Args:
            model_type: Type of model

        Returns:
            Model class or None if not found
        """
        try:
            model_type_lower = model_type.lower()

            if model_type_lower == 'lstm':
                from models.lstm_model import LSTMModel
                return LSTMModel
            elif model_type_lower == 'gru':
                from models.gru_model import GRUModel
                return GRUModel
            elif model_type_lower == 'xgboost':
                from models.xgboost_model import XGBoostModel
                return XGBoostModel
            elif model_type_lower == 'lightgbm':
                from models.lightgbm_model import LightGBMModel
                return LightGBMModel
            elif model_type_lower == 'tft':
                from models.tft_model import TFTModel
                return TFTModel
            elif model_type_lower == 'transformer':
                from models.transformer_model import TransformerModel
                return TransformerModel
            elif model_type_lower == 'arima':
                from models.arima_model import ARIMAModel
                return ARIMAModel
            elif model_type_lower == 'lstm_arima':
                from models.ensemble_model import LSTMARIMAEnsemble
                return LSTMARIMAEnsemble
            elif model_type_lower == 'tft_arima':
                from models.ensemble_model import TFTARIMAEnsemble
                return TFTARIMAEnsemble
            else:
                logger.error(f"Unknown model type: {model_type}")
                return None

        except ImportError as e:
            logger.error(f"Failed to import model class for {model_type}: {str(e)}")
            return None

    def _calculate_test_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate test metrics.

        Args:
            y_true: True values
            y_pred: Predicted values

        Returns:
            Dictionary of metrics
        """
        try:
            from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

            # Ensure both arrays are 1D and have the same length
            y_true = np.asarray(y_true).flatten()
            y_pred = np.asarray(y_pred).flatten()

            # Handle shape mismatch by taking the minimum length
            min_length = min(len(y_true), len(y_pred))
            if len(y_true) != len(y_pred):
                logger.warning(f"Shape mismatch: y_true ({len(y_true)},), y_pred ({len(y_pred)},). Using first {min_length} samples.")
                # For TFT models with memory optimization, this is expected behavior
                if hasattr(self, '_tft_memory_optimized') and self._tft_memory_optimized:
                    logger.info("Shape mismatch is due to TFT memory optimization - this is expected behavior")
                y_true = y_true[:min_length]
                y_pred = y_pred[:min_length]

            if min_length == 0:
                logger.error("No valid samples for metrics calculation")
                return {}

            # Calculate basic metrics
            mae = float(mean_absolute_error(y_true, y_pred))
            mse = float(mean_squared_error(y_true, y_pred))
            rmse = float(np.sqrt(mse))
            r2 = float(r2_score(y_true, y_pred))

            # Data quality checks for poor performance diagnosis
            y_true_mean = float(np.mean(y_true))
            y_true_std = float(np.std(y_true))
            y_pred_mean = float(np.mean(y_pred))
            y_pred_std = float(np.std(y_pred))

            # Check for potential issues
            if r2 < -1.0:
                logger.warning(f"Very poor R² score ({r2:.6f}) detected. Data quality issues suspected.")
                logger.warning(f"True values - Mean: {y_true_mean:.2f}, Std: {y_true_std:.2f}")
                logger.warning(f"Predicted values - Mean: {y_pred_mean:.2f}, Std: {y_pred_std:.2f}")

                # Check for constant predictions
                if y_pred_std < 1e-6:
                    logger.warning("Model is making constant predictions - possible training failure")

                # Check for scale mismatch
                scale_ratio = y_pred_std / y_true_std if y_true_std > 0 else float('inf')
                if scale_ratio > 10 or scale_ratio < 0.1:
                    logger.warning(f"Scale mismatch detected - prediction std/true std ratio: {scale_ratio:.6f}")

            metrics = {
                'mae': mae,
                'mse': mse,
                'rmse': rmse,
                'r2': r2,
                'y_true_mean': y_true_mean,
                'y_true_std': y_true_std,
                'y_pred_mean': y_pred_mean,
                'y_pred_std': y_pred_std
            }

            # Calculate directional accuracy
            if len(y_true) > 1 and len(y_pred) > 1:
                true_direction = np.diff(y_true)
                pred_direction = np.diff(y_pred)
                directional_accuracy = np.mean((true_direction * pred_direction) > 0)
                metrics['directional_accuracy'] = float(directional_accuracy)

            return metrics

        except Exception as e:
            logger.error(f"Error calculating test metrics: {str(e)}")
            return {}

    def train_all_models(self, model_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Train all configured models sequentially.

        Args:
            model_names: Optional list of specific models to train

        Returns:
            Dictionary containing all training results
        """
        try:
            logger.info("Starting training for all models")

            # Load training data with custom symbol and timeframe if specified
            symbol = getattr(self, 'training_symbol', 'BTCUSD.a')
            timeframe = getattr(self, 'training_timeframe', 'M5')
            training_data = self.load_training_data(symbol, timeframe)
            if training_data is None:
                logger.error("Failed to load training data")
                return {'success': False, 'error': 'Failed to load training data'}

            # Get models to train
            models_to_train = model_names if model_names else list(self.config.models.keys())
            logger.info(f"Training models: {models_to_train}")

            # Train models sequentially to avoid GPU resource contention and deadlocks
            # Concurrent training was causing CUDA initialization deadlocks when multiple PyTorch models
            # tried to access GPU simultaneously during the build() phase
            logger.info("Training models sequentially to avoid GPU resource contention")

            for model_name in models_to_train:
                if model_name not in self.config.models:
                    logger.warning(f"Model {model_name} not found in configuration")
                    continue

                logger.info(f"Starting training for model: {model_name}")
                model_config = self.config.models[model_name]

                try:
                    result = self.train_single_model(model_name, model_config, training_data)
                    self.training_results[model_name] = result

                    if result['success']:
                        logger.info(f"Successfully trained {model_name}")
                    else:
                        logger.error(f"Failed to train {model_name}: {result.get('error', 'Unknown error')}")
                        self.failed_models.append(model_name)

                except Exception as e:
                    logger.error(f"Exception during training of {model_name}: {str(e)}")
                    self.failed_models.append(model_name)
                    self.training_results[model_name] = {
                        'success': False,
                        'model_name': model_name,
                        'error': str(e)
                    }

            # Generate summary report
            self._generate_training_report()

            return {
                'success': True,
                'total_models': len(models_to_train),
                'successful_models': len([r for r in self.training_results.values() if r['success']]),
                'failed_models': len(self.failed_models),
                'results': self.training_results
            }

        except Exception as e:
            logger.error(f"Error in train_all_models: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _generate_training_report(self):
        """Generate comprehensive training report."""
        try:
            report = {
                'training_session': {
                    'timestamp': datetime.now().isoformat(),
                    'total_models': len(self.training_results),
                    'successful_models': len([r for r in self.training_results.values() if r['success']]),
                    'failed_models': len(self.failed_models),
                    'failed_model_names': self.failed_models
                },
                'model_results': self.training_results,
                'summary_statistics': self._calculate_summary_statistics()
            }

            # Save report
            report_path = self.output_dir / "training_report.json"
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=4, default=str)

            logger.info(f"Training report saved to {report_path}")

            # Print summary
            self._print_training_summary()

        except Exception as e:
            logger.error(f"Error generating training report: {str(e)}")

    def _calculate_summary_statistics(self) -> Dict[str, Any]:
        """Calculate summary statistics across all models."""
        try:
            successful_results = [r for r in self.training_results.values() if r['success']]

            if not successful_results:
                return {}

            # Extract test metrics
            test_metrics = []
            training_durations = []

            for result in successful_results:
                if 'test_metrics' in result and result['test_metrics']:
                    test_metrics.append(result['test_metrics'])
                if 'training_duration' in result:
                    training_durations.append(result['training_duration'])

            summary = {}

            # Calculate average metrics
            if test_metrics:
                metric_names = set()
                for metrics in test_metrics:
                    metric_names.update(metrics.keys())

                for metric_name in metric_names:
                    values = [m[metric_name] for m in test_metrics if metric_name in m]
                    if values:
                        summary[f'avg_{metric_name}'] = np.mean(values)
                        summary[f'std_{metric_name}'] = np.std(values)
                        summary[f'min_{metric_name}'] = np.min(values)
                        summary[f'max_{metric_name}'] = np.max(values)

            # Training duration statistics
            if training_durations:
                summary['avg_training_duration'] = np.mean(training_durations)
                summary['total_training_time'] = np.sum(training_durations)
                summary['min_training_duration'] = np.min(training_durations)
                summary['max_training_duration'] = np.max(training_durations)

            return summary

        except Exception as e:
            logger.error(f"Error calculating summary statistics: {str(e)}")
            return {}

    def _print_training_summary(self):
        """Print training summary to console."""
        try:
            print("\n" + "="*80)
            print("TRAINING SUMMARY")
            print("="*80)

            total_models = len(self.training_results)
            successful_models = len([r for r in self.training_results.values() if r['success']])
            failed_models = len(self.failed_models)

            print(f"Total models: {total_models}")
            print(f"Successful: {successful_models}")
            print(f"Failed: {failed_models}")

            if self.failed_models:
                print(f"Failed models: {', '.join(self.failed_models)}")

            print("\nModel Performance:")
            print("-" * 50)

            for model_name, result in self.training_results.items():
                if result['success'] and 'test_metrics' in result:
                    metrics = result['test_metrics']
                    duration = result.get('training_duration', 0)

                    print(f"{model_name}:")
                    print(f"  Duration: {duration:.2f}s")
                    if 'rmse' in metrics:
                        print(f"  RMSE: {metrics['rmse']:.6f}")
                    if 'mae' in metrics:
                        print(f"  MAE: {metrics['mae']:.6f}")
                    if 'r2' in metrics:
                        print(f"  R²: {metrics['r2']:.6f}")
                    if 'directional_accuracy' in metrics:
                        print(f"  Dir. Acc: {metrics['directional_accuracy']:.4f}")
                    print()
                else:
                    print(f"{model_name}: FAILED")
                    if 'error' in result:
                        print(f"  Error: {result['error']}")
                    print()

            print("="*80)

        except Exception as e:
            logger.error(f"Error printing training summary: {str(e)}")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train all models in the trading bot system')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--models', nargs='+', help='Specific models to train')
    # Removed --max-workers since we switched to sequential training to avoid GPU deadlocks
    parser.add_argument('--force', action='store_true', help='Force training even if models exist')

    # Custom training parameters
    parser.add_argument('--epochs', type=int, help='Override epochs for training')
    parser.add_argument('--batch-size', type=int, help='Override batch size for training')
    parser.add_argument('--learning-rate', type=float, help='Override learning rate for training')
    parser.add_argument('--patience', type=int, help='Override patience for early stopping')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Trading symbol for training data')
    parser.add_argument('--timeframe', type=str, default='M5', help='Timeframe for training data')
    parser.add_argument('--sequence-length', type=int, help='Override sequence length for models')
    parser.add_argument('--dropout-rate', type=float, help='Override dropout rate for neural networks')
    parser.add_argument('--validation-split', type=float, help='Override validation split ratio')

    # TFT-specific parameters
    parser.add_argument('--max-samples-per-dataset', type=int, help='Override max samples per dataset for TFT model')
    parser.add_argument('--enable-memory-optimization', type=str, choices=['true', 'false'], help='Enable/disable memory optimization for TFT model')

    return parser.parse_args()


def main():
    """Main function."""
    try:
        args = parse_arguments()

        logger.info("Starting model training session")
        logger.info(f"Configuration: {args.config}")
        logger.info(f"Models: {args.models if args.models else 'All configured models'}")
        logger.info(f"Training mode: Sequential (to avoid GPU deadlocks)")
        logger.info(f"Symbol: {args.symbol}, Timeframe: {args.timeframe}")

        # Prepare custom parameters from command line arguments
        custom_params = {}
        if args.epochs is not None:
            custom_params['epochs'] = args.epochs
        if args.batch_size is not None:
            custom_params['batch_size'] = args.batch_size
        if args.learning_rate is not None:
            custom_params['learning_rate'] = args.learning_rate
        if args.patience is not None:
            custom_params['patience'] = args.patience
        if args.sequence_length is not None:
            custom_params['sequence_length'] = args.sequence_length
        if args.dropout_rate is not None:
            custom_params['dropout_rate'] = args.dropout_rate
        if args.validation_split is not None:
            custom_params['validation_split'] = args.validation_split
        if args.max_samples_per_dataset is not None:
            custom_params['max_samples_per_dataset'] = args.max_samples_per_dataset
        if args.enable_memory_optimization is not None:
            custom_params['enable_memory_optimization'] = args.enable_memory_optimization.lower() == 'true'

        # Initialize trainer with custom parameters
        trainer = ModelTrainer(args.config, custom_params)

        # Store symbol and timeframe for data loading
        trainer.training_symbol = args.symbol
        trainer.training_timeframe = args.timeframe

        # Train models sequentially
        results = trainer.train_all_models(
            model_names=args.models
        )

        if results['success']:
            logger.info("Training session completed successfully")
            successful_count = results['successful_models']
            total_count = results['total_models']
            logger.info(f"Successfully trained {successful_count}/{total_count} models")
        else:
            logger.error(f"Training session failed: {results.get('error', 'Unknown error')}")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error in main: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
