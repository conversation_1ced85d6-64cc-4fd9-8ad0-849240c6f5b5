#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to prepare collected BTCUSD.a data for model training.

This script:
1. Loads the collected parquet files
2. Merges data from multiple terminals
3. Performs data cleaning and validation
4. Calculates additional features
5. Splits data into training, validation, and test sets
6. Saves the prepared data in parquet format
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime
import os
from pathlib import Path
import argparse
from concurrent.futures import ThreadPoolExecutor
import json
import sys

# Add the project root to the Python path to ensure imports work correctly
sys.path.insert(0, os.path.abspath('.'))

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_preparation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('data_preparation')

# Import local modules with proper error handling
try:
    from utils.data_preprocessor import DataPreprocessor
    logger.info("Successfully imported required modules")
except ImportError as e:
    logger.error(f"Failed to import required modules: {str(e)}")
    # Create fallback implementations
    class DataPreprocessor:
        def process_data(self, df, add_technical_indicators=True):
            logger.warning("Using simplified DataPreprocessor implementation")
            # Add basic technical indicators
            if add_technical_indicators:
                df = self._add_basic_indicators(df)
            return df

        def _add_basic_indicators(self, df):
            """Add basic technical indicators."""
            try:
                # Simple moving averages
                df['sma_5'] = df['close'].rolling(window=5).mean()
                df['sma_20'] = df['close'].rolling(window=20).mean()
                df['sma_50'] = df['close'].rolling(window=50).mean()

                # Price changes
                df['price_change'] = df['close'].pct_change()
                df['high_low_ratio'] = df['high'] / df['low']
                df['volume_change'] = df['tick_volume'].pct_change()

                # Fill NaN values
                df = df.ffill().bfill().fillna(0)

                logger.info("Added basic technical indicators")
                return df
            except Exception as e:
                logger.error(f"Error adding technical indicators: {str(e)}")
                return df

    class ConfigurationManager:
        def __init__(self):
            pass

        def get_data_base_path(self):
            return "data"

# Constants
SYMBOL = "BTCUSD.a"
TIMEFRAMES = ["M5", "M15", "M30", "H1", "H4"]
MAX_WORKERS = 5  # Maximum number of concurrent workers

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Prepare BTCUSD.a data for model training')
    parser.add_argument('--input_dir', type=str, default='data/raw',
                        help='Input directory containing collected data')
    parser.add_argument('--output_dir', type=str, default='data/processed',
                        help='Output directory for processed data')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a',
                        help='Trading symbol to process')
    parser.add_argument('--timeframe', type=str, choices=['M5', 'M15', 'M30', 'H1', 'H4'],
                        help='Specific timeframe to process (if not specified, processes all)')
    parser.add_argument('--split-ratio', type=str, default='0.7,0.15,0.15',
                        help='Train,validation,test split ratios (comma-separated)')
    parser.add_argument('--force', action='store_true',
                        help='Force data preparation even if files already exist')
    return parser.parse_args()

def merge_terminal_data(symbol, timeframe, input_dir):
    """
    Merge data from multiple terminals for a specific symbol and timeframe.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe
        input_dir: Input directory containing collected data

    Returns:
        Merged DataFrame or None if error
    """
    try:
        logger.info(f"Merging {symbol} {timeframe} data from multiple terminals")

        # Find all files for this symbol and timeframe
        input_path = Path(input_dir)

        # Try multiple file patterns to handle different naming conventions
        patterns = [
            f"{symbol}_{timeframe}_terminal_*.parquet",  # Original pattern
            f"{symbol}_{timeframe}.parquet",             # Simple pattern
            f"*{symbol}*{timeframe}*.parquet"            # Flexible pattern
        ]

        files = []
        for pattern in patterns:
            found_files = list(input_path.glob(pattern))
            if found_files:
                files.extend(found_files)
                break  # Use first pattern that finds files

        if not files:
            logger.error(f"No data files found for {symbol} {timeframe} in {input_dir}")
            logger.error(f"Tried patterns: {patterns}")
            # List available files for debugging
            available_files = list(input_path.glob("*.parquet"))
            logger.error(f"Available parquet files: {[f.name for f in available_files]}")
            return None

        logger.info(f"Found {len(files)} files for {symbol} {timeframe}")

        # Load and merge data
        dfs = []
        for file_path in files:
            try:
                df = pd.read_parquet(file_path)
                if not df.empty:
                    dfs.append(df)
                    logger.info(f"Loaded {len(df)} rows from {file_path}")
                else:
                    logger.warning(f"Empty data file: {file_path}")
            except Exception as e:
                logger.error(f"Error loading {file_path}: {str(e)}")

        if not dfs:
            logger.error(f"No valid data loaded for {symbol} {timeframe}")
            return None

        # Concatenate all dataframes
        merged_df = pd.concat(dfs)

        # Remove duplicates and sort
        merged_df = merged_df[~merged_df.index.duplicated(keep='first')]
        merged_df = merged_df.sort_index()

        logger.info(f"Merged data has {len(merged_df)} rows for {symbol} {timeframe}")

        return merged_df

    except Exception as e:
        logger.error(f"Error merging data for {symbol} {timeframe}: {str(e)}")
        return None

def clean_and_validate_data(df, symbol, timeframe):
    """
    Clean and validate the merged data.

    Args:
        df: DataFrame to clean and validate
        symbol: Trading symbol
        timeframe: Timeframe

    Returns:
        Cleaned DataFrame or None if validation fails
    """
    try:
        if df is None or df.empty:
            logger.error(f"No data to clean for {symbol} {timeframe}")
            return None

        logger.info(f"Cleaning and validating {symbol} {timeframe} data with {len(df)} rows")

        # Handle missing values
        missing_values = df.isnull().sum()
        if missing_values.sum() > 0:
            logger.warning(f"Found missing values in {symbol} {timeframe} data: {missing_values[missing_values > 0]}")

            # Forward fill then backward fill
            df = df.ffill().bfill()

            # Check if there are still missing values
            if df.isnull().sum().sum() > 0:
                logger.error(f"Still have missing values after filling: {df.isnull().sum()[df.isnull().sum() > 0]}")
                return None

        # Check for invalid price relationships
        invalid_rows = df[(df['high'] < df['low']) |
                        (df['open'] > df['high']) |
                        (df['open'] < df['low']) |
                        (df['close'] > df['high']) |
                        (df['close'] < df['low'])]

        if not invalid_rows.empty:
            logger.warning(f"Found {len(invalid_rows)} rows with invalid price relationships in {symbol} {timeframe} data")

            # Remove invalid rows
            df = df.drop(invalid_rows.index)

            if df.empty:
                logger.error(f"No valid data left after removing invalid rows for {symbol} {timeframe}")
                return None

        # Check for outliers in price data
        for col in ['open', 'high', 'low', 'close']:
            # Calculate z-scores
            z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
            outliers = z_scores > 3  # More than 3 standard deviations

            if outliers.sum() > 0:
                logger.warning(f"Found {outliers.sum()} outliers in {col} for {symbol} {timeframe}")

                # Don't remove outliers, just log them

        logger.info(f"Data cleaning and validation complete for {symbol} {timeframe}, {len(df)} rows remaining")

        return df

    except Exception as e:
        logger.error(f"Error cleaning and validating data for {symbol} {timeframe}: {str(e)}")
        return None

def calculate_features(df, symbol, timeframe):
    """
    Calculate additional features for the data.

    Args:
        df: DataFrame to calculate features for
        symbol: Trading symbol
        timeframe: Timeframe

    Returns:
        DataFrame with additional features
    """
    try:
        if df is None or df.empty:
            logger.error(f"No data to calculate features for {symbol} {timeframe}")
            return None

        logger.info(f"Calculating features for {symbol} {timeframe} data with {len(df)} rows")

        # Initialize preprocessor
        preprocessor = DataPreprocessor()

        # Calculate features
        df_with_features = preprocessor.process_data(df, add_technical_indicators=True)

        logger.info(f"Calculated features for {symbol} {timeframe}, now have {len(df_with_features.columns)} columns")

        return df_with_features

    except Exception as e:
        logger.error(f"Error calculating features for {symbol} {timeframe}: {str(e)}")
        return None

def split_and_save_data(df, symbol, timeframe, output_dir, split_ratios=(0.7, 0.15, 0.15)):
    """
    Split data into training, validation, and test sets and save to parquet files.

    Args:
        df: DataFrame to split and save
        symbol: Trading symbol
        timeframe: Timeframe
        output_dir: Output directory
        split_ratios: Tuple of (train_ratio, val_ratio, test_ratio)

    Returns:
        Dictionary with paths to saved files
    """
    try:
        if df is None or df.empty:
            logger.error(f"No data to split and save for {symbol} {timeframe}")
            return None

        logger.info(f"Splitting and saving {symbol} {timeframe} data with {len(df)} rows")

        # Create output directories
        output_path = Path(output_dir)
        train_dir = output_path / 'train'
        val_dir = output_path / 'validation'
        test_dir = output_path / 'test'

        for directory in [train_dir, val_dir, test_dir]:
            directory.mkdir(parents=True, exist_ok=True)

        # Sort by time
        df = df.sort_index()

        # Calculate split points using provided ratios
        train_ratio, val_ratio, _ = split_ratios  # test_ratio not needed for calculation
        total_rows = len(df)
        train_end = int(total_rows * train_ratio)
        val_end = train_end + int(total_rows * val_ratio)

        # Split the data
        train_df = df.iloc[:train_end]
        val_df = df.iloc[train_end:val_end]
        test_df = df.iloc[val_end:]

        # Save to parquet files
        train_path = train_dir / f"{symbol}_{timeframe}.parquet"
        val_path = val_dir / f"{symbol}_{timeframe}.parquet"
        test_path = test_dir / f"{symbol}_{timeframe}.parquet"

        train_df.to_parquet(train_path)
        val_df.to_parquet(val_path)
        test_df.to_parquet(test_path)

        logger.info(f"Saved {len(train_df)} training rows to {train_path}")
        logger.info(f"Saved {len(val_df)} validation rows to {val_path}")
        logger.info(f"Saved {len(test_df)} test rows to {test_path}")

        # Save metadata
        metadata = {
            'symbol': symbol,
            'timeframe': timeframe,
            'total_rows': total_rows,
            'train_rows': len(train_df),
            'val_rows': len(val_df),
            'test_rows': len(test_df),
            'train_start': train_df.index.min().strftime('%Y-%m-%d %H:%M:%S'),
            'train_end': train_df.index.max().strftime('%Y-%m-%d %H:%M:%S'),
            'val_start': val_df.index.min().strftime('%Y-%m-%d %H:%M:%S'),
            'val_end': val_df.index.max().strftime('%Y-%m-%d %H:%M:%S'),
            'test_start': test_df.index.min().strftime('%Y-%m-%d %H:%M:%S'),
            'test_end': test_df.index.max().strftime('%Y-%m-%d %H:%M:%S'),
            'columns': list(df.columns),
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        metadata_path = output_path / 'metadata' / f"{symbol}_{timeframe}_metadata.json"
        metadata_path.parent.mkdir(parents=True, exist_ok=True)

        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=4)

        logger.info(f"Saved metadata to {metadata_path}")

        return {
            'train_path': str(train_path),
            'val_path': str(val_path),
            'test_path': str(test_path),
            'metadata_path': str(metadata_path)
        }

    except Exception as e:
        logger.error(f"Error splitting and saving data for {symbol} {timeframe}: {str(e)}")
        return None

def process_timeframe(symbol, timeframe, input_dir, output_dir, split_ratios=(0.7, 0.15, 0.15)):
    """
    Process data for a specific symbol and timeframe.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe
        input_dir: Input directory
        output_dir: Output directory
        split_ratios: Tuple of (train_ratio, val_ratio, test_ratio)

    Returns:
        Dictionary with processing results
    """
    try:
        logger.info(f"Processing {symbol} {timeframe} data")

        # Step 1: Merge data from multiple terminals
        merged_df = merge_terminal_data(symbol, timeframe, input_dir)
        if merged_df is None:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'success': False,
                'message': "Failed to merge terminal data"
            }

        # Step 2: Clean and validate data
        cleaned_df = clean_and_validate_data(merged_df, symbol, timeframe)
        if cleaned_df is None:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'success': False,
                'message': "Failed to clean and validate data"
            }

        # Step 3: Calculate features
        featured_df = calculate_features(cleaned_df, symbol, timeframe)
        if featured_df is None:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'success': False,
                'message': "Failed to calculate features"
            }

        # Step 4: Split and save data
        save_result = split_and_save_data(featured_df, symbol, timeframe, output_dir, split_ratios)
        if save_result is None:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'success': False,
                'message': "Failed to split and save data"
            }

        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': True,
            'message': "Successfully processed data",
            'paths': save_result
        }

    except Exception as e:
        logger.error(f"Error processing {symbol} {timeframe} data: {str(e)}")
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': False,
            'message': f"Error: {str(e)}"
        }

def main():
    """Main function to prepare training data."""
    args = parse_arguments()

    # Parse split ratios
    try:
        split_ratios = tuple(map(float, args.split_ratio.split(',')))
        if len(split_ratios) != 3 or abs(sum(split_ratios) - 1.0) > 0.001:
            raise ValueError("Split ratios must sum to 1.0")
        logger.info(f"Using split ratios: train={split_ratios[0]}, val={split_ratios[1]}, test={split_ratios[2]}")
    except Exception as e:
        logger.error(f"Invalid split ratios: {args.split_ratio}. Error: {str(e)}")
        return

    # Determine which symbol and timeframes to process
    symbol = args.symbol
    timeframes = [args.timeframe] if args.timeframe else TIMEFRAMES

    logger.info(f"Starting data preparation for {symbol}")
    logger.info(f"Processing timeframes: {timeframes}")
    logger.info(f"Input directory: {args.input_dir}")
    logger.info(f"Output directory: {args.output_dir}")

    # Process each timeframe using ThreadPoolExecutor
    results = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = []

        # Submit tasks for each timeframe
        for timeframe in timeframes:
            future = executor.submit(
                process_timeframe,
                symbol,
                timeframe,
                args.input_dir,
                args.output_dir,
                split_ratios
            )
            futures.append((future, timeframe))

        # Process results as they complete
        for future, timeframe in futures:
            try:
                result = future.result()
                results.append(result)
                logger.info(f"Result for {SYMBOL} {timeframe}: {result['message']}")
            except Exception as e:
                logger.error(f"Error processing result for {SYMBOL} {timeframe}: {str(e)}")
                results.append({
                    'symbol': SYMBOL,
                    'timeframe': timeframe,
                    'success': False,
                    'message': f"Error: {str(e)}"
                })

    # Print summary
    logger.info("\n=== Data Preparation Summary ===")
    logger.info(f"Total tasks: {len(results)}")
    logger.info(f"Successful preparations: {sum(1 for r in results if r['success'])}")
    logger.info(f"Failed preparations: {sum(1 for r in results if not r['success'])}")

    for result in results:
        status = "SUCCESS" if result['success'] else "FAILED"
        logger.info(f"{result['symbol']} {result['timeframe']}: {status} - {result['message']}")

    logger.info("Data preparation complete.")

if __name__ == "__main__":
    main()
