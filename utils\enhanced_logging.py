"""
Enhanced Logging and Monitoring System.

This module provides comprehensive logging and monitoring capabilities
with structured logging, performance tracking, and integration with
other system components for better visibility into system operation.

Key features:
1. Structured logging with context and correlation IDs
2. Performance tracking and metrics collection
3. Log aggregation and filtering
4. Automatic log rotation and cleanup
5. Integration with error handling and resource management
"""

import os
import sys
import logging
import logging.handlers
import threading
import time
import json
import uuid
import traceback
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import functools
import inspect
import socket
import platform

# Configure base logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# Get base logger
logger = logging.getLogger(__name__)

class LogLevel(Enum):
    """Log levels with numeric values for comparison."""
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50

class LogCategory(Enum):
    """Categories for log messages."""
    SYSTEM = "system"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BUSINESS = "business"
    INTEGRATION = "integration"
    DATABASE = "database"
    NETWORK = "network"
    UI = "ui"
    API = "api"
    TRADING = "trading"
    MT5 = "mt5"

@dataclass
class LogContext:
    """Context information for structured logging."""
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    component: Optional[str] = None
    operation: Optional[str] = None
    category: LogCategory = LogCategory.SYSTEM
    extra: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PerformanceMetric:
    """Performance metric for tracking operation performance."""
    operation: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[float] = None
    success: bool = True
    context: Dict[str, Any] = field(default_factory=dict)
    
    def complete(self, success: bool = True) -> None:
        """Mark the operation as complete and calculate duration."""
        self.end_time = datetime.now()
        self.success = success
        if self.start_time:
            self.duration_ms = (self.end_time - self.start_time).total_seconds() * 1000

class EnhancedLogger:
    """
    Enhanced logger with structured logging, performance tracking,
    and integration with other system components.
    """
    
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls):
        """Get the singleton instance."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """Initialize the enhanced logger."""
        self._log_dir = Path("logs")
        self._log_dir.mkdir(exist_ok=True)
        
        # Create file handlers
        self._setup_file_handlers()
        
        # Thread-local storage for context
        self._context = threading.local()
        self._context.log_context = LogContext()
        
        # Performance metrics
        self._performance_metrics: List[PerformanceMetric] = []
        self._metrics_lock = threading.RLock()
        
        # Maximum number of metrics to keep
        self._max_metrics = 1000
        
        # System information
        self._system_info = self._collect_system_info()
        
        # Integration with other components
        self._error_handler = None
        self._resource_manager = None
        
        logger.info("Enhanced logger initialized")
    
    def _setup_file_handlers(self) -> None:
        """Set up file handlers for different log levels."""
        # Main log file with rotation
        main_handler = logging.handlers.RotatingFileHandler(
            self._log_dir / "application.log",
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=10
        )
        main_handler.setLevel(logging.INFO)
        main_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        main_handler.setFormatter(main_formatter)
        
        # Error log file with rotation
        error_handler = logging.handlers.RotatingFileHandler(
            self._log_dir / "error.log",
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=10
        )
        error_handler.setLevel(logging.ERROR)
        error_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s\n%(pathname)s:%(lineno)d\n'
        )
        error_handler.setFormatter(error_formatter)
        
        # Add handlers to root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(main_handler)
        root_logger.addHandler(error_handler)
        
        # JSON log file for structured logging
        self._json_handler = logging.handlers.RotatingFileHandler(
            self._log_dir / "structured.json",
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=10
        )
        self._json_handler.setLevel(logging.INFO)
    
    def _collect_system_info(self) -> Dict[str, Any]:
        """Collect system information for logging context."""
        try:
            return {
                "hostname": socket.gethostname(),
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "cpu_count": os.cpu_count(),
                "pid": os.getpid(),
                "start_time": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error collecting system info: {str(e)}")
            return {}
    
    def set_context(self, **kwargs) -> None:
        """
        Set context values for the current thread.
        
        Args:
            **kwargs: Context values to set
        """
        if not hasattr(self._context, 'log_context'):
            self._context.log_context = LogContext()
        
        for key, value in kwargs.items():
            if hasattr(self._context.log_context, key):
                setattr(self._context.log_context, key, value)
            else:
                self._context.log_context.extra[key] = value
    
    def get_context(self) -> LogContext:
        """
        Get the current logging context.
        
        Returns:
            LogContext: Current logging context
        """
        if not hasattr(self._context, 'log_context'):
            self._context.log_context = LogContext()
        
        return self._context.log_context
    
    def with_context(self, **kwargs):
        """
        Decorator to set context for a function call.
        
        Example:
            @enhanced_logger.with_context(component="auth", operation="login")
            def login_user(username, password):
                # Function that will be logged with context
                pass
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **func_kwargs):
                # Save original context
                original_context = self.get_context()
                
                # Set new context
                self.set_context(**kwargs)
                
                # Start performance tracking
                metric = self.start_operation(func.__name__)
                
                try:
                    # Call the function
                    result = func(*args, **func_kwargs)
                    
                    # Complete performance tracking
                    self.complete_operation(metric, success=True)
                    
                    return result
                except Exception as e:
                    # Log the error with context
                    self.error(f"Error in {func.__name__}: {str(e)}", exc_info=True)
                    
                    # Complete performance tracking
                    self.complete_operation(metric, success=False)
                    
                    # Re-raise the exception
                    raise
                finally:
                    # Restore original context
                    self._context.log_context = original_context
            
            return wrapper
        
        return decorator
    
    def debug(self, message: str, **kwargs) -> None:
        """
        Log a debug message with context.
        
        Args:
            message: Message to log
            **kwargs: Additional context values
        """
        self._log(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """
        Log an info message with context.
        
        Args:
            message: Message to log
            **kwargs: Additional context values
        """
        self._log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """
        Log a warning message with context.
        
        Args:
            message: Message to log
            **kwargs: Additional context values
        """
        self._log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, exc_info: bool = False, **kwargs) -> None:
        """
        Log an error message with context.
        
        Args:
            message: Message to log
            exc_info: Whether to include exception info
            **kwargs: Additional context values
        """
        self._log(LogLevel.ERROR, message, exc_info=exc_info, **kwargs)
    
    def critical(self, message: str, exc_info: bool = False, **kwargs) -> None:
        """
        Log a critical message with context.
        
        Args:
            message: Message to log
            exc_info: Whether to include exception info
            **kwargs: Additional context values
        """
        self._log(LogLevel.CRITICAL, message, exc_info=exc_info, **kwargs)
    
    def _log(self, level: LogLevel, message: str, exc_info: bool = False, **kwargs) -> None:
        """
        Internal method to log a message with context.
        
        Args:
            level: Log level
            message: Message to log
            exc_info: Whether to include exception info
            **kwargs: Additional context values
        """
        # Get current context
        context = self.get_context()
        
        # Update context with kwargs
        temp_context = LogContext(
            correlation_id=context.correlation_id,
            user_id=context.user_id,
            session_id=context.session_id,
            request_id=context.request_id,
            component=context.component,
            operation=context.operation,
            category=context.category,
            extra=context.extra.copy()
        )
        
        for key, value in kwargs.items():
            if hasattr(temp_context, key):
                setattr(temp_context, key, value)
            else:
                temp_context.extra[key] = value
        
        # Get caller information
        frame = inspect.currentframe().f_back.f_back
        module = inspect.getmodule(frame)
        module_name = module.__name__ if module else "unknown"
        
        # Log to standard logger
        log_func = getattr(logging.getLogger(module_name), level.name.lower())
        log_func(message, exc_info=exc_info)
        
        # Log structured data to JSON file
        self._log_structured(level, message, temp_context, module_name, exc_info)
    
    def _log_structured(self, level: LogLevel, message: str, context: LogContext, 
                       module_name: str, exc_info: bool) -> None:
        """
        Log structured data to JSON file.
        
        Args:
            level: Log level
            message: Message to log
            context: Log context
            module_name: Module name
            exc_info: Whether to include exception info
        """
        try:
            # Create structured log entry
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "level": level.name,
                "message": message,
                "module": module_name,
                "correlation_id": context.correlation_id,
                "category": context.category.value,
                "system_info": {
                    "hostname": self._system_info.get("hostname"),
                    "pid": self._system_info.get("pid")
                }
            }
            
            # Add context fields if they exist
            if context.user_id:
                log_entry["user_id"] = context.user_id
            if context.session_id:
                log_entry["session_id"] = context.session_id
            if context.request_id:
                log_entry["request_id"] = context.request_id
            if context.component:
                log_entry["component"] = context.component
            if context.operation:
                log_entry["operation"] = context.operation
            
            # Add extra context
            if context.extra:
                log_entry["context"] = context.extra
            
            # Add exception info if requested
            if exc_info:
                exc_type, exc_value, exc_tb = sys.exc_info()
                if exc_type and exc_value and exc_tb:
                    log_entry["exception"] = {
                        "type": exc_type.__name__,
                        "message": str(exc_value),
                        "traceback": traceback.format_exception(exc_type, exc_value, exc_tb)
                    }
            
            # Write to JSON file
            with open(self._json_handler.baseFilename, "a") as f:
                f.write(json.dumps(log_entry) + "\n")
                
        except Exception as e:
            # Log to standard logger if structured logging fails
            logger.error(f"Error in structured logging: {str(e)}")
    
    def start_operation(self, operation: str, **context) -> PerformanceMetric:
        """
        Start tracking performance for an operation.
        
        Args:
            operation: Name of the operation
            **context: Additional context values
            
        Returns:
            PerformanceMetric: Performance metric object
        """
        metric = PerformanceMetric(
            operation=operation,
            start_time=datetime.now(),
            context=context
        )
        
        return metric
    
    def complete_operation(self, metric: PerformanceMetric, success: bool = True) -> None:
        """
        Complete performance tracking for an operation.
        
        Args:
            metric: Performance metric object
            success: Whether the operation was successful
        """
        # Complete the metric
        metric.complete(success)
        
        # Store the metric
        with self._metrics_lock:
            self._performance_metrics.append(metric)
            
            # Trim metrics list if needed
            if len(self._performance_metrics) > self._max_metrics:
                self._performance_metrics = self._performance_metrics[-self._max_metrics:]
        
        # Log performance data
        if metric.duration_ms is not None:
            status = "succeeded" if success else "failed"
            self.info(
                f"Operation '{metric.operation}' {status} in {metric.duration_ms:.2f}ms",
                category=LogCategory.PERFORMANCE,
                operation=metric.operation,
                duration_ms=metric.duration_ms,
                success=success
            )
    
    def get_performance_metrics(self, operation: Optional[str] = None, 
                              minutes: int = 60) -> List[PerformanceMetric]:
        """
        Get performance metrics.
        
        Args:
            operation: Optional operation name to filter by
            minutes: Number of minutes to look back
            
        Returns:
            List[PerformanceMetric]: List of performance metrics
        """
        with self._metrics_lock:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            
            if operation:
                return [m for m in self._performance_metrics 
                       if m.operation == operation and m.start_time >= cutoff_time]
            else:
                return [m for m in self._performance_metrics 
                       if m.start_time >= cutoff_time]
    
    def get_performance_summary(self, operation: Optional[str] = None, 
                              minutes: int = 60) -> Dict[str, Any]:
        """
        Get performance summary statistics.
        
        Args:
            operation: Optional operation name to filter by
            minutes: Number of minutes to look back
            
        Returns:
            Dict[str, Any]: Performance summary statistics
        """
        metrics = self.get_performance_metrics(operation, minutes)
        
        if not metrics:
            return {
                "count": 0,
                "success_rate": 0,
                "avg_duration_ms": 0,
                "min_duration_ms": 0,
                "max_duration_ms": 0,
                "p95_duration_ms": 0
            }
        
        # Calculate statistics
        count = len(metrics)
        success_count = sum(1 for m in metrics if m.success)
        success_rate = (success_count / count) * 100 if count > 0 else 0
        
        durations = [m.duration_ms for m in metrics if m.duration_ms is not None]
        if durations:
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            # Calculate p95
            sorted_durations = sorted(durations)
            p95_index = int(len(sorted_durations) * 0.95)
            p95_duration = sorted_durations[p95_index] if p95_index < len(sorted_durations) else max_duration
        else:
            avg_duration = min_duration = max_duration = p95_duration = 0
        
        # Group by operation
        operations = {}
        for metric in metrics:
            op = metric.operation
            if op not in operations:
                operations[op] = {
                    "count": 0,
                    "success_count": 0,
                    "durations": []
                }
            
            operations[op]["count"] += 1
            if metric.success:
                operations[op]["success_count"] += 1
            if metric.duration_ms is not None:
                operations[op]["durations"].append(metric.duration_ms)
        
        # Calculate per-operation statistics
        operation_stats = {}
        for op, data in operations.items():
            op_count = data["count"]
            op_success_rate = (data["success_count"] / op_count) * 100 if op_count > 0 else 0
            
            op_durations = data["durations"]
            if op_durations:
                op_avg_duration = sum(op_durations) / len(op_durations)
                op_min_duration = min(op_durations)
                op_max_duration = max(op_durations)
                
                # Calculate p95
                op_sorted_durations = sorted(op_durations)
                op_p95_index = int(len(op_sorted_durations) * 0.95)
                op_p95_duration = op_sorted_durations[op_p95_index] if op_p95_index < len(op_sorted_durations) else op_max_duration
            else:
                op_avg_duration = op_min_duration = op_max_duration = op_p95_duration = 0
            
            operation_stats[op] = {
                "count": op_count,
                "success_rate": op_success_rate,
                "avg_duration_ms": op_avg_duration,
                "min_duration_ms": op_min_duration,
                "max_duration_ms": op_max_duration,
                "p95_duration_ms": op_p95_duration
            }
        
        return {
            "count": count,
            "success_rate": success_rate,
            "avg_duration_ms": avg_duration,
            "min_duration_ms": min_duration,
            "max_duration_ms": max_duration,
            "p95_duration_ms": p95_duration,
            "operations": operation_stats
        }
    
    def set_error_handler(self, error_handler) -> None:
        """Set the error handler for integration."""
        self._error_handler = error_handler
        logger.info("Error handler integrated with enhanced logger")
    
    def set_resource_manager(self, resource_manager) -> None:
        """Set the resource manager for integration."""
        self._resource_manager = resource_manager
        logger.info("Resource manager integrated with enhanced logger")
    
    def cleanup_logs(self, days: int = 30) -> int:
        """
        Clean up old log files.
        
        Args:
            days: Number of days to keep logs
            
        Returns:
            int: Number of files removed
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            count = 0
            
            for file_path in self._log_dir.glob("*.log.*"):
                if file_path.stat().st_mtime < cutoff_time.timestamp():
                    file_path.unlink()
                    count += 1
            
            for file_path in self._log_dir.glob("*.json.*"):
                if file_path.stat().st_mtime < cutoff_time.timestamp():
                    file_path.unlink()
                    count += 1
            
            logger.info(f"Cleaned up {count} old log files")
            return count
            
        except Exception as e:
            logger.error(f"Error cleaning up logs: {str(e)}")
            return 0
    
    def shutdown(self) -> None:
        """Shutdown the enhanced logger."""
        # Flush all handlers
        for handler in logging.getLogger().handlers:
            handler.flush()
        
        logger.info("Enhanced logger shut down")

# Create global instance
enhanced_logger = EnhancedLogger.get_instance()

# Convenience functions
def debug(message: str, **kwargs) -> None:
    """Log a debug message with context."""
    enhanced_logger.debug(message, **kwargs)

def info(message: str, **kwargs) -> None:
    """Log an info message with context."""
    enhanced_logger.info(message, **kwargs)

def warning(message: str, **kwargs) -> None:
    """Log a warning message with context."""
    enhanced_logger.warning(message, **kwargs)

def error(message: str, exc_info: bool = False, **kwargs) -> None:
    """Log an error message with context."""
    enhanced_logger.error(message, exc_info=exc_info, **kwargs)

def critical(message: str, exc_info: bool = False, **kwargs) -> None:
    """Log a critical message with context."""
    enhanced_logger.critical(message, exc_info=exc_info, **kwargs)

def set_context(**kwargs) -> None:
    """Set context values for the current thread."""
    enhanced_logger.set_context(**kwargs)

def with_context(**kwargs):
    """Decorator to set context for a function call."""
    return enhanced_logger.with_context(**kwargs)

def start_operation(operation: str, **context) -> PerformanceMetric:
    """Start tracking performance for an operation."""
    return enhanced_logger.start_operation(operation, **context)

def complete_operation(metric: PerformanceMetric, success: bool = True) -> None:
    """Complete performance tracking for an operation."""
    enhanced_logger.complete_operation(metric, success)

def get_performance_summary(operation: Optional[str] = None, minutes: int = 60) -> Dict[str, Any]:
    """Get performance summary statistics."""
    return enhanced_logger.get_performance_summary(operation, minutes)

def cleanup_logs(days: int = 30) -> int:
    """Clean up old log files."""
    return enhanced_logger.cleanup_logs(days)

# Try to import dependencies for integration
try:
    from utils.enhanced_error_handler import enhanced_error_handler
    enhanced_logger.set_error_handler(enhanced_error_handler)
except ImportError:
    logger.debug("Enhanced error handler not available for logger integration")

try:
    from utils.adaptive_resource_manager import adaptive_resource_manager
    enhanced_logger.set_resource_manager(adaptive_resource_manager)
except ImportError:
    logger.debug("Adaptive resource manager not available for logger integration")
