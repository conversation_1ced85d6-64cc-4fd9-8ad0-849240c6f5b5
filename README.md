# Advanced Trading Bot System

A sophisticated trading bot system that combines multiple machine learning models with robust error handling and resource management.

## Features

### Core Functionality
- Multi-terminal MT5 integration with connection pooling
- Advanced data preprocessing with custom technical indicators
- Ensemble model system with multiple ML models
- Real-time trading signal generation and execution
- Comprehensive error handling and recovery system
- Context-aware model loading and management (`ModelManager`)

### Technical Features
- Memory-optimized data processing
- Circuit breaker pattern for system protection
- Multi-threaded execution with resource management
- Real-time performance monitoring
- Automated recovery mechanisms
- Centralized configuration management (`ConfigurationManager`)
- Advanced training visualization system

### Machine Learning Models
- **ARIMA** (AutoRegressive Integrated Moving Average) - Terminal 1
- **LSTM** (Long Short-Term Memory) - Terminal 2
- **TFT** (Temporal Fusion Transformer) - Terminal 3
- **LSTM + ARIMA Ensemble** - Terminal 4
- **TFT + ARIMA Ensemble** - Terminal 5
- **XGBoost** (Gradient Boosting)
- **LightGBM** (Light Gradient Boosting)

## System Requirements

- Python 3.10+
- MetaTrader 5
- CUDA-capable GPU (optional)
- Minimum 8GB RAM
- Windows 10/11

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/trading-bot.git
cd trading-bot
```

2. Create and activate virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure MT5:
- Install MetaTrader 5
- Configure terminal settings (Paths, Login details)
- Update `config/config.json` with your MT5 terminal credentials and paths.

## Configuration

The main configuration is managed by `config/config.py` (`ConfigurationManager`) which reads `config/config.json`.

Key sections in `config/config.json`:

```json
{
  "mt5": {
    "max_connections": 5,
    "timeout": 60000,
    "retry_interval": 5,
    "terminals": {
      "1": {
        "login": "YOUR_LOGIN_1",
        "password": "YOUR_PASSWORD_1",
        "server": "YOUR_SERVER_1",
        "path": "C:/Path/To/MT5_Terminal_1/terminal64.exe"
      },
      "2": { ... }
    }
  },
  "strategy": {
    "symbol": "BTCUSD.a",
    "timeframes": ["M5", "M15"],
    "sequence_length": 288,
    "lot_size": 0.01,
    // ... other strategy parameters ...
  },
  "models": {
    "lstm": {
      "model_path": "lstm_model.pt", // <<< Filename only
      "input_dim": 50,
      "output_dim": 1,
      "weight": 0.2,
      "FEATURE_COLUMNS": [...],
      // ... other model params ...
    },
    "tft": {
      "model_path": "tft_model.pt", // <<< Filename only
       // ... other model params ...
    }
    // ... other models ...
  },
  "data_base_path": "data/", // <<< Base path for all data
  "models_base_path": "models/", // <<< Base path for all models
  "confidence_threshold": 0.65,
  "update_interval": 60,
  "max_memory_usage": 85.0,
  "log_level": "INFO",
  "debug_mode": false,
  "terminal_model_pairings": {
    "1": {
      "terminal_id": 1,
      "primary_model": "arima",
      "allocation": 20
    },
    "2": {
      "terminal_id": 2,
      "primary_model": "lstm",
      "allocation": 20
    },
    "3": {
      "terminal_id": 3,
      "primary_model": "tft",
      "allocation": 20
    },
    "4": {
      "terminal_id": 4,
      "primary_model": "lstm_arima",
      "allocation": 20
    },
    "5": {
      "terminal_id": 5,
      "primary_model": "tft_arima",
      "allocation": 20
    }
  }
}
```

**Important Notes:**
- `data_base_path` and `models_base_path` define the root directories for data and model files.
- `model_path` within each model's configuration should **only** be the filename. The full path (e.g., `models/terminal_1/M5/lstm_model.pt`) is constructed automatically by the system based on the `models_base_path`, the current `terminal_id`, and `timeframe`.

## Usage

1. **Configure the System:**
   - Update `config/config.json` with your MT5 terminal credentials and paths
   - Adjust strategy parameters, model configurations, and system settings as needed
   - Ensure all required directories exist (data, models, logs)

2. **Run the Trading Bot:**
   ```bash
   # Start the main trading system
   python main.py
   ```

3. **Monitor Performance:**
   - Check the logs directory for detailed logging information
   - Monitor system performance and resource usage
   - Track trading performance metrics

4. **View Logs:**
   ```bash
   # View the main log file
   tail -f logs/main.log

   # View the system log file
   tail -f logs/system.log

   # View the error log file
   tail -f logs/error.log
   ```

5. **Manage the System:**
   ```bash
   # Gracefully stop the trading bot
   # Press Ctrl+C in the terminal running main.py

   # Check system status
   python utils/system_status.py
   ```

6. **Advanced Usage:**
   ```bash
   # Run with specific terminal ID
   python main.py --terminal_id 1

   # Run with specific timeframe
   python main.py --timeframe M5

   # Run in debug mode
   python main.py --debug

   # Run with custom configuration file
   python main.py --config path/to/custom_config.json
   ```

## Project Structure

```
trading-bot/
├── config/             # Configuration files
│   ├── config.py       # Configuration utility classes
│   ├── config.json     # Main configuration file
│   └── consolidated_config.py # Consolidated configuration manager
├── data/               # Base directory for data (defined by data_base_path in config)
│   └── terminal_1/     # Data for specific terminals
│       └── M5/         # Data for specific timeframes
├── logs/               # Log files directory
├── models/             # Base directory for models (defined by models_base_path in config)
│   └── terminal_1/     # Models for specific terminals
│       └── M5/         # Models for specific timeframes
├── scripts/            # Utility scripts
├── tests/              # Test suite
├── trading/            # Trading logic
│   ├── bot.py          # Main trading bot implementation
│   ├── executor.py     # Trade execution logic
│   ├── mt5_connection_manager.py # Enhanced MT5 connection manager
│   ├── signal_generator.py # Trading signal generation
│   └── strategy.py     # Trading strategy implementation
├── utils/              # Utility functions & managers
│   ├── adaptive_resource_manager.py # Resource management
│   ├── circuit_breaker.py # Circuit breaker pattern implementation
│   ├── enhanced_error_handler.py # Enhanced error handling
│   ├── graceful_degradation.py # Graceful degradation implementation
│   ├── intelligent_cache.py # Intelligent caching system
│   ├── memory_manager.py # Memory management
│   └── model_manager.py # Model loading and management
├── main.py             # Main entry point to run the bot system
├── model_adapter.py    # Model input/output adaptation
└── requirements.txt    # Dependencies
```

## Error Handling

The system includes comprehensive error handling through the `EnhancedErrorHandler` class:

### Key Features

- **Contextual Error Handling**: Captures detailed context information for each error
- **Error Classification**: Categorizes errors for appropriate handling strategies
- **Recovery Mechanisms**: Implements specific recovery strategies for different error types
- **Circuit Breaker Integration**: Prevents cascading failures with circuit breaker pattern
- **Graceful Degradation**: Falls back to alternative approaches when primary methods fail
- **Detailed Logging**: Provides comprehensive error logs with context information
- **Error Rate Monitoring**: Tracks error rates and patterns for system health assessment

### Error Recovery Strategies

- **MT5 Connection Issues**: Automatic reconnection with exponential backoff
- **Data Processing Errors**: Fallback to alternative data sources or cached data
- **Model Errors**: Marking unhealthy models and using alternative models
- **Memory Issues**: Automatic cleanup and resource management
- **Network Timeouts**: Retry mechanisms with jitter to prevent thundering herd
- **System Overload**: Throttling and prioritization of critical operations

## Performance Monitoring

The system includes comprehensive performance monitoring capabilities:

### Trading Performance Metrics

- **Profit and Loss**: Real-time tracking of overall P&L
- **Win Rate**: Percentage of successful trades
- **Drawdown**: Maximum observed drawdown
- **Risk-Adjusted Returns**: Sharpe ratio and other risk-adjusted metrics
- **Trade Statistics**: Average trade duration, profit per trade, etc.

### System Performance Metrics

- **Resource Usage**: CPU, memory, and disk utilization
- **Response Times**: Latency for signal generation and trade execution
- **Throughput**: Number of operations processed per time unit
- **Error Rates**: Frequency and types of errors
- **Recovery Metrics**: Time to recover from failures

### Model Performance Metrics

- **Prediction Accuracy**: How well models predict market movements
- **Model Health**: Status of each model in the ensemble
- **Inference Time**: Time taken for model predictions
- **Feature Importance**: Contribution of different features to predictions
- **Ensemble Performance**: Effectiveness of model combination strategies

## Best Practices

### Configuration Management

- **Centralized Configuration**: Maintain all parameters in `config/config.json`
- **Configuration Access**: Use `ConfigurationManager` to access configuration values
- **Environment-Specific Settings**: Use environment variables for sensitive information
- **Configuration Validation**: Validate configuration values at startup
- **Default Values**: Provide sensible defaults for missing configuration parameters

### Error Handling

- **Contextual Error Handling**: Use `EnhancedErrorHandler` with detailed context information
- **Circuit Breaker Pattern**: Implement `CircuitBreaker` to prevent cascading failures
- **Graceful Degradation**: Provide fallback mechanisms for critical operations
- **Retry Mechanisms**: Use exponential backoff with jitter for retries
- **Error Classification**: Categorize errors for appropriate handling strategies

### Resource Management

- **Memory Monitoring**: Use `MemoryManager` to track and optimize memory usage
- **Resource Allocation**: Implement `AdaptiveResourceManager` for dynamic resource allocation
- **Connection Pooling**: Use connection pooling for external services
- **Caching**: Implement intelligent caching for frequently accessed data
- **Cleanup**: Ensure proper cleanup of resources after use

### Code Organization

- **Modularity**: Keep components decoupled with clear interfaces
- **Dependency Injection**: Pass dependencies explicitly rather than creating them internally
- **Manager Classes**: Use manager classes for shared resources (`ModelManager`, `MT5ConnectionManager`)
- **Consistent Naming**: Follow consistent naming conventions throughout the codebase
- **Documentation**: Document classes, methods, and complex logic

## Recent Improvements

1. **Codebase Cleanup and Optimization:**
   - Removed legacy and redundant files to simplify the codebase
   - Fixed inconsistencies in code organization and component structure
   - Standardized component structure and patterns
   - Deleted unused imports and components
   - Improved code readability and maintainability

2. **Enhanced Error Handling and Recovery:**
   - Fixed inconsistencies in error handling across the codebase
   - Standardized the use of EnhancedErrorHandler throughout the system
   - Improved circuit breaker implementation for better system protection
   - Added more robust error recovery mechanisms
   - Enhanced logging for better debugging and monitoring

3. **Fixed Critical Issues:**
   - Fixed MT5 data retrieval in TradingBot to properly handle market data
   - Improved signal handling in TradingStrategy to support both dictionary and object formats
   - Enhanced lot size calculation in TradeExecutor with better fallback mechanisms
   - Fixed account information handling to properly work with MT5's named tuples
   - Corrected market regime detection to properly map string values to enum types
   - Updated ModelManager to use EnhancedErrorHandler consistently

4. **Improved Model Management:**
   - Enhanced ModelInputAdapter for better handling of input shape mismatches
   - Improved model validation and health checking
   - Added better fallback mechanisms for model loading failures
   - Enhanced model weight management for better ensemble predictions
   - Improved model caching for better performance

5. **Robust Configuration Management:**
   - Centralized configuration via `ConfigurationManager` and `config.json`
   - Standardized path handling using `data_base_path` and `models_base_path`
   - Context-aware `ModelManager` for specific terminal/timeframe model loading
   - Improved configuration validation and error handling
   - Better handling of default values for missing configuration parameters

6. **Enhanced Trading Components:**
   - Improved TradingBot to better handle market data and model predictions
   - Enhanced SignalGenerator with better market regime detection and ensemble predictions
   - Improved TradeExecutor with better lot size calculation and error handling
   - Enhanced TradingStrategy with better signal handling and decision making
   - Improved MT5ConnectionManager for better terminal management

7. **System Stability & Consistency:**
   - Standardized error handling and logging across the codebase
   - Improved thread safety with proper locking mechanisms
   - Enhanced resource management for better system stability
   - Improved memory management with better cleanup mechanisms
   - Standardized component initialization and dependency injection

8. **Performance Optimizations:**
   - Improved data processing efficiency
   - Enhanced model prediction caching
   - Optimized memory usage with better resource management
   - Improved MT5 connection handling for better performance
   - Enhanced signal generation and trade execution for faster response times

9. **Australian English and Internationalization:**
   - Updated documentation and log messages to use Australian English
   - Improved error messages and user feedback
   - Enhanced logging for better clarity and consistency
   - Standardized terminology across the codebase

## Future Enhancements

1. **Planned Features:**
   - Advanced risk management with dynamic position sizing based on market volatility
   - Enhanced market regime detection with machine learning-based classification
   - Integration with Optuna for hyperparameter optimization
   - Improved backtesting framework with realistic slippage and spread modeling
   - Multi-asset portfolio management with correlation-based risk allocation

2. **System Improvements:**
   - GPU acceleration optimization for neural network models
   - Advanced caching system for preprocessed data and predictions
   - Automated model retraining pipeline with performance-based deployment
   - Enhanced monitoring dashboard with real-time performance metrics
   - Distributed computing support for model training and backtesting

3. **Data Management:**
   - Integration with Parquet file format for efficient data storage and retrieval
   - Real-time market data streaming with websocket connections
   - Advanced feature engineering pipeline with automated feature selection
   - Time-series specific data preprocessing optimizations
   - Enhanced data quality validation and cleaning procedures

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue in the GitHub repository or contact the maintainers.

## Training Visualization System

The trading bot includes a comprehensive visualization system for model training analysis, implemented in `visualize_training.py`.

### Key Features

- **Training Progress Visualization**: Shows training and validation loss curves for each model
- **Model Comparison**: Compares final validation loss across all models
- **Training Time Comparison**: Compares training duration across models
- **Interactive & Static Outputs**: Generates both HTML (interactive) and PNG (static) visualizations
- **Real-time Monitoring**: Supports monitoring of ongoing training with periodic updates
- **Realistic Synthetic Data**: Generates realistic training curves when detailed metrics are unavailable

### Usage

```bash
# Generate all visualizations
python visualize_training.py --log_file model_training.log

# Monitor training in real-time
python visualize_training.py --log_file model_training.log --monitor --interval 10

# Visualize specific models
python visualize_training.py --log_file model_training.log --models lstm gru xgboost
```

### Documentation

Detailed documentation is available in the `docs/` directory:

- `visualization_guide.md`: Comprehensive user guide
- `visualization_technical_reference.md`: Technical implementation details
- `visualization_quick_reference.md`: Quick reference for common tasks

## MT5 Connection Management

The trading bot implements a robust MT5 connection management system through the `MT5ConnectionManager` class, which provides reliable connection handling, automatic recovery, and efficient resource management.

### Key Features
- **Connection Pooling**: Maintains a pool of active connections to minimize initialization operations
- **Algorithmic Trading Preservation**: Ensures algorithmic trading functionality is preserved
- **Auto-Start Capability**: Can automatically start MT5 terminals if they are not running
- **Error Handling**: Comprehensive error handling with detailed logging
- **Health Checks**: Periodic health checks to ensure connections are working properly
- **Singleton Pattern**: Provides global access to the connection manager through `get_instance()`
- **Automatic Recovery**: Handles connection failures with intelligent retry mechanisms
- **Resource Management**: Properly manages MT5 resources to prevent memory leaks
- **Terminal Process Management**: Monitors and manages MT5 terminal processes
- **Graceful Shutdown**: Ensures proper cleanup of resources during system shutdown

### Configuration (`config.json`)

```json
{
  "mt5": {
    "max_connections": 5,
    "timeout": 60000,
    "retry_interval": 5,
    "max_retries": 3,
    "terminals": {
      "1": {
        "login": "YOUR_LOGIN",
        "password": "YOUR_PASSWORD",
        "server": "YOUR_SERVER",
        "path": "C:/Path/To/MT5/terminal64.exe"
      },
      "2": {
        "login": "YOUR_LOGIN_2",
        "password": "YOUR_PASSWORD_2",
        "server": "YOUR_SERVER_2",
        "path": "C:/Path/To/MT5_2/terminal64.exe"
      }
    }
  }
}
```

### Usage Best Practices

1. **Initialization**: Initialize `ImprovedMT5Manager` once in the main application and pass it to components
   ```python
   mt5_manager = ImprovedMT5Manager(config_manager, error_handler)
   ```

2. **Getting Connections**: Use the get_connection method to obtain a connection
   ```python
   connection = mt5_manager.get_connection(terminal_id)
   if connection and connection.is_connected:
       # Use the connection
   ```

3. **Error Handling**: Always check connection status before using it
   ```python
   connection = mt5_manager.get_connection(terminal_id)
   if not connection or not connection.is_connected:
       logger.error(f"MT5 connection not available for terminal {terminal_id}")
       return None
   ```

4. **Graceful Shutdown**: Always use the shutdown_all method during application exit
   ```python
   mt5_manager.shutdown_all()
   ```

## Model Adapter System

The trading bot implements a flexible model adapter system through the `ModelInputAdapter` class, which handles input/output transformations for different model architectures.

### Key Features

- **Input Shape Handling**: Automatically adapts input data to match model requirements
- **Multi-Model Support**: Handles different model architectures (LSTM, GRU, TFT, XGBoost, LightGBM)
- **Data Validation**: Validates input data and handles missing or invalid values
- **Feature Normalization**: Applies appropriate normalization based on model requirements
- **Error Handling**: Provides detailed error information for debugging model issues

### Supported Model Types

- **LSTM/GRU Models**: Handles 3D input shapes (samples, time steps, features)
- **TFT Models**: Supports Temporal Fusion Transformer architecture
- **XGBoost/LightGBM Models**: Handles 2D input shapes (samples, features)
- **Ensemble Models**: Combines predictions from multiple models

### Usage Example

```python
# Initialize the model adapter
model_adapter = ModelInputAdapter()

# Adapt input data for a specific model
adapted_data = model_adapter.adapt_input(
    data=market_data,
    model_type='lstm',
    input_shape=(None, 60, 30),
    feature_columns=feature_columns
)

# Make prediction with the adapted data
prediction = model.predict(adapted_data)

# Process the prediction
processed_prediction = model_adapter.process_prediction(
    prediction=prediction,
    model_type='lstm',
    threshold=0.5
)
```

## Signal Generator System

The trading bot implements a sophisticated signal generation system through the `SignalGenerator` class, which combines multiple models and market analysis techniques to generate trading signals.

### Key Features

- **Ensemble Model Predictions**: Combines predictions from multiple models with weighted averaging
- **Market Regime Detection**: Adapts signal generation based on current market conditions
- **Technical Indicator Analysis**: Incorporates traditional technical indicators for confirmation
- **Confidence Calculation**: Provides confidence scores for each trading signal
- **Risk Management**: Adjusts position sizing based on signal confidence and market volatility

### Signal Generation Process

1. **Data Preparation**: Preprocesses market data for model input
2. **Market Regime Detection**: Identifies current market conditions (trending, ranging, volatile)
3. **Model Predictions**: Obtains predictions from each model in the ensemble
4. **Ensemble Weighting**: Combines model predictions with regime-specific weights
5. **Signal Confirmation**: Confirms signals with technical indicators
6. **Confidence Calculation**: Determines confidence level for the signal
7. **Position Sizing**: Calculates appropriate position size based on confidence
8. **Signal Generation**: Creates the final trading signal with all necessary parameters

### Signal Structure

```python
{
    'action': 'buy',  # 'buy', 'sell', or 'hold'
    'confidence': 0.85,  # Confidence level (0.0-1.0)
    'entry_price': 50000.0,  # Suggested entry price
    'stop_loss': 49500.0,  # Suggested stop loss price
    'take_profit': 51000.0,  # Suggested take profit price
    'volume': 0.05,  # Suggested position size
    'symbol': 'BTCUSD.a',  # Trading symbol
    'comment': 'Strong uptrend detected',  # Signal description
    'market_regime': 'trending_up',  # Current market regime
    'timestamp': '2023-06-15 14:30:00'  # Signal generation time
}
```