#!/usr/bin/env python
"""
Dependency conflict resolution script.

This script helps resolve common dependency conflicts by upgrading
packages to compatible versions.
"""

import subprocess
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a command and handle errors."""
    logger.info(f"Running: {description}")
    logger.info(f"Command: {command}")

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"❌ {description} failed")
            logger.error(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        logger.error(f"❌ Exception during {description}: {str(e)}")
        return False

def fix_dependencies():
    """Fix dependency conflicts step by step."""
    logger.info("🔧 Starting dependency conflict resolution...")

    # Step 1: Upgrade typing-extensions first (most critical)
    logger.info("\n📦 Step 1: Upgrading typing-extensions...")
    if not run_command("pip install --upgrade typing-extensions>=4.12.2", "Upgrade typing-extensions"):
        logger.error("Failed to upgrade typing-extensions. Continuing anyway...")

    # Step 2: Upgrade numpy
    logger.info("\n📦 Step 2: Upgrading numpy...")
    if not run_command("pip install --upgrade numpy>=1.25.0", "Upgrade numpy"):
        logger.error("Failed to upgrade numpy. Continuing anyway...")

    # Step 3: Upgrade conflicting packages
    logger.info("\n📦 Step 3: Upgrading conflicting packages...")
    packages_to_upgrade = [
        "sqlalchemy>=2.0.38",
        "alembic>=1.15.2",
        "bayesian-optimization>=2.0.0",
        "mypy>=1.15.0",
        "pydantic>=2.10.6",
        "pydantic-core>=2.27.2"
    ]

    for package in packages_to_upgrade:
        if not run_command(f"pip install --upgrade {package}", f"Upgrade {package}"):
            logger.warning(f"Failed to upgrade {package}")

    # Step 4: Install/upgrade PyTorch with CUDA support
    logger.info("\n📦 Step 4: Installing PyTorch with CUDA support...")
    torch_command = "pip install torch>=2.5.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121"
    if not run_command(torch_command, "Install PyTorch with CUDA"):
        logger.warning("Failed to install PyTorch with CUDA. Trying CPU version...")
        if not run_command("pip install --upgrade torch>=2.5.0", "Install PyTorch CPU"):
            logger.error("Failed to install PyTorch")

    # Step 5: Install additional PyTorch ecosystem packages
    logger.info("\n📦 Step 5: Installing additional PyTorch ecosystem packages...")
    pytorch_packages = [
        "pytorch-lightning>=2.0.0",
        "pytorch-forecasting>=1.0.0",
        "torchmetrics>=1.0.0"
    ]

    for package in pytorch_packages:
        if not run_command(f"pip install --upgrade {package}", f"Install {package}"):
            logger.warning(f"Failed to install {package}")

    # Step 6: Check for remaining conflicts
    logger.info("\n🔍 Step 6: Checking for remaining conflicts...")
    run_command("pip check", "Check for dependency conflicts")

    logger.info("\n✅ Dependency resolution completed!")
    logger.info("If there are still conflicts, try running: pip install --upgrade --force-reinstall -r requirements.txt")

def main():
    """Main function."""
    logger.info("🚀 Dependency Conflict Resolution Tool")
    logger.info("=====================================")

    try:
        fix_dependencies()
    except KeyboardInterrupt:
        logger.info("\n⚠️  Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()

