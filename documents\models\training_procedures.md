# Model Training Procedures

## Training Overview

The system supports training **9 different model types** across **5 timeframes** using standardized procedures with full customization support.

### Available Models
1. **lstm** - Long Short-Term Memory neural network
2. **gru** - Gated Recurrent Unit neural network
3. **transformer** - Transformer neural network
4. **tft** - Temporal Fusion Transformer
5. **xgboost** - XGBoost gradient boosting
6. **lightgbm** - LightGBM gradient boosting
7. **arima** - ARIMA time series model
8. **lstm_arima** - LSTM + ARIMA ensemble
9. **tft_arima** - TFT + ARIMA ensemble

## Training Commands

### Basic Individual Model Training
```bash
# Train each model individually with default parameters
python train_models.py --models lstm
python train_models.py --models gru
python train_models.py --models transformer
python train_models.py --models tft
python train_models.py --models xgboost
python train_models.py --models lightgbm
python train_models.py --models arima
python train_models.py --models lstm_arima
python train_models.py --models tft_arima

# Train all models with default parameters
python train_models.py

# Use custom configuration
python train_models.py --config custom_config.json

# Force retraining
python train_models.py --force
```

### Individual Model Training with Custom Parameters

#### 1. LSTM Model
```bash
# Basic LSTM training
python train_models.py --models lstm

# LSTM with custom parameters
python train_models.py --models lstm --epochs 200 --batch-size 128 --learning-rate 0.0005 --sequence-length 120 --dropout-rate 0.3

# LSTM for different timeframe/symbol
python train_models.py --models lstm --timeframe H1 --symbol EURUSD.a --epochs 150
```

#### 2. GRU Model
```bash
# Basic GRU training
python train_models.py --models gru

# GRU with custom parameters
python train_models.py --models gru --epochs 150 --batch-size 64 --learning-rate 0.001 --patience 15 --dropout-rate 0.25

# GRU for different timeframe
python train_models.py --models gru --timeframe M15 --epochs 100 --sequence-length 90
```

#### 3. Transformer Model
```bash
# Basic Transformer training
python train_models.py --models transformer

# Transformer with custom parameters
python train_models.py --models transformer --epochs 200 --batch-size 32 --learning-rate 0.0001 --sequence-length 60 --dropout-rate 0.15

# Transformer for different symbol
python train_models.py --models transformer --symbol EURUSD.a --timeframe H1 --epochs 250
```

#### 4. TFT Model
```bash
# Basic TFT training
python train_models.py --models tft

# TFT with custom parameters and memory optimization
python train_models.py --models tft --epochs 50 --batch-size 64 --learning-rate 0.03 --max-samples-per-dataset 50000

# TFT with large dataset (disable memory optimization)
python train_models.py --models tft --epochs 100 --max-samples-per-dataset 100000 --enable-memory-optimization false

# TFT for different timeframe with memory management
python train_models.py --models tft --timeframe M30 --epochs 50 --batch-size 32 --max-samples-per-dataset 75000
```

#### 5. XGBoost Model
```bash
# Basic XGBoost training
python train_models.py --models xgboost

# XGBoost with custom parameters (epochs becomes n_estimators)
python train_models.py --models xgboost --epochs 200 --learning-rate 0.01

# XGBoost for different symbol/timeframe
python train_models.py --models xgboost --symbol EURUSD.a --timeframe M15 --epochs 300
```

#### 6. LightGBM Model
```bash
# Basic LightGBM training
python train_models.py --models lightgbm

# LightGBM with custom parameters
python train_models.py --models lightgbm --epochs 150 --learning-rate 0.05

# LightGBM for different timeframe
python train_models.py --models lightgbm --timeframe H1 --epochs 250 --symbol BTCUSD.a
```

#### 7. ARIMA Model
```bash
# Basic ARIMA training
python train_models.py --models arima

# ARIMA for different timeframe/symbol
python train_models.py --models arima --timeframe H1 --symbol EURUSD.a

# ARIMA with different symbol
python train_models.py --models arima --symbol BTCUSD.a --timeframe M30
```

#### 8. LSTM-ARIMA Ensemble
```bash
# Basic LSTM-ARIMA ensemble training
python train_models.py --models lstm_arima

# LSTM-ARIMA with custom parameters
python train_models.py --models lstm_arima --epochs 100 --batch-size 64 --learning-rate 0.001

# LSTM-ARIMA for different timeframe
python train_models.py --models lstm_arima --timeframe M15 --epochs 150
```

#### 9. TFT-ARIMA Ensemble
```bash
# Basic TFT-ARIMA ensemble training
python train_models.py --models tft_arima

# TFT-ARIMA with custom parameters
python train_models.py --models tft_arima --epochs 150 --batch-size 32 --learning-rate 0.001

# TFT-ARIMA for different timeframe
python train_models.py --models tft_arima --timeframe H1 --epochs 200
```

### Advanced Training Options

#### Multiple Models with Same Parameters
```bash
# Train multiple models with same custom parameters
python train_models.py --models lstm gru transformer --epochs 200 --batch-size 128

# Train all neural networks
python train_models.py --models lstm gru transformer tft --learning-rate 0.0005 --epochs 150

# Train all tree models
python train_models.py --models xgboost lightgbm --epochs 300 --learning-rate 0.01

# Train all ensemble models
python train_models.py --models lstm_arima tft_arima --epochs 100
```

#### PyTorch-Specific Training (Alternative Script)
```bash
# Train specific PyTorch model with TensorBoard
python train_with_pytorch.py --model lstm --epochs 100 --tensorboard

# Train TFT with custom parameters
python train_with_pytorch.py --model tft --epochs 50 --batch-size 16 --learning-rate 0.03
```

## Available Custom Parameters

### Parameter Reference Table

| Parameter | Description | Applicable Models | Example Values |
|-----------|-------------|-------------------|----------------|
| `--epochs` | Number of training epochs | All models | `50`, `100`, `200` |
| `--batch-size` | Training batch size | Neural networks | `16`, `32`, `64`, `128` |
| `--learning-rate` | Learning rate | All models | `0.001`, `0.01`, `0.03` |
| `--patience` | Early stopping patience | Neural networks | `10`, `15`, `20` |
| `--sequence-length` | Input sequence length | Sequence models | `30`, `60`, `120` |
| `--dropout-rate` | Dropout rate | Neural networks | `0.1`, `0.2`, `0.3` |
| `--max-samples-per-dataset` | Max samples for TFT memory optimization | TFT model | `10000`, `50000`, `100000` |
| `--enable-memory-optimization` | Enable/disable TFT memory optimization | TFT model | `true`, `false` |
| `--symbol` | Trading symbol | All models | `BTCUSD.a`, `EURUSD.a` |
| `--timeframe` | Data timeframe | All models | `M5`, `M15`, `M30`, `H1`, `H4` |
| `--validation-split` | Validation split ratio | All models | `0.15`, `0.2`, `0.25` |
| `--max-workers` | Maximum concurrent processes | All models | `1`, `2`, `3` |
| `--config` | Custom configuration file | All models | `custom_config.json` |
| `--force` | Force retraining | All models | (flag, no value) |

### Command Line Parameters
```bash
--epochs INT                      # Override epochs for training
--batch-size INT                  # Override batch size for training
--learning-rate FLOAT             # Override learning rate for training
--patience INT                    # Override patience for early stopping
--symbol STR                      # Trading symbol for training data (default: BTCUSD.a)
--timeframe STR                   # Timeframe for training data (default: M5)
--sequence-length INT             # Override sequence length for models
--dropout-rate FLOAT              # Override dropout rate for neural networks
--validation-split FLOAT          # Override validation split ratio
--max-samples-per-dataset INT     # Override max samples for TFT memory optimization
--enable-memory-optimization STR  # Enable/disable TFT memory optimization (true/false)
--max-workers INT                 # Maximum concurrent training processes (default: 2)
--config STR                      # Path to custom configuration file
--force                           # Force retraining even if models exist
```

### Timeframe Options
- **M5** - 5-minute timeframe (default)
- **M15** - 15-minute timeframe
- **M30** - 30-minute timeframe
- **H1** - 1-hour timeframe
- **H4** - 4-hour timeframe

### Symbol Options
- **BTCUSD.a** - Bitcoin/USD (default)
- **EURUSD.a** - Euro/USD
- Any other symbol available in your MT5 data

## Model Training Pipeline

### 1. Data Preparation
```python
def prepare_model_data(data, model_config):
    # Extract features based on model configuration
    feature_columns = model_config.FEATURE_COLUMNS
    X_train = data['train'][feature_columns].values
    y_train = data['train']['close'].values

    # Handle different model input requirements
    if model_config.model_type in ['xgboost', 'lightgbm']:
        # Tree models use flattened features
        target_shape = (len(feature_columns),)
    else:
        # Sequence models use time series format
        target_shape = (model_config.sequence_length, len(feature_columns))

    # Adapt input shapes with custom parameters
    X_train_adapted = ModelInputAdapter.adapt_input(X_train, target_shape)
    return X_train_adapted, y_train
```

### 2. Model Initialization with Custom Parameters
```python
def initialize_model(model_name, model_config, custom_params=None):
    # Get model class
    model_class = get_model_class(model_config.model_type)

    # Create configuration dictionary
    config_dict = {
        'model_name': model_name,
        'model_type': model_config.model_type,
        'input_dim': calculate_input_dim(model_config),
        'output_dim': 1,
        **model_config.__dict__
    }

    # Apply custom parameter overrides
    if custom_params:
        for param_name, param_value in custom_params.items():
            if param_name in ['epochs', 'batch_size', 'learning_rate', 'patience',
                            'sequence_length', 'dropout_rate']:
                config_dict[param_name] = param_value
                logger.info(f"Overriding {param_name} for {model_name}: {param_value}")

    # Initialize and build model
    model = model_class(config_dict)
    model.build()
    return model
```

### 3. Training Execution with Custom Parameters
```python
def train_single_model(model_name, model_config, data, custom_params=None):
    # Prepare data
    model_data = prepare_model_data(data, model_config)

    # Initialize model with custom parameters
    model = initialize_model(model_name, model_config, custom_params)

    # Prepare training parameters with custom overrides
    training_params = {
        'X_train': model_data['X_train'],
        'y_train': model_data['y_train'],
        'X_val': model_data['X_val'],
        'y_val': model_data['y_val'],
        'epochs': custom_params.get('epochs', model_config.epochs) if custom_params else model_config.epochs,
        'batch_size': custom_params.get('batch_size', model_config.batch_size) if custom_params else model_config.batch_size,
        'patience': custom_params.get('patience', model_config.patience) if custom_params else model_config.patience,
        'verbose': 1
    }

    # Train with validation
    training_result = model.train_with_validation(**training_params)

    # Evaluate on test data
    test_predictions = model.predict(model_data['X_test'])
    test_metrics = calculate_test_metrics(model_data['y_test'], test_predictions)

    # Save model
    model.save()

    return {
        'success': True,
        'training_metrics': training_result,
        'test_metrics': test_metrics,
        'model_path': model.model_path,
        'custom_params_used': custom_params
    }
```

## Model-Specific Training Examples

### Neural Network Models (LSTM, GRU, Transformer, TFT)

**Framework**: PyTorch
**Input Format**: 3D tensors (batch_size, sequence_length, features)

#### LSTM Model Training
```bash
# Basic LSTM training
python train_models.py --models lstm

# LSTM with custom architecture
python train_models.py --models lstm --epochs 150 --batch-size 64 --sequence-length 120 --dropout-rate 0.2

# LSTM for different timeframes
python train_models.py --models lstm --timeframe H1 --epochs 200 --learning-rate 0.0005
```

#### GRU Model Training
```bash
# Basic GRU training
python train_models.py --models gru

# GRU with custom parameters
python train_models.py --models gru --epochs 100 --batch-size 32 --learning-rate 0.001 --patience 15
```

#### Transformer Model Training
```bash
# Basic Transformer training
python train_models.py --models transformer

# Transformer with attention parameters
python train_models.py --models transformer --epochs 200 --learning-rate 0.0001 --batch-size 32 --sequence-length 60
```

#### TFT Model Training
```bash
# Basic TFT training
python train_models.py --models tft

# TFT with memory optimization (recommended for large datasets)
python train_models.py --models tft --epochs 50 --max-samples-per-dataset 50000 --batch-size 64

# TFT with full dataset (disable memory optimization)
python train_models.py --models tft --epochs 100 --enable-memory-optimization false --batch-size 32

# TFT with custom temporal parameters
python train_models.py --models tft --epochs 50 --sequence-length 60 --max-samples-per-dataset 75000
```

### Tree-Based Models (XGBoost, LightGBM)

**Framework**: Native implementations
**Input Format**: 2D arrays (samples, features)

#### XGBoost Model Training
```bash
# Basic XGBoost training
python train_models.py --models xgboost

# XGBoost with custom parameters (epochs becomes n_estimators)
python train_models.py --models xgboost --epochs 200 --learning-rate 0.01

# XGBoost for different symbols
python train_models.py --models xgboost --symbol EURUSD.a --timeframe M15
```

#### LightGBM Model Training
```bash
# Basic LightGBM training
python train_models.py --models lightgbm

# LightGBM with custom parameters
python train_models.py --models lightgbm --epochs 150 --learning-rate 0.05 --timeframe H1
```

### Time Series Models (ARIMA)

**Framework**: Statsmodels
**Input Format**: 1D time series

#### ARIMA Model Training
```bash
# Basic ARIMA training
python train_models.py --models arima

# ARIMA for different timeframes
python train_models.py --models arima --timeframe H1 --symbol BTCUSD.a
```

### Ensemble Models

**Framework**: Combined models
**Input Format**: Multiple model outputs

#### LSTM-ARIMA Ensemble Training
```bash
# Basic LSTM-ARIMA ensemble
python train_models.py --models lstm_arima

# LSTM-ARIMA with custom parameters
python train_models.py --models lstm_arima --epochs 100 --timeframe M15 --batch-size 64
```

#### TFT-ARIMA Ensemble Training
```bash
# Basic TFT-ARIMA ensemble
python train_models.py --models tft_arima

# TFT-ARIMA with custom parameters
python train_models.py --models tft_arima --epochs 150 --batch-size 64 --learning-rate 0.001
```

## Performance Metrics

### Training Metrics
- **Loss**: Training and validation loss curves
- **Convergence**: Training convergence analysis
- **Overfitting**: Training vs validation performance gap

### Test Metrics
- **MAE**: Mean Absolute Error
- **MSE**: Mean Squared Error
- **RMSE**: Root Mean Squared Error
- **R²**: R-squared Score
- **Directional Accuracy**: Prediction direction accuracy

### Calculation Example
```python
def calculate_test_metrics(y_true, y_pred):
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

    metrics = {
        'mae': float(mean_absolute_error(y_true, y_pred)),
        'mse': float(mean_squared_error(y_true, y_pred)),
        'rmse': float(np.sqrt(mean_squared_error(y_true, y_pred))),
        'r2': float(r2_score(y_true, y_pred))
    }

    # Directional accuracy
    if len(y_true) > 1:
        true_direction = np.diff(y_true)
        pred_direction = np.diff(y_pred)
        directional_accuracy = np.mean((true_direction * pred_direction) > 0)
        metrics['directional_accuracy'] = float(directional_accuracy)

    return metrics
```

## Training Output

### Output Directory Structure
```
training_results/YYYYMMDD_HHMMSS/
├── training_report.json          # Comprehensive training report
├── lstm_training_log.csv         # Training metrics per epoch
├── gru_training_log.csv          # Training metrics per epoch
├── transformer_training_log.csv  # Training metrics per epoch
├── checkpoints/                  # Model checkpoints
│   ├── lstm/
│   │   └── best_model.pt
│   ├── transformer/
│   │   └── best_model.pt
│   └── ...
└── tensorboard/                  # TensorBoard logs (PyTorch Lightning)
    ├── lstm/
    ├── transformer/
    └── ...
```

### Model Storage Paths
```
models/
├── saved_models/
│   ├── lstm/
│   │   ├── terminal_1/
│   │   │   ├── M5/
│   │   │   │   ├── BTCUSD.a_M5_lstm_model.pt
│   │   │   │   └── BTCUSD.a_M5_lstm_metrics.json
│   │   │   ├── M15/
│   │   │   ├── M30/
│   │   │   ├── H1/
│   │   │   └── H4/
│   │   └── terminal_2/
│   ├── gru/
│   ├── transformer/
│   ├── tft/
│   ├── xgboost/
│   ├── lightgbm/
│   ├── arima/
│   └── ensemble/
│       ├── lstm_arima/
│       └── tft_arima/
└── ...
```

### Model File Types
- **Neural Networks**: `.pt`, `.pth` files (PyTorch models and state dictionaries)
- **Tree Models**: `.json` (XGBoost), `.txt` (LightGBM)
- **Time Series**: `.pkl` (ARIMA, Ensemble models)
- **Metrics**: `.json` files with performance metrics
- **Scalers**: `.joblib` files with feature scalers
- **Legacy**: `.h5` files (deprecated TensorFlow/Keras models - retrain with PyTorch)

## Advanced Training Features

### Parallel Training
```bash
# Control concurrent training processes
python train_models.py --models lstm gru transformer --max-workers 3

# Train multiple models with different parameters
python train_models.py --models lstm gru --epochs 200 --max-workers 2
```

### TensorBoard Integration (PyTorch Lightning)
```bash
# Train with TensorBoard monitoring (PyTorch models)
python train_with_pytorch.py --model lstm --epochs 100 --tensorboard

# View TensorBoard logs
tensorboard --logdir=training_results/YYYYMMDD_HHMMSS/tensorboard/
```

### Configuration Override
```bash
# Use custom configuration file
python train_models.py --config custom_config.json --models lstm gru

# Force retraining even if models exist
python train_models.py --models lstm --force --epochs 200
```

## Training Reports

### Comprehensive Training Report
```json
{
    "training_session": {
        "timestamp": "2024-01-01T12:00:00",
        "total_models": 9,
        "successful_models": 8,
        "failed_models": 1,
        "failed_model_names": ["arima"]
    },
    "model_results": {
        "lstm": {
            "success": true,
            "model_name": "lstm",
            "training_metrics": {
                "final_loss": 0.0123,
                "final_val_loss": 0.0145
            },
            "test_metrics": {
                "mae": 0.0234,
                "mse": 0.0005,
                "rmse": 0.0224,
                "r2": 0.8765,
                "directional_accuracy": 0.6543
            },
            "training_duration": 1234.56,
            "model_saved": true,
            "model_path": "models/saved_models/lstm/terminal_1/M5/BTCUSD.a_M5_lstm_model.pt",
            "custom_params_used": {
                "epochs": 200,
                "batch_size": 128
            }
        }
    },
    "summary_statistics": {
        "avg_mae": 0.0245,
        "avg_rmse": 0.0312,
        "avg_r2": 0.8234,
        "total_training_time": 9876.54
    }
}
```

## Troubleshooting

### Common Training Issues

#### Memory Errors
```bash
# Reduce batch size
python train_models.py --models lstm --batch-size 32

# Reduce sequence length
python train_models.py --models transformer --sequence-length 30
```

#### Convergence Issues
```bash
# Adjust learning rate
python train_models.py --models gru --learning-rate 0.0001

# Increase patience
python train_models.py --models tft --patience 20
```

#### Overfitting
```bash
# Increase dropout
python train_models.py --models lstm --dropout-rate 0.4

# Reduce epochs
python train_models.py --models transformer --epochs 50
```

#### Data Issues
```bash
# Check different timeframes
python train_models.py --models lstm --timeframe M15

# Try different symbols
python train_models.py --models gru --symbol EURUSD.a
```

### Performance Optimization

#### GPU Utilization
- Use PyTorch with CUDA for neural networks
- Enable mixed precision training with PyTorch AMP
- Optimize batch sizes for GPU memory

#### Memory Management
- Monitor memory usage during training
- Use gradient accumulation for large models
- Implement data streaming for large datasets

#### Training Speed
- Use parallel training for multiple models
- Optimize data loading pipelines
- Use appropriate batch sizes

### Model-Specific Troubleshooting

#### Neural Networks (LSTM, GRU, Transformer, TFT)
- **Vanishing gradients**: Use gradient clipping
- **Exploding gradients**: Reduce learning rate
- **Poor convergence**: Try different optimizers

#### Tree Models (XGBoost, LightGBM)
- **Overfitting**: Reduce max_depth or increase regularization
- **Underfitting**: Increase n_estimators or max_depth
- **Memory issues**: Reduce feature count or use sampling

#### Time Series (ARIMA)
- **Non-stationarity**: Check data preprocessing
- **Poor fit**: Adjust ARIMA order parameters
- **Convergence issues**: Try different optimization methods

#### Ensemble Models
- **Imbalanced contributions**: Adjust model weights
- **Poor ensemble performance**: Check individual model quality
- **Training conflicts**: Train base models separately first
