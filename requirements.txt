# Core dependencies
numpy>=1.25.0
pandas>=1.3.0
typing-extensions>=4.12.2
scikit-learn>=0.24.2
xgboost>=1.4.2
lightgbm>=3.3.0
MetaTrader5>=5.0.34
matplotlib>=3.4.3
seaborn>=0.11.2
dash>=2.0.0
plotly>=5.3.1
bayesian-optimization>=2.0.0
psutil>=5.8.0
GPUtil>=1.4.0
pytest>=6.2.5
pytest-cov>=2.12.1
black>=21.9b0
flake8>=3.9.2
mypy>=1.15.0
isort>=5.9.3
pre-commit>=2.13.0

# PyTorch ecosystem (primary ML framework) - Latest with CUDA support
torch>=2.5.0
torchvision>=0.20.0
torchaudio>=2.5.0
pytorch-lightning>=2.0.0
pytorch-forecasting>=1.0.0
torchmetrics>=1.0.0

# Additional dependencies
ta>=0.7.0  # Technical analysis library
python-telegram-bot>=13.7  # For Telegram notifications
statsmodels>=0.13.0  # For ARIMA models
joblib>=1.2.0  # For model serialization and parallel processing
tqdm>=4.65.0  # Progress bars
optuna>=3.1.0  # Hyperparameter optimization
h5py>=3.8.0  # HDF5 support
pyyaml>=6.0  # Configuration files
pyarrow>=12.0.0  # Parquet file support

# Additional system dependencies
requests>=2.28.0  # HTTP requests
websocket-client>=1.4.0  # WebSocket support
python-dateutil>=2.8.0  # Date utilities

