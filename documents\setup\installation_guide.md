# Installation Guide

## System Requirements

### Hardware Requirements
- **CPU**: Multi-core processor (8+ cores recommended)
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 100GB+ free space for data and models
- **GPU**: NVIDIA GPU with CUDA support (optional but recommended)

### Software Requirements
- **OS**: Windows 10/11 (for MT5 compatibility)
- **Python**: 3.8+ (3.10 recommended)
- **MetaTrader 5**: Latest version installed

## Installation Steps

### 1. Clone Repository
```bash
git clone <repository-url>
cd trading-bot
```

### 2. Create Virtual Environment
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

**Key Dependencies Installed:**
- **PyTorch**: 2.5.1+cu121 (with CUDA support)
- **PyTorch Lightning**: 2.5.1 (for TFT model training)
- **PyTorch Forecasting**: 1.3.0 (for TFT implementation)
- **MetaTrader5**: Latest (for MT5 integration)
- **XGBoost**: Latest (for gradient boosting)
- **LightGBM**: Latest (for gradient boosting)
- **Scikit-learn**: Latest (for preprocessing and metrics)
- **Pandas/Numpy**: Latest (for data processing)

### 4. Configure MT5 Terminals
1. Install MT5 terminals in separate directories (actual paths from codebase):
   - Terminal 1: `C:/Users/<USER>/Desktop/MT5 Pepper 03/terminal64.exe`
   - Terminal 2: `C:/Users/<USER>/Desktop/MT5 Pepper 02/terminal64.exe`
   - Terminal 3: `C:/Users/<USER>/Desktop/MT5 IC 01/terminal64.exe`
   - Terminal 4: `C:/Users/<USER>/Desktop/MT5 IC 02/terminal64.exe`
   - Terminal 5: `C:/Users/<USER>/Desktop/MT5 IC 03/terminal64.exe`

2. Configure each terminal with broker credentials

### 5. Create Directory Structure
```bash
mkdir -p data/storage/historical
mkdir -p models/terminal_1/M5 models/terminal_2/M5 models/terminal_3/M5
mkdir -p models/terminal_4/M5 models/terminal_5/M5
mkdir -p logs training_results
mkdir -p reports
```

### 6. Configuration Setup
Copy `config/config.json.example` to `config/config.json` and update with your settings.

## Verification

Run the installation verification:
```bash
# Test core dependencies
python -c "import MetaTrader5; print('MT5 OK')"
python -c "import torch; print(f'PyTorch {torch.__version__} OK')"
python -c "import pytorch_lightning; print(f'PyTorch Lightning {pytorch_lightning.__version__} OK')"
python -c "import pytorch_forecasting; print(f'PyTorch Forecasting {pytorch_forecasting.__version__} OK')"

# Test configuration system
python -c "from config.consolidated_config import ConfigurationManager; print('Config OK')"

# Test model imports
python -c "from models import LSTMModel, TFTModel, XGBoostModel; print('Models OK')"

# Test CUDA availability
python -c "import torch; print(f'CUDA Available: {torch.cuda.is_available()}')"
```

## Troubleshooting

### Common Issues
1. **MT5 Connection Failed**:
   - Check terminal paths in config.json
   - Verify MT5 terminals are properly installed
   - Ensure broker credentials are correct

2. **CUDA Not Available**:
   - Install NVIDIA drivers (latest)
   - Install CUDA toolkit 12.1+
   - Verify GPU compatibility

3. **PyTorch Forecasting Import Errors**:
   - Ensure PyTorch Lightning 2.x compatibility
   - Check version compatibility: `pip list | grep pytorch`

4. **Permission Errors**:
   - Run as administrator if needed
   - Check file/folder permissions

5. **TFT Model Training Issues**:
   - Verify PyTorch Lightning 2.x imports
   - Check device compatibility (CPU/GPU)
   - Ensure sufficient memory for batch processing
