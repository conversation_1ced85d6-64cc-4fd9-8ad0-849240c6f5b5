{"mt5": {"max_connections": 5, "timeout": 60000, "retry_interval": 5, "auto_start_terminals": true, "terminals": {"1": {"login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "YOUR_SERVER", "path": "C:/Path/To/MT5/terminal64.exe", "trade_mode": true, "auto_trading": true}}}, "strategy": {"symbol": "BTCUSD.a", "timeframes": ["M5", "M15", "M30", "H1", "H4"], "primary_timeframe": "M5", "sequence_length": 288, "lot_size": 0.01, "max_positions": 2, "stop_loss_pips": 200, "take_profit_pips": 400, "max_spread_pips": 50, "risk_per_trade": 0.01, "max_daily_loss": 50.0, "max_daily_trades": 5, "cooldown_period": 600, "volatility_threshold": 2.0, "trend_threshold": 0.7, "position_sizing_factor": 0.5}, "models": {"lstm": {"model_path": "lstm_model.pt", "model_type": "lstm", "input_dim": 50, "output_dim": 1, "weight": 0.2, "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "gru": {"model_path": "gru_model.pt", "model_type": "gru", "input_dim": 50, "output_dim": 1, "weight": 0.2, "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "xgboost": {"model_path": "xgboost_model.json", "model_type": "xgboost", "input_dim": 50, "output_dim": 1, "weight": 0.2, "sequence_length": 100, "uses_flattened_sequence": true, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "learning_rate": 0.01, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "lightgbm": {"model_path": "lightgbm_model.txt", "model_type": "lightgbm", "input_dim": 50, "output_dim": 1, "weight": 0.2, "sequence_length": 100, "uses_flattened_sequence": true, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "num_leaves": 31, "learning_rate": 0.01, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "transformer": {"model_path": "transformer_model.pt", "model_type": "transformer", "input_dim": 50, "output_dim": 1, "weight": 0.1, "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.1, "d_model": 128, "num_heads": 8, "ff_dim": 256, "num_layers": 4, "max_position_embeddings": 100, "attention_dropout": 0.1, "layer_norm_eps": 1e-05, "activation": "gelu", "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "tft": {"model_path": "tft_model.pt", "model_type": "tft", "input_dim": 50, "output_dim": 1, "weight": 0.1, "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.1, "hidden_size": 32, "attention_head_size": 4, "hidden_continuous_size": 16, "max_encoder_length": 100, "max_prediction_length": 1, "max_samples_per_dataset": 10000, "enable_memory_optimization": true, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "arima": {"model_path": "arima_model.pkl", "model_type": "arima", "input_dim": 1, "output_dim": 1, "weight": 0.2, "order": [1, 1, 1], "seasonal_order": [0, 0, 0, 0], "trend": "c", "FEATURE_COLUMNS": ["close"]}, "lstm_arima": {"model_path": "lstm_arima_ensemble.pkl", "model_type": "lstm_arima", "input_dim": 50, "output_dim": 1, "weight": 0.2, "ensemble_method": "weighted_average", "model_weights": {"lstm": 0.7, "arima": 0.3}, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "tft_arima": {"model_path": "tft_arima_ensemble.pkl", "model_type": "tft_arima", "input_dim": 50, "output_dim": 1, "weight": 0.2, "ensemble_method": "weighted_average", "model_weights": {"tft": 0.8, "arima": 0.2}, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}}, "data_base_path": "data/", "models_base_path": "models/", "confidence_threshold": 0.65, "update_interval": 60, "max_memory_usage": 85.0, "log_level": "INFO", "debug_mode": false, "terminal_model_pairings": {"1": {"terminal_id": 1, "primary_model": "lstm", "allocation": 20}}, "training": {"symbols": ["BTCUSD.a"], "timeframes": ["M5", "M15", "M30", "H1", "H4"], "models_to_train": ["lstm", "gru", "xgboost", "lightgbm", "transformer", "tft", "arima", "lstm_arima", "tft_arima"], "days": 30, "validation_split_ratio": 0.2}, "model_selection": {"primary_model": "ensemble", "fallback_model": "lstm", "ensemble_weights": {"lstm": 0.2, "gru": 0.2, "tft": 0.2, "xgboost": 0.2, "lightgbm": 0.2}}, "system": {"log_level": "INFO", "log_file": "logs/system.log", "min_memory_gb": 4, "environment": "development", "data_dir": "data/", "models_dir": "models/", "reports_dir": "reports"}, "monitoring": {"report_interval": 10, "metrics_interval": 5, "plot_interval": 10, "save_interval": 10, "output_dir": "monitoring", "plots_dir": "plots", "reports_dir": "reports", "max_log_files": 10, "max_log_size_mb": 100}}