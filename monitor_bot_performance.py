#!/usr/bin/env python3
"""
Trading Bot Performance Monitor
==============================

This script provides real-time monitoring and visualization of trading bot performance.
It reads performance data from the monitoring directory and generates live dashboards.

Usage:
    # Monitor all terminals
    python monitor_bot_performance.py

    # Monitor specific terminal
    python monitor_bot_performance.py --terminal 1

    # Web dashboard mode
    python monitor_bot_performance.py --web --port 8080
"""

import argparse
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional

import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BotPerformanceMonitor:
    """Real-time trading bot performance monitor."""

    def __init__(self, monitoring_dir: str = "monitoring"):
        """Initialize the performance monitor."""
        self.monitoring_dir = Path(monitoring_dir)
        self.output_dir = Path("monitoring/dashboard")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Performance data storage
        self.terminal_data = {}
        self.last_update = {}

        logger.info(f"Performance monitor initialized. Monitoring directory: {self.monitoring_dir}")

    def load_terminal_data(self, terminal_id: str) -> Dict:
        """Load performance data for a specific terminal."""
        try:
            terminal_dir = self.monitoring_dir / f"terminal_{terminal_id}"
            if not terminal_dir.exists():
                logger.warning(f"Terminal directory not found: {terminal_dir}")
                return {}

            data = {}

            # Load metrics history
            metrics_file = terminal_dir / "metrics_history.json"
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    data['metrics'] = json.load(f)

            # Load trade history
            trades_file = terminal_dir / "trade_history.json"
            if trades_file.exists():
                with open(trades_file, 'r') as f:
                    data['trades'] = json.load(f)

            # Load model performance
            model_file = terminal_dir / "model_performance.json"
            if model_file.exists():
                with open(model_file, 'r') as f:
                    data['models'] = json.load(f)

            # Load performance report
            report_file = terminal_dir / "performance_report.json"
            if report_file.exists():
                with open(report_file, 'r') as f:
                    data['report'] = json.load(f)

            return data

        except Exception as e:
            logger.error(f"Error loading terminal {terminal_id} data: {str(e)}")
            return {}

    def get_available_terminals(self) -> List[str]:
        """Get list of available terminals."""
        try:
            terminals = []
            for item in self.monitoring_dir.iterdir():
                if item.is_dir() and item.name.startswith("terminal_"):
                    terminal_id = item.name.replace("terminal_", "")
                    terminals.append(terminal_id)
            return sorted(terminals)
        except Exception as e:
            logger.error(f"Error getting available terminals: {str(e)}")
            return []

    def generate_performance_dashboard(self, terminal_ids: Optional[List[str]] = None) -> None:
        """Generate comprehensive performance dashboard."""
        try:
            if terminal_ids is None:
                terminal_ids = self.get_available_terminals()

            if not terminal_ids:
                logger.warning("No terminals found for monitoring")
                return

            # Load data for all terminals
            all_data = {}
            for terminal_id in terminal_ids:
                data = self.load_terminal_data(terminal_id)
                if data:
                    all_data[terminal_id] = data

            if not all_data:
                logger.warning("No performance data found")
                return

            # Create dashboard
            self._create_overview_dashboard(all_data)
            self._create_detailed_charts(all_data)

            logger.info(f"Performance dashboard generated for {len(all_data)} terminals")

        except Exception as e:
            logger.error(f"Error generating performance dashboard: {str(e)}")

    def _create_overview_dashboard(self, data: Dict) -> None:
        """Create overview dashboard with key metrics."""
        try:
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Total P&L by Terminal', 'Win Rate Comparison',
                               'Active Trades', 'Daily Performance'),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )

            terminals = list(data.keys())

            # Extract key metrics
            total_pnl = []
            win_rates = []
            active_trades = []

            for terminal_id in terminals:
                terminal_data = data[terminal_id]

                # Get latest report data
                if 'report' in terminal_data:
                    report = terminal_data['report']
                    total_pnl.append(report.get('total_profit', 0))
                    win_rates.append(report.get('win_rate', 0) * 100)  # Convert to percentage
                    active_trades.append(report.get('total_trades', 0))
                else:
                    total_pnl.append(0)
                    win_rates.append(0)
                    active_trades.append(0)

            # Total P&L by Terminal
            fig.add_trace(
                go.Bar(x=terminals, y=total_pnl, name='Total P&L',
                      marker_color='green' if all(p >= 0 for p in total_pnl) else 'red'),
                row=1, col=1
            )

            # Win Rate Comparison
            fig.add_trace(
                go.Bar(x=terminals, y=win_rates, name='Win Rate (%)', marker_color='blue'),
                row=1, col=2
            )

            # Active Trades
            fig.add_trace(
                go.Bar(x=terminals, y=active_trades, name='Total Trades', marker_color='orange'),
                row=2, col=1
            )

            # Daily Performance (placeholder - would need time series data)
            daily_returns = [0.5, -0.2, 1.1, 0.8, -0.3]  # Example data
            dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
            fig.add_trace(
                go.Scatter(x=dates, y=daily_returns, mode='lines+markers',
                          name='Daily Returns', line_color='purple'),
                row=2, col=2
            )

            # Update layout
            fig.update_layout(
                title_text="Trading Bot Performance Dashboard",
                showlegend=False,
                height=800
            )

            # Save dashboard
            dashboard_path = self.output_dir / 'performance_dashboard.html'
            fig.write_html(str(dashboard_path))

            # Also save as PNG
            png_path = self.output_dir / 'performance_dashboard.png'
            fig.write_image(str(png_path))

            logger.info(f"Overview dashboard saved to {dashboard_path}")

        except Exception as e:
            logger.error(f"Error creating overview dashboard: {str(e)}")

    def _create_detailed_charts(self, data: Dict) -> None:
        """Create detailed performance charts for each terminal."""
        try:
            for terminal_id, terminal_data in data.items():
                if 'trades' not in terminal_data or not terminal_data['trades']:
                    continue

                # Convert trades to DataFrame
                df = pd.DataFrame(terminal_data['trades'])
                df['timestamp'] = pd.to_datetime(df['timestamp'])

                # Create detailed chart for this terminal
                fig = make_subplots(
                    rows=2, cols=2,
                    subplot_titles=(f'Terminal {terminal_id} - Cumulative P&L',
                                   'Trade Distribution', 'Drawdown', 'Win Rate Over Time'),
                    specs=[[{"secondary_y": False}, {"secondary_y": False}],
                           [{"secondary_y": False}, {"secondary_y": False}]]
                )

                # Cumulative P&L
                df['cumulative_pnl'] = df['profit'].cumsum()
                fig.add_trace(
                    go.Scatter(x=df['timestamp'], y=df['cumulative_pnl'],
                              mode='lines', name='Cumulative P&L'),
                    row=1, col=1
                )

                # Trade Distribution
                fig.add_trace(
                    go.Histogram(x=df['profit'], nbinsx=30, name='Trade Distribution'),
                    row=1, col=2
                )

                # Drawdown calculation
                cumulative = df['cumulative_pnl'].values
                running_max = pd.Series(cumulative).expanding().max()
                drawdown = running_max - cumulative

                fig.add_trace(
                    go.Scatter(x=df['timestamp'], y=drawdown,
                              mode='lines', name='Drawdown', line_color='red'),
                    row=2, col=1
                )

                # Rolling win rate
                window_size = min(20, len(df))
                if window_size > 1:
                    rolling_wins = df['profit'].rolling(window=window_size).apply(
                        lambda x: (x > 0).mean() * 100
                    )
                    fig.add_trace(
                        go.Scatter(x=df['timestamp'], y=rolling_wins,
                                  mode='lines', name='Rolling Win Rate %'),
                        row=2, col=2
                    )

                # Update layout
                fig.update_layout(
                    title_text=f"Terminal {terminal_id} - Detailed Performance Analysis",
                    showlegend=False,
                    height=800
                )

                # Save terminal-specific chart
                terminal_chart_path = self.output_dir / f'terminal_{terminal_id}_detailed.html'
                fig.write_html(str(terminal_chart_path))

                logger.info(f"Detailed chart for terminal {terminal_id} saved to {terminal_chart_path}")

        except Exception as e:
            logger.error(f"Error creating detailed charts: {str(e)}")

    def monitor_realtime(self, interval: int = 30, terminal_ids: Optional[List[str]] = None) -> None:
        """Monitor performance in real-time."""
        logger.info(f"Starting real-time monitoring (interval: {interval}s)")

        try:
            while True:
                # Generate updated dashboard
                self.generate_performance_dashboard(terminal_ids)

                # Log current status
                if terminal_ids is None:
                    terminal_ids = self.get_available_terminals()

                logger.info(f"Dashboard updated for {len(terminal_ids)} terminals")

                # Wait for next update
                time.sleep(interval)

        except KeyboardInterrupt:
            logger.info("Real-time monitoring stopped by user")
        except Exception as e:
            logger.error(f"Error during real-time monitoring: {str(e)}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Monitor trading bot performance")
    parser.add_argument('--monitoring_dir', default='monitoring', help='Monitoring data directory')
    parser.add_argument('--terminal', help='Specific terminal to monitor')
    parser.add_argument('--realtime', action='store_true', help='Enable real-time monitoring')
    parser.add_argument('--interval', type=int, default=30, help='Update interval for real-time monitoring')
    parser.add_argument('--web', action='store_true', help='Start web dashboard (future feature)')
    parser.add_argument('--port', type=int, default=8080, help='Web dashboard port')

    args = parser.parse_args()

    # Initialize monitor
    monitor = BotPerformanceMonitor(args.monitoring_dir)

    # Determine terminals to monitor
    terminal_ids = [args.terminal] if args.terminal else None

    if args.realtime:
        # Real-time monitoring
        monitor.monitor_realtime(args.interval, terminal_ids)
    else:
        # Generate static dashboard
        monitor.generate_performance_dashboard(terminal_ids)
        logger.info("Static dashboard generation completed")

        # Open dashboard in browser
        dashboard_path = monitor.output_dir / 'performance_dashboard.html'
        if dashboard_path.exists():
            logger.info(f"Dashboard available at: {dashboard_path.absolute()}")
            logger.info("Open this file in your web browser to view the dashboard")

if __name__ == "__main__":
    main()
