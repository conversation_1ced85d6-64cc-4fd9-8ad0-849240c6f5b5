"""
Intelligent Cache System - Advanced caching system with tiered storage,
smart eviction policies, and memory-aware operation.

This module provides comprehensive caching capabilities including:
1. Tiered storage (memory, disk, compressed)
2. Advanced eviction policies (LRU, LFU, priority-based)
3. Time-to-live (TTL) management
4. Memory-aware caching
5. Cache statistics and monitoring
"""

import os
import time
import logging
import threading
import pickle
import hashlib
import zlib
from typing import Dict, Any, Optional, Union, List, Tuple, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import weakref
import json

# Configure logging
logger = logging.getLogger(__name__)

class CacheTier(Enum):
    """Cache storage tiers."""
    MEMORY = "memory"  # Fast access, limited capacity
    DISK = "disk"      # Slower access, larger capacity
    COMPRESSED = "compressed"  # Memory-efficient, slower access

class EvictionPolicy(Enum):
    """Cache eviction policies."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    PRIORITY = "priority"  # Based on item priority
    SIZE = "size"  # Based on item size

@dataclass
class CacheItem:
    """Represents a cached item with metadata."""
    key: str
    value: Any
    tier: CacheTier = CacheTier.MEMORY
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    size_bytes: int = 0
    ttl: Optional[int] = None  # Time-to-live in seconds
    priority: int = 0  # Higher priority = less likely to be evicted

    def is_expired(self) -> bool:
        """Check if the item has expired."""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl

    def access(self) -> None:
        """Update access metadata."""
        self.last_accessed = datetime.now()
        self.access_count += 1

    def get_age(self) -> float:
        """Get item age in seconds."""
        return (datetime.now() - self.created_at).total_seconds()

    def get_last_access_age(self) -> float:
        """Get time since last access in seconds."""
        return (datetime.now() - self.last_accessed).total_seconds()

class IntelligentCache:
    """
    Advanced caching system with tiered storage, smart eviction policies,
    and memory-aware operation.
    """

    def __init__(self,
                name: str = "default",
                memory_limit_mb: float = 100.0,
                disk_limit_mb: float = 1000.0,
                disk_cache_dir: Optional[str] = None,
                default_ttl: Optional[int] = None,
                eviction_policy: EvictionPolicy = EvictionPolicy.LRU,
                memory_manager = None):
        """
        Initialize the intelligent cache system.

        Args:
            name: Cache name for identification
            memory_limit_mb: Memory cache size limit in MB
            disk_limit_mb: Disk cache size limit in MB
            disk_cache_dir: Directory for disk cache (default: .cache/{name})
            default_ttl: Default TTL for cache items in seconds
            eviction_policy: Default eviction policy
            memory_manager: Optional memory manager for memory-aware operation
        """
        self.name = name
        self.memory_limit_bytes = memory_limit_mb * 1024 * 1024
        self.disk_limit_bytes = disk_limit_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.eviction_policy = eviction_policy
        self.memory_manager = memory_manager

        # Set up disk cache directory
        if disk_cache_dir:
            self.disk_cache_dir = Path(disk_cache_dir)
        else:
            self.disk_cache_dir = Path(".cache") / name
        self.disk_cache_dir.mkdir(parents=True, exist_ok=True)

        # Initialize cache storage
        self._memory_cache: Dict[str, CacheItem] = {}
        self._compressed_cache: Dict[str, bytes] = {}

        # Cache statistics
        self._stats = {
            "hits": 0,
            "misses": 0,
            "memory_hits": 0,
            "disk_hits": 0,
            "compressed_hits": 0,
            "evictions": 0,
            "expirations": 0,
            "memory_bytes": 0,
            "disk_bytes": 0,
            "compressed_bytes": 0
        }

        # Thread safety
        self._lock = threading.RLock()

        logger.info(f"Intelligent Cache '{name}' initialized with {memory_limit_mb}MB memory limit")

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get an item from the cache.

        Args:
            key: Cache key
            default: Default value if key not found

        Returns:
            Any: Cached value or default if not found
        """
        with self._lock:
            # Check if key exists in memory cache
            if key in self._memory_cache:
                item = self._memory_cache[key]

                # Check if item has expired
                if item.is_expired():
                    self._remove_item(key)
                    self._stats["expirations"] += 1
                    self._stats["misses"] += 1
                    return default

                # Update access metadata
                item.access()
                self._stats["hits"] += 1
                self._stats["memory_hits"] += 1
                return item.value

            # Check if key exists in compressed cache
            if key in self._compressed_cache:
                try:
                    # Decompress and deserialize
                    compressed_data = self._compressed_cache[key]
                    data = pickle.loads(zlib.decompress(compressed_data))

                    # Move to memory cache if possible
                    self._try_move_to_memory(key, data)

                    self._stats["hits"] += 1
                    self._stats["compressed_hits"] += 1
                    return data
                except Exception as e:
                    logger.error(f"Error retrieving compressed item '{key}': {str(e)}")
                    self._remove_item(key)
                    self._stats["misses"] += 1
                    return default

            # Check if key exists in disk cache
            disk_path = self._get_disk_path(key)
            if disk_path.exists():
                try:
                    # Read from disk
                    with open(disk_path, "rb") as f:
                        loaded_data = pickle.load(f)

                    # Handle different storage formats
                    if isinstance(loaded_data, tuple) and len(loaded_data) == 2:
                        # New format: (metadata, value)
                        metadata, data = loaded_data

                        # Check if item has expired
                        if metadata.get("ttl") is not None:
                            created_at = datetime.fromtimestamp(metadata.get("created_at", 0))
                            age_seconds = (datetime.now() - created_at).total_seconds()
                            if age_seconds > metadata.get("ttl"):
                                self._remove_item(key)
                                self._stats["expirations"] += 1
                                self._stats["misses"] += 1
                                return default
                    elif isinstance(loaded_data, CacheItem):
                        # Old format: CacheItem
                        if loaded_data.is_expired():
                            self._remove_item(key)
                            self._stats["expirations"] += 1
                            self._stats["misses"] += 1
                            return default
                        data = loaded_data.value
                    else:
                        # Direct value
                        data = loaded_data

                    # Move to memory cache if possible
                    self._try_move_to_memory(key, data)

                    self._stats["hits"] += 1
                    self._stats["disk_hits"] += 1
                    return data
                except Exception as e:
                    logger.error(f"Error retrieving disk item '{key}': {str(e)}")
                    self._remove_item(key)
                    self._stats["misses"] += 1
                    return default

            # Key not found in any cache tier
            self._stats["misses"] += 1
            return default

    def set(self, key: str, value: Any, ttl: Optional[int] = None,
           priority: int = 0, tier: Optional[CacheTier] = None) -> bool:
        """
        Set an item in the cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for no expiration)
            priority: Item priority (higher = less likely to be evicted)
            tier: Specific cache tier to use (None for automatic)

        Returns:
            bool: True if successful, False otherwise
        """
        with self._lock:
            # Use default TTL if not specified
            if ttl is None:
                ttl = self.default_ttl

            try:
                # Estimate item size
                size_bytes = self._estimate_size(value)

                # Determine appropriate tier if not specified
                if tier is None:
                    tier = self._determine_tier(size_bytes)

                # Store in appropriate tier
                if tier == CacheTier.MEMORY:
                    return self._store_in_memory(key, value, ttl, priority, size_bytes)
                elif tier == CacheTier.COMPRESSED:
                    return self._store_compressed(key, value, ttl, priority, size_bytes)
                elif tier == CacheTier.DISK:
                    return self._store_on_disk(key, value, ttl, priority, size_bytes)
                else:
                    logger.error(f"Unknown cache tier: {tier}")
                    return False
            except Exception as e:
                logger.error(f"Error setting cache item '{key}': {str(e)}")
                return False

    def remove(self, key: str) -> bool:
        """
        Remove an item from the cache.

        Args:
            key: Cache key

        Returns:
            bool: True if item was removed, False if not found
        """
        with self._lock:
            return self._remove_item(key)

    def clear(self, tier: Optional[CacheTier] = None) -> None:
        """
        Clear the cache.

        Args:
            tier: Specific tier to clear (None for all)
        """
        with self._lock:
            if tier is None or tier == CacheTier.MEMORY:
                self._memory_cache.clear()
                self._stats["memory_bytes"] = 0

            if tier is None or tier == CacheTier.COMPRESSED:
                self._compressed_cache.clear()
                self._stats["compressed_bytes"] = 0

            if tier is None or tier == CacheTier.DISK:
                # Remove all files in disk cache directory
                for file_path in self.disk_cache_dir.glob("*.cache"):
                    try:
                        file_path.unlink()
                    except Exception as e:
                        logger.error(f"Error removing disk cache file {file_path}: {str(e)}")
                self._stats["disk_bytes"] = 0

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dict[str, Any]: Cache statistics
        """
        with self._lock:
            # Calculate hit ratio
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_ratio = self._stats["hits"] / total_requests if total_requests > 0 else 0

            # Copy stats and add calculated values
            stats = self._stats.copy()
            stats["total_requests"] = total_requests
            stats["hit_ratio"] = hit_ratio
            stats["memory_items"] = len(self._memory_cache)
            stats["compressed_items"] = len(self._compressed_cache)
            stats["disk_items"] = len(list(self.disk_cache_dir.glob("*.cache")))
            stats["total_items"] = stats["memory_items"] + stats["compressed_items"] + stats["disk_items"]
            stats["memory_limit_bytes"] = self.memory_limit_bytes
            stats["disk_limit_bytes"] = self.disk_limit_bytes

            return stats

    def cleanup(self) -> int:
        """
        Clean up expired items.

        Returns:
            int: Number of items removed
        """
        with self._lock:
            count = 0

            # Clean memory cache
            expired_keys = [key for key, item in self._memory_cache.items() if item.is_expired()]
            for key in expired_keys:
                self._remove_item(key)
                count += 1

            # Clean disk cache
            for file_path in self.disk_cache_dir.glob("*.cache"):
                try:
                    # Check file modification time
                    mtime = file_path.stat().st_mtime
                    age = time.time() - mtime

                    # If file is older than 1 day, check if it's expired
                    if age > 86400:  # 24 hours
                        try:
                            with open(file_path, "rb") as f:
                                item = pickle.load(f)
                            if isinstance(item, CacheItem) and item.is_expired():
                                file_path.unlink()
                                count += 1
                        except Exception:
                            # If we can't read the file, remove it
                            file_path.unlink()
                            count += 1
                except Exception as e:
                    logger.error(f"Error cleaning up disk cache file {file_path}: {str(e)}")

            self._stats["expirations"] += count
            return count

    def _estimate_size(self, value: Any) -> int:
        """
        Estimate the size of a value in bytes.

        Args:
            value: Value to estimate size for

        Returns:
            int: Estimated size in bytes
        """
        try:
            # Serialize and measure
            serialized = pickle.dumps(value)
            return len(serialized)
        except Exception as e:
            logger.warning(f"Error estimating size: {str(e)}")
            return 1024  # Default to 1KB if estimation fails

    def _determine_tier(self, size_bytes: int) -> CacheTier:
        """
        Determine the appropriate cache tier for an item.

        Args:
            size_bytes: Size of the item in bytes

        Returns:
            CacheTier: Appropriate cache tier
        """
        # Small items go to memory
        if size_bytes < 1024 * 10:  # < 10KB
            return CacheTier.MEMORY

        # Medium items go to compressed memory
        if size_bytes < 1024 * 100:  # < 100KB
            return CacheTier.COMPRESSED

        # Large items go to disk
        return CacheTier.DISK

    def _store_in_memory(self, key: str, value: Any, ttl: Optional[int],
                        priority: int, size_bytes: int) -> bool:
        """
        Store an item in memory cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds
            priority: Item priority
            size_bytes: Size of the item in bytes

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if we need to make room
        if self._stats["memory_bytes"] + size_bytes > self.memory_limit_bytes:
            # Try to evict items
            freed_bytes = self._evict_items(CacheTier.MEMORY, size_bytes)

            # If we couldn't free enough space, store in a different tier
            if self._stats["memory_bytes"] + size_bytes - freed_bytes > self.memory_limit_bytes:
                if size_bytes < 1024 * 1024:  # < 1MB
                    return self._store_compressed(key, value, ttl, priority, size_bytes)
                else:
                    return self._store_on_disk(key, value, ttl, priority, size_bytes)

        # Check memory pressure if memory manager is available
        if self.memory_manager:
            memory_status = self.memory_manager.check_memory()
            if memory_status in ("HIGH", "CRITICAL"):
                # Under high memory pressure, store large items on disk
                if size_bytes > 10240:  # > 10KB
                    logger.info(f"Memory pressure {memory_status}, storing large item '{key}' on disk")
                    return self._store_on_disk(key, value, ttl, priority, size_bytes)
                # Under critical memory pressure, store medium items compressed
                elif memory_status == "CRITICAL" and size_bytes > 1024:  # > 1KB
                    logger.info(f"Memory pressure CRITICAL, storing medium item '{key}' compressed")
                    return self._store_compressed(key, value, ttl, priority, size_bytes)

        # Create cache item
        item = CacheItem(
            key=key,
            value=value,
            tier=CacheTier.MEMORY,
            ttl=ttl,
            priority=priority,
            size_bytes=size_bytes
        )

        # Store in memory cache
        self._memory_cache[key] = item
        self._stats["memory_bytes"] += size_bytes

        # Update memory manager if available
        if self.memory_manager:
            self.memory_manager.update_component_usage(f"cache_{self.name}", self._stats["memory_bytes"])

        return True

    def _store_compressed(self, key: str, value: Any, ttl: Optional[int],
                         priority: int, size_bytes: int) -> bool:
        """
        Store an item in compressed memory cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds
            priority: Item priority
            size_bytes: Size of the item in bytes

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Serialize and compress
            serialized = pickle.dumps(value)
            compressed = zlib.compress(serialized)
            compressed_size = len(compressed)

            # Check if compression is effective
            if compressed_size > size_bytes * 0.8:  # If compression saves less than 20%
                return self._store_on_disk(key, value, ttl, priority, size_bytes)

            # Check if we need to make room
            if self._stats["compressed_bytes"] + compressed_size > self.memory_limit_bytes / 2:
                # Try to evict items
                self._evict_items(CacheTier.COMPRESSED, compressed_size)

                # If we still don't have enough space, store on disk
                if self._stats["compressed_bytes"] + compressed_size > self.memory_limit_bytes / 2:
                    return self._store_on_disk(key, value, ttl, priority, size_bytes)

            # Store in compressed cache
            self._compressed_cache[key] = compressed
            self._stats["compressed_bytes"] += compressed_size

            # Update memory manager if available
            if self.memory_manager:
                self.memory_manager.update_component_usage(
                    f"cache_{self.name}_compressed",
                    self._stats["compressed_bytes"]
                )

            return True
        except Exception as e:
            logger.error(f"Error compressing item '{key}': {str(e)}")
            return self._store_on_disk(key, value, ttl, priority, size_bytes)

    def _store_on_disk(self, key: str, value: Any, ttl: Optional[int],
                      priority: int, size_bytes: int) -> bool:
        """
        Store an item in disk cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds
            priority: Item priority
            size_bytes: Size of the item in bytes

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if we need to make room
            if self._stats["disk_bytes"] + size_bytes > self.disk_limit_bytes:
                # Try to evict items
                self._evict_items(CacheTier.DISK, size_bytes)

            # Get disk path
            disk_path = self._get_disk_path(key)

            # Store metadata separately from value to avoid pickling issues
            metadata = {
                "key": key,
                "created_at": datetime.now().timestamp(),
                "ttl": ttl,
                "priority": priority,
                "size_bytes": size_bytes,
                "tier": CacheTier.DISK.value
            }

            # Write to disk
            with open(disk_path, "wb") as f:
                # Store metadata and value separately
                pickle.dump((metadata, value), f)

            # Update stats
            self._stats["disk_bytes"] += size_bytes

            # Update memory manager if available
            if self.memory_manager:
                self.memory_manager.update_component_usage(
                    f"cache_{self.name}_disk",
                    self._stats["disk_bytes"]
                )

            return True
        except Exception as e:
            logger.error(f"Error storing item '{key}' on disk: {str(e)}")
            return False

    def _get_disk_path(self, key: str) -> Path:
        """
        Get the disk path for a cache key.

        Args:
            key: Cache key

        Returns:
            Path: Path to the disk cache file
        """
        # Hash the key to create a filename
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.disk_cache_dir / f"{key_hash}.cache"

    def _remove_item(self, key: str) -> bool:
        """
        Remove an item from all cache tiers.

        Args:
            key: Cache key

        Returns:
            bool: True if item was removed, False if not found
        """
        removed = False

        # Remove from memory cache
        if key in self._memory_cache:
            item = self._memory_cache[key]
            self._stats["memory_bytes"] -= item.size_bytes
            del self._memory_cache[key]
            removed = True

        # Remove from compressed cache
        if key in self._compressed_cache:
            compressed_data = self._compressed_cache[key]
            self._stats["compressed_bytes"] -= len(compressed_data)
            del self._compressed_cache[key]
            removed = True

        # Remove from disk cache
        disk_path = self._get_disk_path(key)
        if disk_path.exists():
            try:
                # Get file size for stats
                file_size = disk_path.stat().st_size
                self._stats["disk_bytes"] -= file_size

                # Remove file
                disk_path.unlink()
                removed = True
            except Exception as e:
                logger.error(f"Error removing disk cache file {disk_path}: {str(e)}")

        return removed

    def _evict_items(self, tier: CacheTier, needed_bytes: int) -> int:
        """
        Evict items from a cache tier to free up space.

        Args:
            tier: Cache tier to evict from
            needed_bytes: Number of bytes needed

        Returns:
            int: Number of bytes freed
        """
        freed_bytes = 0

        if tier == CacheTier.MEMORY:
            # Get items sorted by eviction policy
            items = list(self._memory_cache.values())
            items = self._sort_by_eviction_policy(items)

            # Evict items until we have enough space
            for item in items:
                if freed_bytes >= needed_bytes:
                    break

                # Skip high-priority items if possible
                if item.priority > 5 and freed_bytes < needed_bytes / 2:
                    continue

                # Remove item
                self._remove_item(item.key)
                freed_bytes += item.size_bytes
                self._stats["evictions"] += 1

        elif tier == CacheTier.COMPRESSED:
            # Evict items from compressed cache
            keys = list(self._compressed_cache.keys())

            # Evict items until we have enough space
            for key in keys:
                if freed_bytes >= needed_bytes:
                    break

                # Remove item
                compressed_data = self._compressed_cache[key]
                freed_bytes += len(compressed_data)
                del self._compressed_cache[key]
                self._stats["evictions"] += 1
                self._stats["compressed_bytes"] -= len(compressed_data)

        elif tier == CacheTier.DISK:
            # Get disk cache files sorted by modification time
            files = list(self.disk_cache_dir.glob("*.cache"))
            files.sort(key=lambda f: f.stat().st_mtime)

            # Evict items until we have enough space
            for file_path in files:
                if freed_bytes >= needed_bytes:
                    break

                try:
                    # Get file size
                    file_size = file_path.stat().st_size

                    # Remove file
                    file_path.unlink()
                    freed_bytes += file_size
                    self._stats["evictions"] += 1
                    self._stats["disk_bytes"] -= file_size
                except Exception as e:
                    logger.error(f"Error evicting disk cache file {file_path}: {str(e)}")

        return freed_bytes

    def _sort_by_eviction_policy(self, items: List[CacheItem]) -> List[CacheItem]:
        """
        Sort items according to the eviction policy.

        Args:
            items: List of cache items

        Returns:
            List[CacheItem]: Sorted list of cache items
        """
        if self.eviction_policy == EvictionPolicy.LRU:
            # Sort by last access time (oldest first)
            return sorted(items, key=lambda item: item.last_accessed)

        elif self.eviction_policy == EvictionPolicy.LFU:
            # Sort by access count (least accessed first)
            return sorted(items, key=lambda item: item.access_count)

        elif self.eviction_policy == EvictionPolicy.PRIORITY:
            # Sort by priority (lowest first) and then by last access time
            return sorted(items, key=lambda item: (item.priority, item.last_accessed))

        elif self.eviction_policy == EvictionPolicy.SIZE:
            # Sort by size (largest first)
            return sorted(items, key=lambda item: -item.size_bytes)

        # Default to LRU
        return sorted(items, key=lambda item: item.last_accessed)

    def _try_move_to_memory(self, key: str, value: Any) -> bool:
        """
        Try to move an item to memory cache.

        Args:
            key: Cache key
            value: Value to cache

        Returns:
            bool: True if moved to memory, False otherwise
        """
        try:
            # Estimate size
            size_bytes = self._estimate_size(value)

            # Check if it's small enough for memory cache
            if size_bytes < 1024 * 10:  # < 10KB
                # Check if we have enough space
                if self._stats["memory_bytes"] + size_bytes <= self.memory_limit_bytes:
                    # Create cache item
                    item = CacheItem(
                        key=key,
                        value=value,
                        tier=CacheTier.MEMORY,
                        size_bytes=size_bytes
                    )

                    # Store in memory cache
                    self._memory_cache[key] = item
                    self._stats["memory_bytes"] += size_bytes

                    return True

            return False
        except Exception as e:
            logger.error(f"Error moving item '{key}' to memory: {str(e)}")
            return False

# Import memory manager
try:
    from utils.enhanced_memory_manager import enhanced_memory_manager
    memory_manager_available = True
except ImportError:
    memory_manager_available = False
    logger.warning("Enhanced memory manager not available, cache will operate without memory awareness")

# Create global instance with memory manager integration if available
if memory_manager_available:
    intelligent_cache = IntelligentCache(name="global", memory_manager=enhanced_memory_manager)

    # Register cache as a component with the memory manager
    enhanced_memory_manager.register_component(
        "intelligent_cache",
        cleanup_handlers={
            "light": lambda component_name: intelligent_cache.cleanup() or 0,  # Return 0 if cleanup returns None
            "moderate": lambda component_name: (intelligent_cache.clear(CacheTier.MEMORY), 0)[1],  # Return 0
            "aggressive": lambda component_name: (intelligent_cache.clear(), 0)[1]  # Return 0
        }
    )
else:
    intelligent_cache = IntelligentCache(name="global")

# Export convenience functions
def get_cache(name="global", memory_limit_mb=None):
    """Get a named cache instance."""
    if name == "global":
        return intelligent_cache

    # Create a new cache with memory manager integration if available
    if memory_manager_available:
        # If memory_limit_mb is not specified, calculate based on system memory
        if memory_limit_mb is None:
            system_memory_mb = enhanced_memory_manager.get_memory_stats()["system"]["total_bytes"] / (1024 * 1024)
            memory_limit_mb = max(10, system_memory_mb * 0.1)  # 10% of system memory, minimum 10MB

        cache = IntelligentCache(name=name, memory_limit_mb=memory_limit_mb, memory_manager=enhanced_memory_manager)

        # Register with memory manager
        enhanced_memory_manager.register_component(
            f"cache_{name}",
            cleanup_handlers={
                "light": lambda component_name: cache.cleanup() or 0,  # Return 0 if cleanup returns None
                "moderate": lambda component_name: (cache.clear(CacheTier.MEMORY), 0)[1],  # Return 0
                "aggressive": lambda component_name: (cache.clear(), 0)[1]  # Return 0
            }
        )

        return cache
    else:
        return IntelligentCache(name=name, memory_limit_mb=memory_limit_mb or 100.0)

get_global_cache = lambda: intelligent_cache
cache_get = lambda key, default=None: intelligent_cache.get(key, default)
cache_set = lambda key, value, **kwargs: intelligent_cache.set(key, value, **kwargs)
cache_remove = lambda key: intelligent_cache.remove(key)
cache_clear = lambda: intelligent_cache.clear()
get_cache_stats = lambda: intelligent_cache.get_stats()
