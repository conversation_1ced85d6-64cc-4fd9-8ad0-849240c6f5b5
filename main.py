"""
Main module for the trading bot.
Handles initialization and execution of trading bots.
"""
import sys
import signal
import logging
import time
import threading
from typing import Dict, Optional
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/main.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Create logs directory
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Import configuration manager
from config import ConfigurationManager, config_manager

# Get main configuration
trading_config = config_manager.get_config()

# Initialize utility managers first
from utils.enhanced_error_handler import EnhancedErrorHandler
from utils.thread_manager import ThreadManager
error_handler = EnhancedError<PERSON>andler()

# Initialize thread manager
thread_manager = ThreadManager(max_workers=16, thread_name_prefix="Main")

from utils.enhanced_memory_manager import enhanced_memory_manager
# Set unified memory thresholds across all components
memory_thresholds = {
    "WARNING": trading_config.max_memory_usage - 15.0,  # 75% if max is 90%
    "HIGH": trading_config.max_memory_usage - 5.0,      # 85% if max is 90%
    "CRITICAL": trading_config.max_memory_usage         # 90% if max is 90%
}

# Initialize enhanced memory manager with unified thresholds
enhanced_memory_manager.base_thresholds = memory_thresholds
enhanced_memory_manager.thresholds = enhanced_memory_manager._calculate_adaptive_thresholds()
enhanced_memory_manager.monitor_interval = 60.0

# Use the global instance as memory_manager for backward compatibility
memory_manager = enhanced_memory_manager

# Log memory thresholds
logger.info(f"Unified memory thresholds set: WARNING={memory_thresholds['WARNING']}%, "
           f"HIGH={memory_thresholds['HIGH']}%, CRITICAL={memory_thresholds['CRITICAL']}%")

from utils.thread_manager import ThreadManager
thread_manager = ThreadManager(
    max_workers=32, # Consider making this configurable
    thread_name_prefix="Main"
)

# ModelManager needs to be instantiated per context (terminal/timeframe)
# It will be created within TradingBotManager or TradingBot

from trading.mt5_connection_manager import MT5ConnectionManager
mt5_manager = MT5ConnectionManager(config_manager)

# Register standard circuit breakers
from utils.enhanced_circuit_breaker import register_standard_circuit_breakers
standard_circuit_breakers = register_standard_circuit_breakers(memory_manager=enhanced_memory_manager)
logger.info(f"Registered {len(standard_circuit_breakers)} standard circuit breakers")

# Function to register all caches with memory manager
def register_caches_with_memory_manager(memory_manager):
    """Register all caches with the memory manager for consistent cleanup."""
    registered_components = []

    # Try to register intelligent cache (if not already registered)
    try:
        from utils.intelligent_cache import intelligent_cache
        # Check if already registered
        if not memory_manager.is_component_registered("intelligent_cache"):
            memory_manager.register_component(
                "intelligent_cache",
                cleanup_handlers={
                    "light": lambda _: intelligent_cache.cleanup() or 0,
                    "moderate": lambda _: (intelligent_cache.clear(tier="memory"), 0)[1],
                    "aggressive": lambda _: (intelligent_cache.clear(), 0)[1]
                }
            )
            registered_components.append("intelligent_cache")
        else:
            logger.debug("Intelligent cache already registered with memory manager")
    except (ImportError, AttributeError):
        logger.debug("Intelligent cache not available for registration")

    # Register data processor cache when it's created
    # This will be done when the data processor is instantiated

    logger.info(f"Registered {len(registered_components)} cache components with memory manager")
    return registered_components

# Register caches with memory manager
registered_caches = register_caches_with_memory_manager(enhanced_memory_manager)

# Now import the rest of the modules
from monitoring.progress import ProgressVisualizer
from trading.bot import TradingBot
from monitoring.performance import PerformanceMonitor
from config.credentials import MT5_TERMINALS

class TradingBotManager:
    """
    Manager class for handling multiple trading bots.
    Coordinates initialization, updates, and shutdown of bots.
    """

    def __init__(self, visualizer: Optional[ProgressVisualizer] = None, config_manager: ConfigurationManager = None, error_handler: EnhancedErrorHandler = None, mt5_manager: MT5ConnectionManager = None, thread_manager: ThreadManager = None, memory_manager = None):
        """
        Initialize the trading bot manager.

        Args:
            visualizer: Optional progress visualizer
            config_manager: Configuration manager
            error_handler: Error handler
            mt5_manager: MT5 connection manager
            thread_manager: Thread manager
        """
        self.bots: Dict[str, TradingBot] = {}
        self.monitors: Dict[str, PerformanceMonitor] = {}
        self.running = False
        self.visualizer = visualizer or ProgressVisualizer()
        self._lock = threading.RLock()
        self.config_manager = config_manager or ConfigurationManager()
        self.error_handler = error_handler or EnhancedErrorHandler()
        self.mt5_manager = mt5_manager or MT5ConnectionManager(self.config_manager)
        self.thread_manager = thread_manager or ThreadManager(
            max_workers=32,
            thread_name_prefix="Main"
        )
        self.memory_manager = memory_manager or enhanced_memory_manager

    def _validate_terminal_config(self, terminal_id_str: str) -> bool:
        """
        Validate terminal configuration exists.

        Args:
            terminal_id_str: Terminal ID as string (e.g., "1")

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Get specific terminal configuration
            terminal_config = self.config_manager.get_mt5_config().terminals.get(terminal_id_str)
            if not terminal_config:
                logger.error(f"Terminal configuration for ID '{terminal_id_str}' not found.")
                return False
            return True

        except Exception as e:
            self.error_handler.handle_error(e, context={"function": "_validate_terminal_config", "terminal_id": terminal_id_str})
            return False

    def _check_algo_trading_status(self) -> None:
        """Check and report algorithmic trading status across all terminals."""
        try:
            logger.info("[INFO] Checking algorithmic trading status across all terminals...")

            enabled_terminals = []
            disabled_terminals = []
            unknown_terminals = []

            # Check all active connections
            for terminal_id in self.bots.keys():
                try:
                    connection = self.mt5_manager.get_connection(terminal_id)
                    if connection:
                        # For validated-only connections, assume algo trading is preserved
                        is_validated_only = hasattr(connection, 'validated_only') and connection.validated_only

                        if hasattr(connection, 'algo_trading_disabled'):
                            if connection.algo_trading_disabled:
                                disabled_terminals.append(terminal_id)
                            else:
                                enabled_terminals.append(terminal_id)
                        elif is_validated_only:
                            # Validated-only connections preserve algo trading status
                            enabled_terminals.append(terminal_id)
                        else:
                            unknown_terminals.append(terminal_id)
                    else:
                        unknown_terminals.append(terminal_id)
                except Exception as e:
                    logger.debug(f"Error checking algo trading status for terminal {terminal_id}: {str(e)}")
                    unknown_terminals.append(terminal_id)

            # Report status
            total_terminals = len(enabled_terminals) + len(disabled_terminals) + len(unknown_terminals)

            if enabled_terminals:
                logger.info(f"[OK] Algorithmic trading ENABLED on {len(enabled_terminals)} terminals: {enabled_terminals}")

            if disabled_terminals:
                logger.error(f"[ERROR] Algorithmic trading DISABLED on {len(disabled_terminals)} terminals: {disabled_terminals}")
                logger.error("[URGENT] These terminals will NOT be able to execute trades!")
                logger.error("[ACTION] Run 'python setup_terminals.py' to enable algorithmic trading")

            if unknown_terminals:
                logger.warning(f"[WARNING] Algorithmic trading status UNKNOWN for {len(unknown_terminals)} terminals: {unknown_terminals}")

            # Overall status
            if len(enabled_terminals) == total_terminals:
                logger.info("[SUCCESS] ALL TERMINALS READY: Algorithmic trading enabled on all terminals!")
            elif len(disabled_terminals) > 0:
                logger.error("[WARNING] SYSTEM NOT READY: Some terminals have algorithmic trading disabled!")
                logger.error("[ACTION] Please enable algorithmic trading on all terminals before trading")

        except Exception as e:
            logger.error(f"Error checking algorithmic trading status: {str(e)}")
            self.error_handler.handle_error(e, context={"function": "_check_algo_trading_status"})

    def initialize_bot(self, terminal_id_str: str) -> bool:
        """
        Initialize a trading bot for a specific terminal.

        Args:
            terminal_id_str: Terminal ID as string (e.g., "1")

        Returns:
            bool: True if initialization successful, False otherwise
        """
        with self._lock:
            try:
                # Validate terminal configuration
                if not self._validate_terminal_config(terminal_id_str):
                    return False

                # Check MT5 connection first
                logger.info(f"Checking connection for terminal {terminal_id_str}")
                connection = self.mt5_manager.get_connection(terminal_id_str)
                if not connection:
                    logger.error(f"[ERROR] Failed to get connection object for terminal {terminal_id_str}")
                    logger.error(f"Possible causes: Invalid credentials, expired demo account, or terminal not running")
                    logger.error(f"This terminal will be skipped - the system will continue with other available terminals")
                    return False

                # Check connection status (including validated-only connections)
                is_validated_only = hasattr(connection, 'validated_only') and connection.validated_only
                if not connection.is_connected and not is_validated_only:
                    logger.error(f"[ERROR] Failed to connect to MT5 for terminal {terminal_id_str}")
                    logger.error(f"Possible causes: Invalid credentials, expired demo account, or terminal not running")
                    logger.error(f"This terminal will be skipped - the system will continue with other available terminals")
                    return False

                # Log connection type
                if is_validated_only:
                    logger.info(f"[OK] Terminal {terminal_id_str} validated (preserving active connection)")
                else:
                    logger.info(f"[OK] Terminal {terminal_id_str} actively connected")

                # Check if algorithmic trading is disabled
                if hasattr(connection, 'algo_trading_disabled') and connection.algo_trading_disabled:
                    logger.error(f"[CRITICAL] Terminal {terminal_id_str} has algorithmic trading DISABLED")
                    logger.error(f"Trading operations will FAIL until this is resolved!")
                    logger.error(f"Run 'python setup_terminals.py' for detailed setup instructions")
                    return False

                # Create bot instance
                logger.info(f"Creating trading bot for terminal {terminal_id_str}")
                self.bots[terminal_id_str] = TradingBot(
                    config_manager=self.config_manager,
                    terminal_id=terminal_id_str,
                    error_handler=self.error_handler,
                    thread_manager=self.thread_manager,
                    mt5_manager=self.mt5_manager,
                    visualizer=self.visualizer,
                    memory_manager=self.memory_manager
                )

                # Create performance monitor
                logger.info(f"Creating performance monitor for terminal {terminal_id_str}")
                self.monitors[terminal_id_str] = PerformanceMonitor(
                    terminal_id=int(terminal_id_str)
                )

                logger.info(f"Initialized trading bot for terminal {terminal_id_str}")
                return True

            except Exception as e:
                self.error_handler.handle_error(e, context={"function": "initialize_bot", "terminal_id": terminal_id_str})
                logger.error(f"Error initializing bot for terminal {terminal_id_str}: {str(e)}")
                return False

    def start_all_bots(self) -> bool:
        """
        Start all trading bots.

        Returns:
            bool: True if all bots started successfully, False otherwise
        """
        with self._lock:
            try:
                # Get available terminals
                terminal_ids = list(MT5_TERMINALS.keys())

                logger.info(f"Starting bots for terminals: {terminal_ids}")

                successful_bots = 0
                failed_bots = 0

                for terminal_id in terminal_ids:
                    # Ensure terminal_id is a string for consistency
                    term_id_str = str(terminal_id).replace("terminal", "")

                    if term_id_str not in self.bots:
                        success = self.initialize_bot(term_id_str)
                        if not success:
                            logger.warning(f"Failed to initialize bot for terminal {term_id_str}. Skipping this terminal.")
                            failed_bots += 1
                            continue

                    # Start the bot
                    logger.info(f"Starting bot for terminal {term_id_str}")
                    try:
                        # The run method starts the bot in a separate thread
                        self.bots[term_id_str].run(interval_seconds=60)  # Run with 60-second intervals
                        logger.info(f"Successfully started bot for terminal {term_id_str}")
                        successful_bots += 1
                    except Exception as e:
                        logger.error(f"Failed to start bot for terminal {term_id_str}: {str(e)}")
                        failed_bots += 1
                        # Remove the failed bot from the dictionary
                        if term_id_str in self.bots:
                            del self.bots[term_id_str]
                        continue

                    # Initialize the monitor (no start method needed)
                    logger.info(f"Monitor initialized for terminal {term_id_str}")

                # Set running status if at least one bot started successfully
                if successful_bots > 0:
                    self.running = True
                    logger.info(f"Started {successful_bots} trading bots successfully. {failed_bots} terminals failed.")

                    # Check algorithmic trading status across all terminals
                    self._check_algo_trading_status()

                    return True
                else:
                    logger.error(f"Failed to start any trading bots. All {failed_bots} terminals failed.")
                    logger.error("[CRITICAL] No trading bots are running!")
                    logger.error("[ACTION] Run 'python setup_terminals.py' to diagnose and fix terminal issues")
                    return False

            except Exception as e:
                self.error_handler.handle_error(e, context={"function": "start_all_bots"})
                logger.error(f"Error starting all bots: {str(e)}")
                return False

    def stop_all_bots(self) -> bool:
        """
        Stop all trading bots.

        Returns:
            bool: True if all bots stopped successfully, False otherwise
        """
        with self._lock:
            try:
                logger.info("Stopping all trading bots...")

                for terminal_id, bot in self.bots.items():
                    logger.info(f"Stopping bot for terminal {terminal_id}")
                    bot.stop()

                    if terminal_id in self.monitors:
                        logger.info(f"Cleaning up monitor for terminal {terminal_id}")
                        # PerformanceMonitor doesn't need explicit stop method

                self.running = False
                logger.info("Stopped all trading bots")

                # Disconnect from MT5 (preserve algorithmic trading)
                logger.info("Disconnecting from MT5 terminals (preserving algorithmic trading)")
                self.mt5_manager.disconnect_all()

                return True

            except Exception as e:
                self.error_handler.handle_error(e, context={"function": "stop_all_bots"})
                logger.error(f"Error stopping all bots: {str(e)}")
                return False

    def update(self) -> None:
        """
        Update all trading bots.
        """
        try:
            if not self.running:
                return

            for terminal_id, bot in self.bots.items():
                try:
                    bot.update()
                except Exception as e:
                    self.error_handler.handle_error(
                        e,
                        context={
                            "function": "update",
                            "terminal_id": terminal_id
                        }
                    )

            # Update visualizer
            if self.visualizer:
                try:
                    # Collect data from all bots for visualization
                    bot_data = {}
                    for terminal_id, bot in self.bots.items():
                        if hasattr(bot, 'performance_metrics'):
                            bot_data[terminal_id] = bot.performance_metrics

                    # Update visualizer
                    self.visualizer.update(bot_data)
                except Exception as e:
                    self.error_handler.handle_error(e, context={"function": "update_visualizer"})

        except Exception as e:
            self.error_handler.handle_error(e, context={"function": "update"})

    def shutdown(self) -> None:
        """
        Clean shutdown of the manager and all bots.
        """
        try:
            logger.info("Shutting down trading bot manager...")

            # Stop all bots
            self.stop_all_bots()

            # Clear resources
            self.bots.clear()
            self.monitors.clear()

            # Perform global cleanup
            enhanced_memory_manager.stop_monitoring()
            enhanced_memory_manager.cleanup("aggressive")
            self.thread_manager.shutdown(wait=True)

            logger.info("Trading bot manager shutdown complete")

        except Exception as e:
            self.error_handler.handle_error(e, context={"function": "shutdown"})
            logger.error(f"Error during shutdown: {str(e)}")

def setup_signal_handlers(manager: TradingBotManager) -> None:
    """
    Set up signal handlers for graceful shutdown.

    Args:
        manager: Trading bot manager
    """
    # Define a generic handler that ignores the signal parameters
    def create_signal_handler(signal_name):
        # This creates a closure over signal_name
        # The sig and frame parameters are required by the signal API but not used
        def handler(*_):
            logger.info(f"Received {signal_name}, shutting down...")
            manager.shutdown()
            sys.exit(0)
        return handler

    # Register signal handlers
    signal.signal(signal.SIGINT, create_signal_handler("SIGINT"))
    signal.signal(signal.SIGTERM, create_signal_handler("SIGTERM"))

def main() -> int:
    """Main function to initialize and run the trading bot manager."""
    logger.info("Starting Trading Bot System...")

    # Initialize configuration (already done globally)
    config = config_manager

    # Initialize managers (already done globally)

    # Initialize the main bot manager
    visualizer = ProgressVisualizer() # Optional
    manager = TradingBotManager(visualizer=visualizer,
                              config_manager=config, # Pass config
                              error_handler=error_handler,
                              mt5_manager=mt5_manager,
                              thread_manager=thread_manager,
                              memory_manager=enhanced_memory_manager)

    # Setup signal handlers for graceful shutdown
    setup_signal_handlers(manager)

    # Start all trading bots
    if not manager.start_all_bots():
        logger.error("Failed to start trading bots")
        return 1

    # Main loop
    try:
        logger.info("Entering main loop...")
        while True:
            # Update trading bots
            manager.update()

            # Sleep to prevent CPU overuse
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")

    # Clean shutdown
    manager.shutdown()
    logger.info("Trading bot application exited cleanly")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)